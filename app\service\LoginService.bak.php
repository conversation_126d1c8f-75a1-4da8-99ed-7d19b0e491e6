<?php
declare (strict_types = 1);

namespace app\service;

use app\model\User;
use think\facade\Session;
use think\facade\Db;
use think\facade\Request;
use app\service\LogService;

class LoginService extends BaseService
{
    /**
     * 用户登录
     */
    public function login($username, $password)
    {
        // 开始登录流程
        die('Login Process Started');
        trace('=== Login Process Started ===', 'debug');
        trace('Attempting login for username: ' . $username, 'debug');

        try {
            // 1. 查找用户
            $user = User::where('username', $username)->find();
            trace('User query result: ' . json_encode($user), 'debug');
            
            // 2. 验证用户存在
            if (!$user) {
                trace('User not found, calling LogService::recordLogin', 'debug');
                $logResult = LogService::recordLogin(0, $username, 0, '用户不存在');
                trace('LogService::recordLogin result: ' . var_export($logResult, true), 'debug');
                return $this->error('用户不存在');
            }

            // 3. 验证密码
            if (!password_verify($password, $user->password)) {
                trace('Password verification failed, calling LogService::recordLogin', 'debug');
                $logResult = LogService::recordLogin($user->id, $username, 0, '密码错误');
                trace('LogService::recordLogin result: ' . var_export($logResult, true), 'debug');
                return $this->error('密码错误');
            }

            // 4. 检查用户状态
            if ($user->status != 1) {
                trace('Account disabled, calling LogService::recordLogin', 'debug');
                $logResult = LogService::recordLogin($user->id, $username, 0, '账号已停用');
                trace('LogService::recordLogin result: ' . var_export($logResult, true), 'debug');
                return $this->error('账号已停用');
            }

            // 5. 设置会话
            Session::set('user_id', $user->id);
            Session::set('username', $user->username);
            Session::set('role_id', $user->role_id);

            // 6. 记录成功登录
            trace('Login successful, calling LogService::recordLogin', 'debug');
            $logResult = LogService::recordLogin($user->id, $username, 1, '登录成功');
            trace('LogService::recordLogin result: ' . var_export($logResult, true), 'debug');

            // 7. 返回用户数据
            $userData = $user->toArray();
            unset($userData['password']);
            
            trace('=== Login Process Completed Successfully ===', 'debug');
            return $this->success($userData);
        } catch (\Exception $e) {
            trace('Login process error: ' . $e->getMessage(), 'error');
            trace('Error trace: ' . $e->getTraceAsString(), 'error');
            
            $logResult = LogService::recordLogin(0, $username, 0, '登录异常：' . $e->getMessage());
            trace('LogService::recordLogin result: ' . var_export($logResult, true), 'debug');
            
            return $this->error('登录失败：' . $e->getMessage());
        }
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        try {
            $userId = Session::get('user_id');
            $username = Session::get('username');

            if ($userId && $username) {
                $this->writeLoginLog($userId, $username, 1, '用户登出');
            }

            Session::clear();
            Session::destroy();

            return $this->success([], '登出成功');
        } catch (\Exception $e) {
            trace('Logout error: ' . $e->getMessage(), 'error');
            return $this->error('登出失败：' . $e->getMessage());
        }
    }
} 