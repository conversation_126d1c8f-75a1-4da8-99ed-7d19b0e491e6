<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Queue;

class QueueWorker extends Command
{
    protected function configure()
    {
        $this->setName('queue:worker')
            ->addOption('queue', null, Option::VALUE_OPTIONAL, '队列名称', 'default')
            ->addOption('delay', null, Option::VALUE_OPTIONAL, '失败后延迟秒数', 0)
            ->addOption('memory', null, Option::VALUE_OPTIONAL, '内存限制，单位MB', 128)
            ->addOption('sleep', null, Option::VALUE_OPTIONAL, '队列为空时休眠秒数', 3)
            ->addOption('tries', null, Option::VALUE_OPTIONAL, '最大尝试次数', 0)
            ->setDescription('启动队列处理进程');
    }

    protected function execute(Input $input, Output $output)
    {
        $queue = $input->getOption('queue');
        $delay = $input->getOption('delay');
        $memory = $input->getOption('memory');
        $sleep = $input->getOption('sleep');
        $tries = $input->getOption('tries');

        $output->writeln('启动队列处理进程');
        $output->writeln("队列: {$queue}, 内存限制: {$memory}MB, 休眠: {$sleep}秒");

        // 创建队列连接
        $connection = Queue::connection();

        // 记录开始时间
        $startTime = time();

        while (true) {
            // 检查内存使用情况
            if ($this->memoryExceeded($memory)) {
                $output->writeln('<error>内存超出限制，重启进程</error>');
                break;
            }

            try {
                // 获取一个任务
                $job = $connection->pop($queue);

                if ($job) {
                    $output->writeln('<info>处理任务: ' . get_class($job->getJob()) . '</info>');
                    $startJob = microtime(true);

                    // 处理任务
                    $job->fire();

                    $time = round((microtime(true) - $startJob) * 1000, 2);
                    $output->writeln("<info>任务处理完成，耗时: {$time}ms</info>");
                } else {
                    // 没有任务时休眠
                    if ($sleep > 0) {
                        $output->writeln("<comment>队列为空，休眠 {$sleep} 秒...</comment>");
                        sleep($sleep);
                    }
                }
            } catch (\Throwable $e) {
                $output->writeln('<error>处理任务异常: ' . $e->getMessage() . '</error>');
                sleep(1);
            }
        }
    }

    /**
     * 检查是否超出内存限制
     *
     * @param  int  $memoryLimit
     * @return bool
     */
    protected function memoryExceeded($memoryLimit)
    {
        return (memory_get_usage() / 1024 / 1024) >= $memoryLimit;
    }
} 