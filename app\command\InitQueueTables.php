<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class InitQueueTables extends Command
{
    protected function configure()
    {
        $this->setName('queue:init')
            ->setDescription('初始化队列相关数据表');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始初始化队列相关数据表...');
        
        // 创建jobs表
        $this->createJobsTable($output);
        
        // 创建failed_jobs表
        $this->createFailedJobsTable($output);
        
        $output->writeln('<info>队列相关数据表初始化完成</info>');
    }
    
    /**
     * 创建jobs表
     */
    protected function createJobsTable(Output $output)
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `jobs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL DEFAULT 'default' COMMENT '队列名称',
  `payload` longtext NOT NULL COMMENT '任务载荷数据',
  `attempts` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '尝试次数',
  `reserved_at` int(11) unsigned DEFAULT NULL COMMENT '预留时间',
  `available_at` int(11) unsigned NOT NULL COMMENT '可执行时间',
  `created_at` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `queue_reserved_at` (`queue`,`reserved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='队列任务表';
SQL;

        try {
            Db::execute($sql);
            $output->writeln('<info>jobs表创建成功</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>jobs表创建失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 创建failed_jobs表
     */
    protected function createFailedJobsTable(Output $output)
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `failed_jobs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `connection` varchar(255) NOT NULL COMMENT '连接名称',
  `queue` varchar(255) NOT NULL COMMENT '队列名称',
  `payload` longtext NOT NULL COMMENT '任务载荷数据',
  `exception` longtext NOT NULL COMMENT '异常信息',
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失败时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='失败的队列任务表';
SQL;

        try {
            Db::execute($sql);
            $output->writeln('<info>failed_jobs表创建成功</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>failed_jobs表创建失败: ' . $e->getMessage() . '</error>');
        }
    }
} 