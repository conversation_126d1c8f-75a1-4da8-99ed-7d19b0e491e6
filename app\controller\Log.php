<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\LogService;
use think\facade\Request;

class Log extends BaseController
{
    protected $logService;

    public function initialize()
    {
        parent::initialize();
        $this->logService = new LogService();
    }

    /**
     * 获取登录日志列表
     */
    public function loginLogs()
    {
        $params = Request::get();
        return json($this->logService->getLoginLogs($params));
    }

    /**
     * 获取操作日志列表
     */
    public function operationLogs()
    {
        $params = Request::get();
        return json($this->logService->getOperationLogs($params));
    }
} 