/**
 * Copyright (c) 2018-present, Ephox, Inc.
 *
 * This source code is licensed under the Apache 2 license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { IPropTypes } from './EditorPropTypes';
export declare const Editor: import("vue").DefineComponent<import("./EditorPropTypes").CopyProps<IPropTypes>, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<import("./EditorPropTypes").CopyProps<IPropTypes>>>, {
    init: any;
    apiKey: any;
    licenseKey: any;
    cloudChannel: any;
    id: any;
    initialValue: any;
    outputFormat: any;
    inline: any;
    modelEvents: any;
    plugins: any;
    tagName: any;
    toolbar: any;
    modelValue: any;
    disabled: any;
    tinymceScriptSrc: any;
}, {}>;
