<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;
use think\facade\Db;

class User extends Model
{
    // 用户状态常量
    const STATUS_ENABLED = 1;   // 启用
    const STATUS_DISABLED = 0;  // 禁用
    
    // 设置表名为 sp_user，不要让框架再添加前缀
    protected $name = 'user';
    // 设置表前缀
    protected $prefix = 'sp_';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    // 创建时间字段
    protected $createTime = 'create_at';
    // 更新时间字段
    protected $updateTime = 'update_at';
    
    // 隐藏字段
    protected $hidden = ['password'];
    
    // 关联部门
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    // 关联角色
    public function roles()
    {
        // 如果role_ids为空，返回空集合
        if (empty($this->getAttr('role_ids'))) {
            return Role::where('id', 0)->select(); // 返回空集合
        }
        
        // 从role_ids字段获取角色ID数组
        $roleIds = array_map('intval', explode(',', $this->getAttr('role_ids')));
        
        // 返回对应的角色集合
        return Role::whereIn('id', $roleIds)->select();
    }
    
    // 设置用户的角色
    public function setRoles($roleIds)
    {
        if (is_array($roleIds)) {
            // 过滤并去除重复值
            $roleIds = array_unique(array_filter($roleIds, function($id) {
                return is_numeric($id) && $id > 0;
            }));
            
            // 转为字符串保存
            $this->setAttr('role_ids', implode(',', $roleIds));
            return $this->save();
        }
        return false;
    }

    // 检查用户是否有指定角色
    public function hasRole($roleId)
    {
        if (empty($this->getAttr('role_ids'))) {
            return false;
        }
        
        $roleIds = explode(',', $this->getAttr('role_ids'));
        return in_array($roleId, $roleIds);
    }

    // 检查用户是否有指定权限
    public function hasPermission($permissionId)
    {
        if (empty($this->getAttr('role_ids'))) {
            return false;
        }
        
        $roleIds = explode(',', $this->getAttr('role_ids'));
        
        // 查询这些角色是否有指定权限
        $hasPermission = Db::name('role_permission')
            ->whereIn('role_id', $roleIds)
            ->where('permission_id', $permissionId)
            ->find();
            
        return $hasPermission ? true : false;
    }

    // 修改器：密码加密
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    // 获取器：确保返回真实姓名
    public function getRealnameAttr($value)
    {
        return $value ?: $this->getAttr('username');
    }
} 