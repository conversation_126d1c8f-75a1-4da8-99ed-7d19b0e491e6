<?php
namespace command;

// 加载框架引导文件
require __DIR__ . '/../vendor/autoload.php';

// 初始化应用
$app = new \think\App();
$app->initialize();

// 数据库操作
use think\facade\Db;

try {
    // 创建超级管理员角色
    $roleId = Db::name('role')->insertGetId([
        'name' => '超级管理员',
        'code' => 'super_admin',
        'description' => '系统超级管理员',
        'status' => 1,
        'create_at' => date('Y-m-d H:i:s')
    ]);

    // 创建系统管理部门
    $deptId = Db::name('department')->insertGetId([
        'name' => '系统管理部',
        'code' => 'system',
        'parent_id' => 0,
        'sort' => 0,
        'status' => 1,
        'create_at' => date('Y-m-d H:i:s')
    ]);

    // 创建管理员用户
    $userId = Db::name('user')->insertGetId([
        'username' => 'admin',
        'password' => password_hash('123456', PASSWORD_DEFAULT), // 默认密码：123456
        'realname' => '系统管理员',
        'department_id' => $deptId,
        'status' => 1,
        'create_at' => date('Y-m-d H:i:s')
    ]);

    // 分配角色给管理员
    Db::name('user_role')->insert([
        'user_id' => $userId,
        'role_id' => $roleId
    ]);

    // 创建基础权限
    $permissions = [
        ['name' => '系统管理', 'code' => 'system', 'type' => 'menu', 'parent_id' => 0],
        ['name' => '用户管理', 'code' => 'user', 'type' => 'menu', 'parent_id' => 1],
        ['name' => '角色管理', 'code' => 'role', 'type' => 'menu', 'parent_id' => 1],
        ['name' => '权限管理', 'code' => 'permission', 'type' => 'menu', 'parent_id' => 1],
        ['name' => '部门管理', 'code' => 'department', 'type' => 'menu', 'parent_id' => 1],
        ['name' => '电站管理', 'code' => 'station', 'type' => 'menu', 'parent_id' => 0],
        ['name' => '告警管理', 'code' => 'alarm', 'type' => 'menu', 'parent_id' => 0],
        ['name' => '数据统计', 'code' => 'stats', 'type' => 'menu', 'parent_id' => 0],
    ];

    foreach ($permissions as $permission) {
        $permission['status'] = 1;
        $permission['create_at'] = date('Y-m-d H:i:s');
        $permId = Db::name('permission')->insertGetId($permission);

        // 为超级管理员分配权限
        Db::name('role_permission')->insert([
            'role_id' => $roleId,
            'permission_id' => $permId
        ]);
    }

    echo "管理员账号创建成功！\n";
    echo "账号：admin\n";
    echo "密码：123456\n";
    echo "请登录后及时修改密码！\n";

} catch (\Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
} 