<?php
declare(strict_types=1);

namespace app\controller\finance;

use think\facade\Request;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Rent
{
    public function getList()
    {
        $params = Request::get();
        $query = Db::name('sp_fund_rent')
            ->field('id,total_serial,gongwang_account,payment_date,amount,payment_method')
            ->order('payment_date', 'desc');

        if (!empty($params['dateRange'])) {
            $query->whereBetweenTime('payment_date', $params['dateRange'][0], $params['dateRange'][1]);
        }

        if (!empty($params['paymentMethod'])) {
            $query->where('payment_method', $params['paymentMethod']);
        }

        $total = $query->count();
        $list = $query->paginate([
            'page' => $params['page'] ?? 1,
            'list_rows' => $params['pageSize'] ?? 10
        ]);

        return json([
            'code' => 200,
            'data' => [
                'list' => $list->items(),
                'total' => $total
            ]
        ]);
    }

    public function import()
    {
        $file = Request::file('file');
        try {
            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();
            $rows = $sheet->toArray();
            
            $success = 0;
            $errors = [];
            
            foreach ($rows as $index => $row) {
                if ($index === 0) continue; // 跳过表头
                
                // 验证数据格式
                if (count($row) < 6) {
                    $errors[] = "第{$index}行数据列数不足";
                    continue;
                }
                
                // 匹配电站
                $station = Db::name('station')
                    ->where('total_serial', $row[0])
                    ->where('gongwang_account', $row[1])
                    ->find();
                
                if (!$station) {
                    $errors[] = "第{$index}行未找到匹配电站";
                    continue;
                }
                
                // 保存数据
                Db::name('sp_fund_rent')->insert([
                    'station_id' => $station['id'],
                    'total_serial' => $row[0],
                    'gongwang_account' => $row[1],
                    'payment_date' => $row[2],
                    'amount' => $row[3],
                    'payment_method' => $row[4],
                    'remark' => $row[5]
                ]);
                
                $success++;
            }
            
            return json([
                'code' => 200,
                'message' => "导入完成，成功{$success}条，失败".count($errors)."条",
                'errors' => $errors
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }

    public function save(Request $request)
    {
        $data = $request->post();
        
        // 验证电站信息
        $station = Db::name('station')
            ->where('total_serial', $data['total_serial'])
            ->where('gongwang_account', $data['gongwang_account'])
            ->find();
        
        if (!$station) {
            return json(['code' => 400, 'message' => '未找到匹配的电站信息']);
        }
        
        $data['station_id'] = $station['id'];
        
        try {
            if (isset($data['id'])) {
                Db::name('sp_fund_rent')->update($data);
            } else {
                Db::name('sp_fund_rent')->insert($data);
            }
            return json(['code' => 200]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }

    public function export()
    {
        $params = input('get.');
        $query = Db::name('sp_fund_rent')->alias('r')
            ->join('sp_station s', 'r.station_id=s.id')
            ->field('r.*, s.contact_name, s.address');
        
        // 过滤条件处理...
        
        $data = $query->select();
        
        $spreadsheet = new Spreadsheet();
        // 生成Excel文件...
    }
} 