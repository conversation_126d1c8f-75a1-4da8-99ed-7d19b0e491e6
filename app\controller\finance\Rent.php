<?php
namespace app\controller\finance;

use app\BaseController;
use think\facade\Db;
use app\model\Station;
use think\facade\Validate;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Rent extends BaseController
{
    /**
     * 获取租金发放列表
     */
    public function getList()
    {
        $page = input('page', 1, 'intval');
        $pageSize = input('pageSize', 10, 'intval');
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $paymentMethod = input('payment_method', '', 'trim');

        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.payment_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.payment_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.payment_date', '<=', $endDate];
        }
        
        if (!empty($paymentMethod)) {
            $where[] = ['a.payment_method', '=', $paymentMethod];
        }
        
        try {
            // 获取数据总数
            $total = Db::table('sp_fund_rent')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->count();
            
            // 查询数据
            $list = Db::table('sp_fund_rent')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.payment_date DESC, a.id DESC')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return $this->success([
                'items' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取租金发放列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加租金发放记录
     */
    public function save()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'payment_date' => 'require|date',
            'payment_method' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 获取电站信息
            $station = Station::where('id', $params['station_id'])->find();
            if (empty($station)) {
                return $this->error('电站不存在');
            }
            
            // 准备数据
            $data = [
                'station_id' => $params['station_id'],
                'payment_date' => $params['payment_date'],
                'payment_method' => $params['payment_method'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            // 添加记录
            $id = Db::table('sp_fund_rent')->insertGetId($data);
            
            return $this->success('添加成功', ['id' => $id]);
        } catch (\Exception $e) {
            return $this->error('添加失败：' . $e->getMessage());
        }
    }

    /**
     * 登记发放 - 手动添加单条租金发放记录
     */
    public function register()
    {
        $params = $this->request->post();

        // 参数验证
        $validate = Validate::rule([
            'total_serial' => 'require',
            'gongwang_account' => 'require',
            'contact_name' => 'require',
            'payment_date' => 'require|date',
            'payment_type' => 'require|in:银行转账,现金支付,支付宝,微信支付,其他',
            'amount' => 'require|float|gt:0'
        ]);

        if (!$validate->check($params)) {
            return json(['code' => 400, 'message' => $validate->getError()]);
        }

        try {
            // 查找电站记录
            $station = Db::name('station')
                ->where('total_serial', $params['total_serial'])
                ->where('gongwang_account', $params['gongwang_account'])
                ->find();

            if (!$station) {
                return json(['code' => 404, 'message' => '未找到匹配的电站信息，请先在电站管理中添加该电站']);
            }

            // 解析日期
            $dateInfo = date_parse($params['payment_date']);

            $data = [
                'station_id' => $station['id'],
                'payment_date' => $params['payment_date'],
                'payment_year' => $dateInfo['year'],
                'payment_month' => $dateInfo['month'],
                'payment_day' => $dateInfo['day'],
                'payment_type' => $params['payment_type'],
                'amount' => $params['amount'],
                'remark' => $params['remark'] ?? '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            Db::startTrans();

            // 插入明细记录
            $detailId = Db::name('rent_payment_detail')->insertGetId($data);

            // 更新或创建月度汇总
            $this->updateMonthlyTotal($station['id'], $dateInfo['year'], $dateInfo['month']);

            Db::commit();

            return json([
                'code' => 200,
                'message' => '登记成功',
                'data' => ['id' => $detailId]
            ]);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '登记失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新租金发放记录
     */
    public function update($id)
    {
        $params = $this->request->put();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'payment_date' => 'require|date',
            'payment_method' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 检查记录是否存在
            $record = Db::table('sp_fund_rent')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 准备数据
            $data = [
                'payment_date' => $params['payment_date'],
                'payment_method' => $params['payment_method'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 更新记录
            Db::table('sp_fund_rent')->where('id', $id)->update($data);
            
            return $this->success('更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除租金发放记录
     */
    public function delete($id)
    {
        try {
            // 检查记录是否存在
            $record = Db::table('sp_fund_rent')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 删除记录
            Db::table('sp_fund_rent')->where('id', $id)->delete();
            
            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 导入租金发放数据
     */
    public function import()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请上传文件');
        }
        
        // 验证文件类型
        $fileExt = $file->getOriginalExtension();
        if (!in_array($fileExt, ['xlsx', 'xls'])) {
            return $this->error('仅支持 Excel 文件导入');
        }
        
        try {
            // 获取文件内容
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            
            // 获取标题行，解析部门和年份信息
            $titleCell = $worksheet->getCellByColumnAndRow(1, 1)->getValue();
            $department = '';
            $year = '';
            $rentYear = '';
            
            // 解析标题格式：所属部门年度项目租金支付明细表（租金发放年度）
            if (preg_match('/^(.*?)(\d{4})年项目租金支付明细表（(\d{4})年度）/', $titleCell, $matches)) {
                $department = $matches[1];
                $year = $matches[2];
                $rentYear = $matches[3];
            } else {
                return $this->error('无法从表格标题中解析部门和年份信息，请确认模板格式是否正确');
            }
            
            // 直接获取单元格值，而不是使用toArray，防止类型转换问题
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            
            // 导入结果统计
            $success = 0;
            $fail = 0;
            $errors = [];
            
            Db::startTrans();
            
            // 从第3行开始读取数据（跳过标题行和表头行）
            for ($row = 3; $row <= $highestRow; $row++) {
                $arcId = $worksheet->getCellByColumnAndRow(1, $row)->getValue();
                $contactName = $worksheet->getCellByColumnAndRow(2, $row)->getValue();
                $gongwangAccount = $worksheet->getCellByColumnAndRow(3, $row)->getValue();
                $contactPhone = $worksheet->getCellByColumnAndRow(4, $row)->getValue();
                $county = $worksheet->getCellByColumnAndRow(5, $row)->getValue();
                $town = $worksheet->getCellByColumnAndRow(6, $row)->getValue();
                $village = $worksheet->getCellByColumnAndRow(7, $row)->getValue();
                $bankCard = $worksheet->getCellByColumnAndRow(8, $row)->getValue();
                $bankName = $worksheet->getCellByColumnAndRow(9, $row)->getValue();
                $expectedAmount = $worksheet->getCellByColumnAndRow(10, $row)->getValue();
                $actualAmount = $worksheet->getCellByColumnAndRow(11, $row)->getValue();
                $paymentStatus = $worksheet->getCellByColumnAndRow(12, $row)->getValue();
                $remark = $worksheet->getCellByColumnAndRow(13, $row)->getValue();
                
                // 跳过空行
                if (empty($gongwangAccount) && empty($contactName)) {
                    continue;
                }
                
                // 数据验证
                if (empty($gongwangAccount)) {
                    $fail++;
                    $errors[] = "第{$row}行: 户号(国网号)不能为空";
                    continue;
                }
                
                if (!is_numeric($expectedAmount) || !is_numeric($actualAmount)) {
                    $fail++;
                    $errors[] = "第{$row}行: 应发租金或实发租金必须为数字";
                    continue;
                }
                
                // 查找电站ID，使用gongwang_account匹配
                $station = \app\model\Station::where('gongwang_account', $gongwangAccount)->find();
                
                if (!$station) {
                    $fail++;
                    $errors[] = "第{$row}行: 找不到匹配的电站信息，国网户号: {$gongwangAccount}";
                    continue;
                }
                
                try {
                    // 准备数据
                    $paymentDate = date('Y-m-d'); // 默认今天，可以根据需要修改
                    $paymentMethod = 'bank'; // 默认银行转账，可以根据需要修改
                    
                    // 创建租金记录
                    $data = [
                        'station_id' => $station['id'],
                        'arc_id' => $arcId,
                        'department' => $department,
                        'year' => $year,
                        'rent_year' => $rentYear,
                        'gongwang_account' => $gongwangAccount,
                        'total_serial' => $station['total_serial'] ?? '',
                        'payment_date' => $paymentDate,
                        'payment_method' => $paymentMethod,
                        'expected_amount' => $expectedAmount,
                        'actual_amount' => $actualAmount,
                        'payment_status' => $paymentStatus,
                        'remark' => $remark,
                        'county' => $county,
                        'town' => $town,
                        'village' => $village,
                        'bank_card' => $bankCard,
                        'bank_name' => $bankName,
                        'create_time' => date('Y-m-d H:i:s')
                    ];
                    
                    Db::table('sp_fund_rent')->insert($data);
                    $success++;
                } catch (\Exception $e) {
                    $fail++;
                    $errors[] = "第{$row}行: " . $e->getMessage();
                    continue;
                }
            }
            
            if ($fail > 0) {
                Db::rollback();
                return $this->error('导入失败，发现' . $fail . '条错误记录', ['errors' => $errors]);
            }
            
            Db::commit();
            return $this->success([
                'success' => $success,
                'fail' => $fail,
                'errors' => $errors
            ], '成功导入' . $success . '条数据');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导出租金发放数据
     */
    public function export()
    {
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $paymentMethod = input('payment_method', '', 'trim');
        
        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.payment_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.payment_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.payment_date', '<=', $endDate];
        }
        
        if (!empty($paymentMethod)) {
            $where[] = ['a.payment_method', '=', $paymentMethod];
        }
        
        try {
            // 查询数据
            $list = Db::table('sp_fund_rent')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.payment_date DESC, a.id DESC')
                ->select()
                ->toArray();
            
            // 创建电子表格
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $sheet->setCellValue('A1', '总序号');
            $sheet->setCellValue('B1', '国网号');
            $sheet->setCellValue('C1', '户主名称');
            $sheet->setCellValue('D1', '支付日期');
            $sheet->setCellValue('E1', '支付方式');
            $sheet->setCellValue('F1', '支付金额');
            $sheet->setCellValue('G1', '备注');
            
            // 填充数据
            $row = 2;
            foreach ($list as $item) {
                $sheet->setCellValue('A' . $row, $item['total_serial']);
                $sheet->setCellValue('B' . $row, $item['gongwang_account']);
                $sheet->setCellValue('C' . $row, $item['contact_name']);
                $sheet->setCellValue('D' . $row, $item['payment_date']);
                $sheet->setCellValue('E' . $row, $item['payment_method']);
                $sheet->setCellValue('F' . $row, $item['amount']);
                $sheet->setCellValue('G' . $row, $item['remark']);
                $row++;
            }
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(12);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(12);
            $sheet->getColumnDimension('D')->setWidth(12);
            $sheet->getColumnDimension('E')->setWidth(10);
            $sheet->getColumnDimension('F')->setWidth(12);
            $sheet->getColumnDimension('G')->setWidth(25);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="租金发放数据_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取导入模板
     */
    public function template()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('租金表');
            
            // 设置标题行
            $currentYear = date('Y');
            $department = '业务一部';  // 默认部门，可根据当前登录用户的部门调整
            $projectYear = $currentYear - 1;  // 默认项目年度为当前年份减1
            
            $title = $department . $projectYear . '年项目租金支付明细表（' . $currentYear . '年度）';
            $sheet->setCellValue('A1', $title);
            $sheet->mergeCells('A1:M1');
            
            // 设置表头
            $sheet->setCellValue('A2', '序号');
            $sheet->setCellValue('B2', '姓名');
            $sheet->setCellValue('C2', '户号');
            $sheet->setCellValue('D2', '电话');
            $sheet->setCellValue('E2', '县区');
            $sheet->setCellValue('F2', '乡镇');
            $sheet->setCellValue('G2', '村名');
            $sheet->setCellValue('H2', '银行卡号');
            $sheet->setCellValue('I2', '开户银行');
            $sheet->setCellValue('J2', '应发租金');
            $sheet->setCellValue('K2', '实发租金');
            $sheet->setCellValue('L2', '发放状态');
            $sheet->setCellValue('M2', '备注');
            
            // 添加示例数据
            $sheet->setCellValue('A3', '1');
            $sheet->setCellValue('B3', '张三');
            $sheet->setCellValue('C3', '3700123456789');
            $sheet->setCellValue('D3', '13800138000');
            $sheet->setCellValue('E3', '示例县');
            $sheet->setCellValue('F3', '示例镇');
            $sheet->setCellValue('G3', '示例村');
            $sheet->setCellValue('H3', '6222021610000000000');
            $sheet->setCellValue('I3', '工商银行');
            $sheet->setCellValue('J3', '1200.00');
            $sheet->setCellValue('K3', '1200.00');
            $sheet->setCellValue('L3', '已发放');
            $sheet->setCellValue('M3', '');
            
            // 设置样式
            $titleStyle = [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];
            
            $headerStyle = [
                'font' => [
                    'bold' => true,
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'E0E0E0',
                    ],
                ],
            ];
            
            $sheet->getStyle('A1:M1')->applyFromArray($titleStyle);
            $sheet->getStyle('A2:M2')->applyFromArray($headerStyle);
            $sheet->getRowDimension(1)->setRowHeight(40);
            $sheet->getRowDimension(2)->setRowHeight(30);
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(8);
            $sheet->getColumnDimension('B')->setWidth(12);
            $sheet->getColumnDimension('C')->setWidth(18);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(12);
            $sheet->getColumnDimension('F')->setWidth(12);
            $sheet->getColumnDimension('G')->setWidth(15);
            $sheet->getColumnDimension('H')->setWidth(25);
            $sheet->getColumnDimension('I')->setWidth(15);
            $sheet->getColumnDimension('J')->setWidth(12);
            $sheet->getColumnDimension('K')->setWidth(12);
            $sheet->getColumnDimension('L')->setWidth(12);
            $sheet->getColumnDimension('M')->setWidth(20);
            
            // 添加一个指导说明页
            $guideSheet = $spreadsheet->createSheet();
            $guideSheet->setTitle('导入说明');
            $guideSheet->setCellValue('A1', '租金发放表导入说明');
            $guideSheet->setCellValue('A3', '1. 请勿修改表格的标题格式，标题格式为：所属部门xxxx年项目租金支付明细表（xxxx年度）');
            $guideSheet->setCellValue('A4', '2. 请勿修改第二行的字段名称');
            $guideSheet->setCellValue('A5', '3. 从第三行开始填写数据');
            $guideSheet->setCellValue('A6', '4. 户号必须与系统中的国网户号完全匹配，否则无法导入');
            $guideSheet->setCellValue('A7', '5. 应发租金和实发租金必须为数字');
            $guideSheet->setCellValue('A8', '6. 使用此模板导入的数据将设置发放日期为今天，发放方式为银行转账');
            $guideSheet->mergeCells('A1:F1');
            $guideSheet->getStyle('A1')->applyFromArray($titleStyle);
            
            for ($i = 'A'; $i <= 'F'; $i++) {
                $guideSheet->getColumnDimension($i)->setWidth(25);
            }
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="租金发放导入模板_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('获取模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新月度汇总表
     */
    private function updateMonthlyTotal($stationId, $year, $month)
    {
        // 查询该月的各种类型金额
        $result = Db::name('rent_payment_detail')
            ->where('station_id', $stationId)
            ->where('payment_year', $year)
            ->where('payment_month', $month)
            ->field([
                'sum(CASE WHEN payment_type = "银行转账" THEN amount ELSE 0 END) as bank_amount',
                'sum(CASE WHEN payment_type = "现金支付" THEN amount ELSE 0 END) as cash_amount',
                'sum(CASE WHEN payment_type = "支付宝" THEN amount ELSE 0 END) as alipay_amount',
                'sum(CASE WHEN payment_type = "微信支付" THEN amount ELSE 0 END) as wechat_amount',
                'sum(CASE WHEN payment_type = "其他" THEN amount ELSE 0 END) as other_amount',
                'sum(amount) as total_amount'
            ])
            ->find();

        // 检查是否已有月度记录
        $monthlyRecord = Db::name('rent_payment_monthly')
            ->where('station_id', $stationId)
            ->where('payment_year', $year)
            ->where('payment_month', $month)
            ->find();

        $monthlyData = [
            'bank_amount' => $result['bank_amount'] ?? 0,
            'cash_amount' => $result['cash_amount'] ?? 0,
            'alipay_amount' => $result['alipay_amount'] ?? 0,
            'wechat_amount' => $result['wechat_amount'] ?? 0,
            'other_amount' => $result['other_amount'] ?? 0,
            'total_amount' => $result['total_amount'] ?? 0,
        ];

        if ($monthlyRecord) {
            // 更新现有记录
            Db::name('rent_payment_monthly')
                ->where('id', $monthlyRecord['id'])
                ->update($monthlyData);
        } else {
            // 创建新记录
            $monthlyData['station_id'] = $stationId;
            $monthlyData['payment_year'] = $year;
            $monthlyData['payment_month'] = $month;
            $monthlyData['create_time'] = date('Y-m-d H:i:s');

            Db::name('rent_payment_monthly')->insert($monthlyData);
        }
    }
}