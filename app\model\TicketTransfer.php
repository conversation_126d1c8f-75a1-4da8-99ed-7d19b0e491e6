<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class TicketTransfer extends Model
{
    protected $name = 'ticket_transfer';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = false;
    
    // 关联工单
    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }
    
    // 关联原处理人
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }
    
    // 关联新处理人
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }
    
    // 关联操作人
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
} 