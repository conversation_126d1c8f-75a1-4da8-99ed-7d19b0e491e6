<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Session;
use think\facade\Config;
use think\facade\Filesystem;
use app\service\LogService;

class Archive extends BaseController
{
    protected $progressKey = 'archive_init_progress';
    
    /**
     * 获取指定父部门下的部门列表
     */
    public function getDepartmentsByParent()
    {
        try {
            $parentId = $this->request->param('parentId', 0, 'intval');
            $excludeId = $this->request->param('excludeId', 0, 'intval');
            
            $query = Db::name('department')->where('parent_id', $parentId);
            
            if ($excludeId > 0) {
                $query->where('id', '<>', $excludeId);
            }
            
            $departments = $query->field('id, name')->select();
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $departments]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取部门列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取档案目录列表
     */
    public function getDirectories()
    {
        try {
            $oldArchPath = root_path() . 'public/oldarch';
            
            // 检查目录是否存在
            if (!is_dir($oldArchPath)) {
                return json(['code' => 404, 'message' => '档案目录不存在', 'data' => []]);
            }
            
            // 获取目录列表
            $dirs = [];
            $handle = opendir($oldArchPath);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..' && is_dir($oldArchPath . '/' . $file)) {
                        $dirs[] = $file;
                    }
                }
                closedir($handle);
            }
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $dirs]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取档案目录列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 初始化档案
     */
    public function initArchive()
    {
        try {
            // 设置无限执行时间（或足够长的时间）
            ini_set('max_execution_time', 0);
            set_time_limit(0);
            
            $departmentId = $this->request->post('departmentId', 0, 'intval');
            $archiveDir = $this->request->post('archiveDir', '', 'trim');
            
            if (empty($departmentId) || empty($archiveDir)) {
                return json(['code' => 400, 'message' => '参数错误']);
            }
            
            // 验证部门是否存在
            $department = Db::name('department')->where('id', $departmentId)->find();
            if (!$department) {
                return json(['code' => 404, 'message' => '部门不存在']);
            }
            
            // 验证目录是否存在
            $oldArchPath = root_path() . 'public/oldarch/' . $archiveDir;
            if (!is_dir($oldArchPath)) {
                return json(['code' => 404, 'message' => '档案目录不存在']);
            }
            
            // 初始化进度信息
            $progressData = [
                'total' => 0,
                'processed' => 0,
                'percentage' => 0,
                'currentFile' => '',
                'finished' => false,
                'success' => false,
                'logs' => ['开始初始化档案处理...', '正在扫描文件，请稍候...'],
                'departmentId' => $departmentId,
                'departmentName' => $department['name'],
                'archiveDir' => $archiveDir,
                'last_update' => date('Y-m-d H:i:s')
            ];
            
            Cache::set($this->progressKey, $progressData, 7200); // 缓存2小时
            
            // 立即发送队列任务并返回响应，不等待结果
            $this->asyncQueueTask($departmentId, $archiveDir, $department['name']);
            
            return json(['code' => 200, 'message' => '档案初始化任务已启动，请通过进度查询接口获取处理状态']);
        } catch (\Exception $e) {
            \think\facade\Log::error('初始化档案失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '初始化档案失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取处理进度
     */
    public function getProgress()
    {
        try {
            $progressData = Cache::get($this->progressKey);
            
            if (!$progressData) {
                return json(['code' => 404, 'message' => '没有正在进行的档案初始化任务']);
            }
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $progressData]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取进度失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 异步启动队列任务
     */
    protected function asyncQueueTask($departmentId, $archiveDir, $departmentName)
    {
        try {
            // 将请求写入数据库
            $taskId = Db::name('task_queue')->insertGetId([
                'task_type' => 'archive_init',
                'task_data' => json_encode([
                    'department_id' => $departmentId,
                    'archive_dir' => $archiveDir,
                    'department_name' => $departmentName
                ]),
                'status' => 'pending',
                'create_time' => date('Y-m-d H:i:s')
            ]);
            
            // 更新进度信息
            $progressData = Cache::get($this->progressKey, []);
            if (!empty($progressData)) {
                $progressData['logs'][] = '档案处理任务已加入队列(任务ID:'.$taskId.')，正在后台处理...';
                $progressData['task_id'] = $taskId;
                Cache::set($this->progressKey, $progressData, 7200);
            }
            
            // 异步启动队列处理
            \think\facade\Queue::push('app\job\ArchiveProcess', [
                'department_id' => $departmentId,
                'archive_dir' => $archiveDir,
                'department_name' => $departmentName,
                'task_id' => $taskId
            ], 'archive');
            
            \think\facade\Log::info('档案处理任务已加入队列 - 部门ID: ' . $departmentId . ', 目录: ' . $archiveDir);
            
            return true;
        } catch (\Exception $e) {
            \think\facade\Log::error('启动队列任务失败: ' . $e->getMessage());
            
            // 尝试使用备用方式启动
            try {
                // 直接触发队列处理，不等待结果
                \think\facade\Queue::push('app\job\ArchiveProcess', [
                    'department_id' => $departmentId,
                    'archive_dir' => $archiveDir,
                    'department_name' => $departmentName
                ]);
                
                return true;
            } catch (\Exception $e2) {
                \think\facade\Log::error('备用队列启动也失败: ' . $e2->getMessage());
                return false;
            }
        }
    }
} 