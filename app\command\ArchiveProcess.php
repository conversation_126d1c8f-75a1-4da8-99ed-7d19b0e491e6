<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Cache;
use app\service\LogService;

class ArchiveProcess extends Command
{
    protected $progressKey = 'archive_init_progress';
    
    protected function configure()
    {
        $this->setName('archive:process')
            ->setDescription('Process archives from old directory structure')
            ->addArgument('department_id', \think\console\input\Argument::REQUIRED, 'Department ID')
            ->addArgument('archive_dir', \think\console\input\Argument::REQUIRED, 'Archive directory name');
    }
    
    protected function execute(Input $input, Output $output)
    {
        $departmentId = $input->getArgument('department_id');
        $archiveDir = $input->getArgument('archive_dir');
        
        $output->writeln("Starting archive processing for department ID: {$departmentId}, directory: {$archiveDir}");
        \think\facade\Log::info("开始处理档案 - 部门ID: {$departmentId}, 目录: {$archiveDir}");
        
        // 初始化进度信息，确保能够正确更新缓存
        $this->updateProgress([
            'logs' => ["命令行任务已启动，开始处理档案 - 部门ID: {$departmentId}, 目录: {$archiveDir}"]
        ]);
        
        try {
            $this->processArchives($departmentId, $archiveDir, $output);
            $output->writeln('Archive processing completed successfully');
            \think\facade\Log::info("档案处理成功完成");
            
            // 确保最后更新进度为完成状态
            $this->updateProgress([
                'finished' => true,
                'success' => true,
                'percentage' => 100,
                'logs' => ["档案处理任务成功完成"]
            ]);
        } catch (\Exception $e) {
            $errorMsg = "Error processing archives: {$e->getMessage()}";
            $output->writeln("<error>{$errorMsg}</error>");
            \think\facade\Log::error($errorMsg);
            
            // 更新进度为错误状态
            $this->updateProgress([
                'finished' => true,
                'success' => false,
                'logs' => ["错误: 处理档案失败 - " . $e->getMessage()]
            ]);
        }
    }
    
    /**
     * 处理档案
     */
    protected function processArchives($departmentId, $archiveDir, Output $output)
    {
        // 获取档案目录
        $oldArchPath = root_path() . 'public/oldarch/' . $archiveDir;
        
        if (!is_dir($oldArchPath)) {
            throw new \Exception('档案目录不存在');
        }
        
        // 获取部门
        $department = Db::name('department')->where('id', $departmentId)->find();
        if (!$department) {
            throw new \Exception('部门不存在');
        }
        
        // 阶段1: 收集所有文件并准备任务数组
        $output->writeln("开始收集文件信息...");
        \think\facade\Log::info("开始收集文件信息...");
        $this->updateProgress(['logs' => ["开始收集文件信息..."]]);
        
        $taskList = $this->collectFileTasks($oldArchPath, $departmentId, $department);
        
        // 更新总数
        $totalCount = count($taskList);
        $this->updateProgress([
            'total' => $totalCount,
            'logs' => ["已收集 {$totalCount} 个档案文件待处理"]
        ]);
        
        if ($totalCount == 0) {
            $this->updateProgress([
                'logs' => ["未找到需要处理的档案文件"],
                'finished' => true,
                'success' => false
            ]);
            throw new \Exception('未找到需要处理的档案文件');
        }
        
        // 阶段2: 执行文件处理
        $output->writeln("开始处理 {$totalCount} 个档案文件...");
        \think\facade\Log::info("开始处理 {$totalCount} 个档案文件");
        $this->updateProgress(['logs' => ["开始处理 {$totalCount} 个档案文件..."]]);
        
        return $this->executeFileTasks($taskList, $output);
    }
    
    /**
     * 收集所有文件任务
     */
    protected function collectFileTasks($oldArchPath, $departmentId, $department)
    {
        $taskList = [];
        $logs = [];
        
        // 获取年份子目录列表
        $yearDirs = $this->getDirectories($oldArchPath);
        
        // 遍历年份目录
        foreach ($yearDirs as $yearDir) {
            // 解析年份
            preg_match('/(\d{4})年(.+)/', $yearDir, $matches);
            
            if (count($matches) >= 3) {
                $year = $matches[1];
                $deptName = $matches[2];
                
                // 从目录名称中提取部门名称
                $installDate = $year . '-01-01 00:00:00';
                
                // 查找匹配的部门ID
                $deptId = $departmentId; // 默认使用提供的部门ID
                
                // 如果目录名中的部门名称与当前部门不同，尝试查找匹配的部门
                if ($deptName != $department['name']) {
                    $matchedDept = Db::name('department')
                        ->where('name', 'like', "%{$deptName}%")
                        ->find();
                    
                    if ($matchedDept) {
                        $deptId = $matchedDept['id'];
                    }
                }
                
                // 准备更新电站安装日期的任务
                $taskList[] = [
                    'type' => 'update_install_date',
                    'dept_id' => $deptId,
                    'dept_name' => $deptName,
                    'install_date' => $installDate
                ];
                
                // 处理该年份目录下的电站档案
                $stationDirs = $this->getDirectories($oldArchPath . '/' . $yearDir);
                
                foreach ($stationDirs as $stationDir) {
                        // 解析电站目录名称（arc_id-contact_name）
                        preg_match('/([^-]+)-(.+)/', $stationDir, $stationMatches);
                        
                        if (count($stationMatches) >= 3) {
                            $arcId = trim($stationMatches[1]);
                            $contactName = trim($stationMatches[2]);
                            
                            // 查找对应的电站
                            $station = Db::name('station')
                                ->where([
                                    ['arc_id', '=', $arcId],
                                    ['contact_name', 'like', "%{$contactName}%"]
                                ])
                                ->find();
                            
                            if (!$station) {
                                // 尝试仅根据arc_id查找
                                $station = Db::name('station')
                                    ->where('arc_id', $arcId)
                                    ->find();
                            }
                            
                            if ($station) {
                                $stationId = $station['id'];
                                
                            // 获取档案文件
                                $stationDirPath = $oldArchPath . '/' . $yearDir . '/' . $stationDir;
                            $files = $this->getFiles($stationDirPath);
                            
                            // 准备目标目录
                            $targetDir = 'uploads/archive/' . date('Y/m/d') . '/' . $departmentId;
                            $targetPath = root_path() . 'public/' . $targetDir;
                            
                            // 收集文件处理任务
                            foreach ($files as $file) {
                                // 检查是否是相对路径 (包含目录)
                                $isRelativePath = strpos($file, '/') !== false;
                                $filePath = $stationDirPath . '/' . $file;
                                
                                // 如果文件不存在，可能是递归获取的路径，需要处理
                                if (!file_exists($filePath) && $isRelativePath) {
                                    // 已经是相对路径，无需再拼接目录名
                                    $filePath = $stationDirPath . '/' . $file;
                                }
                                
                                if (file_exists($filePath) && is_file($filePath)) {
                                    $fileSize = filesize($filePath);
                                    $fileInfo = pathinfo($filePath);
                                    $fileType = isset($fileInfo['extension']) ? $fileInfo['extension'] : '';
                                    $fileName = $fileInfo['filename'];
                                    
                                    // 保留子目录结构，创建对应的目标子目录
                                    $subDir = '';
                                    if ($isRelativePath) {
                                        $subDir = dirname($file);
                                        if ($subDir == '.') $subDir = '';
                                    }
                                    
                                    $finalTargetDir = $targetDir;
                                    $finalTargetPath = $targetPath;
                                    
                                    if ($subDir) {
                                        $finalTargetDir .= '/' . $subDir;
                                        $finalTargetPath .= '/' . $subDir;
                                    }
                                    
                                    // 生成新的文件名
                                    $newFileName = date('YmdHis') . md5(uniqid(mt_rand(), true)) . ($fileType ? '.' . $fileType : '');
                                    
                                    // 添加到任务列表
                                    $taskList[] = [
                                        'type' => 'process_file',
                                        'station_id' => $stationId,
                                        'station_name' => $arcId . '-' . $contactName,
                                        'source_file' => $filePath,
                                        'target_dir' => $finalTargetDir,
                                        'target_path' => $finalTargetPath,
                                        'new_file_name' => $newFileName,
                                        'original_file_name' => $fileName,
                                        'file_size' => $fileSize,
                                        'file_type' => $fileType,
                                        'sub_dir' => $subDir
                                    ];
                                }
                            }
                        } else {
                            $logs[] = "警告: 未找到匹配的电站: {$arcId}-{$contactName}";
                        }
                    } else {
                        $logs[] = "警告: 电站目录名称格式不正确: {$stationDir}";
                    }
                }
            } else {
                $logs[] = "警告: 年份目录名称格式不正确: {$yearDir}";
            }
        }
        
        // 更新日志
        if (!empty($logs)) {
            $this->updateProgress(['logs' => $logs]);
        }
        
        return $taskList;
    }
    
    /**
     * 执行文件处理任务
     */
    protected function executeFileTasks($taskList, Output $output)
    {
        $logs = [];
        $processedCount = 0;
        $totalCount = count($taskList);
        $stationUpdateCount = 0;
        $fileProcessCount = 0;
        
        foreach ($taskList as $index => $task) {
            $processedCount++;
            $percentage = intval(($processedCount / $totalCount) * 100);
            
            try {
                if ($task['type'] === 'update_install_date') {
                    // 更新电站安装日期
                    Db::name('station')
                        ->where('department_id', $task['dept_id'])
                        ->update(['install_date' => $task['install_date']]);
                    
                    $stationUpdateCount++;
                    $logs[] = "已更新部门 {$task['dept_name']}(ID: {$task['dept_id']}) 的电站安装日期为 {$task['install_date']}";
                    
                } else if ($task['type'] === 'process_file') {
                    // 处理文件
                    $currentFile = basename($task['source_file']);
                    
                    // 更新进度
                    $this->updateProgress([
                        'processed' => $processedCount,
                        'percentage' => $percentage,
                        'currentFile' => $currentFile
                    ]);
                    
                    // 确保目标目录存在
                    if (!is_dir($task['target_path'])) {
                        mkdir($task['target_path'], 0755, true);
                    }
            
            // 复制文件
                    $newFilePath = $task['target_path'] . '/' . $task['new_file_name'];
                    if (copy($task['source_file'], $newFilePath)) {
                // 添加档案记录
                $archiveData = [
                            'station_id' => $task['station_id'],
                            'name' => $task['original_file_name'],
                            'file_path' => $task['target_dir'] . '/' . $task['new_file_name'],
                            'file_size' => $task['file_size'],
                            'file_type' => $task['file_type'],
                    'create_at' => date('Y-m-d H:i:s')
                ];
                
                Db::name('station_archive')->insert($archiveData);
                        $fileProcessCount++;
                        
                        if ($processedCount % 10 == 0 || $processedCount == $totalCount) {
                            $logs[] = "已处理 {$processedCount}/{$totalCount} 个任务，当前处理: {$currentFile}";
                        }
            } else {
                        $logs[] = "错误: 无法复制文件 {$currentFile} 到新位置";
                    }
                }
                
                // 每10个任务更新一次进度日志
                if ($processedCount % 10 == 0 || $processedCount == $totalCount) {
                    $this->updateProgress(['logs' => $logs]);
                    $logs = []; // 清空日志缓存
                }
                
            } catch (\Exception $e) {
                $logs[] = "错误: 处理任务 " . ($task['type'] == 'process_file' ? basename($task['source_file']) : $task['dept_name']) . " 失败: " . $e->getMessage();
                $this->updateProgress(['logs' => $logs]);
                $logs = [];
            }
        }
        
        // 完成
        $finalLogs = ["档案处理完成，共处理 {$stationUpdateCount} 个部门安装日期更新和 {$fileProcessCount} 个档案文件"];
        $this->updateProgress([
            'finished' => true,
            'success' => true,
            'percentage' => 100,
            'processed' => $totalCount,
            'logs' => $finalLogs
        ]);
        
        return true;
    }
    
    /**
     * 获取目录列表
     */
    protected function getDirectories($path)
    {
        $dirs = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..' && is_dir($path . '/' . $file)) {
                        $dirs[] = $file;
                    }
                }
                closedir($handle);
            }
        }
        
        return $dirs;
    }
    
    /**
     * 获取文件列表
     */
    protected function getFiles($path)
    {
        $files = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..') {
                        if (is_file($path . '/' . $file)) {
                        $files[] = $file;
                        } elseif (is_dir($path . '/' . $file)) {
                            // 如果是子目录，则递归获取
                            $subDirFiles = $this->getFilesRecursive($path . '/' . $file, $file);
                            $files = array_merge($files, $subDirFiles);
                        }
                    }
                }
                closedir($handle);
            }
        }
        
        return $files;
    }
    
    /**
     * 递归获取所有文件，包括子目录
     * @param string $path 当前目录路径
     * @param string $relativePath 相对于起始目录的路径
     * @return array 文件路径列表，相对于起始目录
     */
    protected function getFilesRecursive($path, $relativePath = '')
    {
        $files = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..') {
                        $filePath = $path . '/' . $file;
                        $relativeFilePath = $relativePath ? $relativePath . '/' . $file : $file;
                        
                        if (is_file($filePath)) {
                            $files[] = $relativeFilePath;
                        } elseif (is_dir($filePath)) {
                            // 递归处理子目录
                            $subDirFiles = $this->getFilesRecursive($filePath, $relativeFilePath);
                            $files = array_merge($files, $subDirFiles);
                        }
                    }
                }
                closedir($handle);
            }
        }
        
        return $files;
    }
    
    /**
     * 更新进度信息
     */
    protected function updateProgress(array $data)
    {
        try {
        $progress = Cache::get($this->progressKey, []);
        
            // 如果缓存为空，重新初始化进度数据
            if (empty($progress)) {
                \think\facade\Log::warning('进度缓存数据为空，正在重新初始化');
                $progress = [
                    'total' => 0,
                    'processed' => 0,
                    'percentage' => 0,
                    'currentFile' => '',
                    'finished' => false,
                    'success' => false,
                    'logs' => ['进度数据已重新初始化...']
                ];
            }
            
            // 合并数据
            foreach ($data as $key => $value) {
                if ($key === 'logs' && !empty($progress['logs'])) {
                    // 日志特殊处理，合并而不是替换
                    if (is_array($value)) {
                    $progress['logs'] = array_merge($progress['logs'], array_diff($value, $progress['logs']));
                    } else {
                        $progress['logs'][] = $value;
                    }
                } else {
                    $progress[$key] = $value;
                }
            }
            
            // 添加当前时间戳，便于调试
            $progress['last_update'] = date('Y-m-d H:i:s');
            
            // 保存更新后的进度数据
            $result = Cache::set($this->progressKey, $progress, 3600);
            if (!$result) {
                \think\facade\Log::error('更新进度缓存失败: ' . json_encode($progress));
            }
            
            return $progress;
        } catch (\Exception $e) {
            \think\facade\Log::error('更新进度数据异常: ' . $e->getMessage());
            return false;
        }
    }
} 