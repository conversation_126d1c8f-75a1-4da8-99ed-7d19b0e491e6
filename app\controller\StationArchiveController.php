<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\facade\Db;
use think\file\UploadedFile;

class StationArchiveController extends \app\BaseController
{
    /**
     * 获取电站档案列表
     */
    public function index($id)
    {
        try {
            $archives = Db::name('station_archive')
                ->where('station_id', $id)
                ->where('is_deleted', 0) // 只查询未删除的档案
                ->order('create_at', 'desc')
                ->select()
                ->map(function ($item) {
                    // 生成完整的文件URL
                    $item['url'] = '/uploads/' . $item['file_path'];
                    // 判断文件类型
                    $extension = strtolower(pathinfo($item['file_path'], PATHINFO_EXTENSION));
                    $item['type'] = in_array($extension, ['jpg', 'jpeg', 'png']) ? 'image' : 'pdf';
                    return $item;
                });
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $archives]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 上传档案文件
     */
    public function upload(Request $request, $id)
    {
        try {
            // 记录请求信息
            \think\facade\Log::info('开始处理文件上传请求', [
                'station_id' => $id,
                'post_data' => $request->post(),
                'files' => $request->file()
            ]);

            $file = $request->file('file');
            $name = $request->param('name');

            if (!$file) {
                \think\facade\Log::error('未接收到上传文件');
                return json(['code' => 400, 'message' => '请选择文件']);
            }

            // 验证文件是否有效
            if (!$file->isValid()) {
                \think\facade\Log::error('文件无效', [
                    'error' => $file->getErrorMessage()
                ]);
                return json(['code' => 400, 'message' => '文件无效：' . $file->getErrorMessage()]);
            }

            // 获取文件大小（添加错误处理）
            try {
                $fileSize = $file->getSize();
                if ($fileSize === false || $fileSize === 0) {
                    throw new \Exception('无法获取文件大小');
                }
            } catch (\Exception $e) {
                \think\facade\Log::error('获取文件大小失败', [
                    'error' => $e->getMessage()
                ]);
                return json(['code' => 400, 'message' => '文件上传失败：无法获取文件大小']);
            }

            // 记录文件信息
            \think\facade\Log::info('接收到文件', [
                'original_name' => $file->getOriginalName(),
                'mime_type' => $file->getMime(),
                'size' => $fileSize,
                'custom_name' => $name,
                'temp_path' => $file->getPathname()
            ]);

            // 检查文件类型
            $fileType = $file->getMime();
            $allowTypes = ['image/webp','image/jpeg', 'image/png', 'application/pdf'];
            if (!in_array($fileType, $allowTypes)) {
                \think\facade\Log::warning('文件类型不允许'. '|type:'. $fileType, ['type' => $fileType]);
                return json(['code' => 400, 'message' => '只支持JPG、PNG和PDF文件']);
            }

            // 生成存储路径
            $savePath = root_path() . 'public/uploads/archive/' . date('Y/m/d');
            if (!is_dir($savePath)) {
                \think\facade\Log::info('创建存储目录', ['path' => $savePath]);
                if (!mkdir($savePath, 0777, true)) {
                    \think\facade\Log::error('创建存储目录失败', ['path' => $savePath]);
                    return json(['code' => 500, 'message' => '创建存储目录失败']);
                }
            }

            // 生成文件名
            $fileName = md5(uniqid((string)mt_rand(), true)) . '.' . $file->getOriginalExtension();
            \think\facade\Log::info('生成新文件名', ['filename' => $fileName]);

            try {
                // 移动文件
                $file->move($savePath, $fileName);
                $relativePath = 'archive/' . date('Y/m/d') . '/' . $fileName;
                \think\facade\Log::info('文件移动成功', [
                    'save_path' => $savePath,
                    'relative_path' => $relativePath
                ]);
            } catch (\Exception $e) {
                \think\facade\Log::error('文件移动失败', [
                    'error' => $e->getMessage(),
                    'save_path' => $savePath,
                    'filename' => $fileName
                ]);
                return json(['code' => 500, 'message' => '文件保存失败：' . $e->getMessage()]);
            }

            // 保存档案记录
            try {
                $data = [
                    'station_id' => $id,
                    'name' => $name,
                    'file_path' => $relativePath,
                    'file_size' => $fileSize,
                    'file_type' => $fileType,
                    'create_at' => date('Y-m-d H:i:s')
                ];
                \think\facade\Log::info('准备保存数据库记录', $data);

                $result = Db::name('station_archive')->insert($data);
                if (!$result) {
                    \think\facade\Log::error('数据库记录保存失败');
                    return json(['code' => 500, 'message' => '保存档案记录失败']);
                }
                \think\facade\Log::info('数据库记录保存成功');

            } catch (\Exception $e) {
                \think\facade\Log::error('保存数据库记录异常', ['error' => $e->getMessage()]);
                return json(['code' => 500, 'message' => '保存档案记录失败：' . $e->getMessage()]);
            }

            \think\facade\Log::info('文件上传处理完成');
            return json(['code' => 200, 'message' => '上传成功']);

        } catch (\Exception $e) {
            \think\facade\Log::error('文件上传处理异常', ['error' => $e->getMessage()]);
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除档案文件（软删除）
     */
    public function delete($stationId, $archiveId)
    {
        try {
            $archive = Db::name('station_archive')
                ->where('station_id', $stationId)
                ->where('id', $archiveId)
                ->where('is_deleted', 0) // 只查找未删除的记录
                ->find();

            if (!$archive) {
                return json(['code' => 404, 'message' => '档案文件不存在']);
            }

            // 软删除：设置 is_deleted = 1
            $result = Db::name('station_archive')
                ->where('id', $archiveId)
                ->update([
                    'is_deleted' => 1,
                    'reserve_time' => date('Y-m-d H:i:s') // 记录删除时间
                ]);

            if (!$result) {
                return json(['code' => 500, 'message' => '删除数据库记录失败']);
            }

            // 注意：不再删除物理文件，保留文件以便恢复
            // 如果需要定期清理，可以通过定时任务处理已软删除的文件

            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取文件类型
     */
    private function getFileType($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png']) ? 'image' : 'pdf';
    }
}