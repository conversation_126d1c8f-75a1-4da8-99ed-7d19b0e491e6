<?php
declare (strict_types = 1);
namespace app\service\ticket;

use think\facade\Db;
use app\service\BaseService;
use app\controller\TicketNew;

class ListService extends BaseService
{
    /**
     * 获取待办工单列表
     * @param array $params 查询参数
     * @param int $userId 当前用户ID
     * @param bool $isAdmin 是否为管理员
     * @return array
     */
    /**
 * 检查是否为管理员
 */
    private function isAdmin()
    {
        $roleIds = session('role_ids');
        trace('roleIds: ' . json_encode($roleIds), 'debug');
        
        // 如果roleIds不存在，尝试使用其他方法判断
        if (empty($roleIds)) {
            // 尝试从roles数组中判断是否包含超级管理员
            $roles = session('roles');
            if (is_array($roles) && in_array('超级管理员', $roles)) {
                return true;
            }
            
            // 或者尝试从数据库实时查询用户角色
            $userId = session('user_id');
            if ($userId) {
                $roleIds = Db::name('user_role')
                    ->where('user_id', $userId)
                    ->column('role_id');
                    
                trace('从数据库查询的roleIds: ' . json_encode($roleIds), 'debug');
                return is_array($roleIds) && in_array(1, $roleIds);
            }
        }
        
        return is_array($roleIds) && in_array(1, $roleIds);
    }
    public function getTodoList($params, $userId, $isAdmin = false)
    {
        // 构建基础查询条件
        $where = [
            ['status', '=', 0]  // 待处理的工单
        ];
        $isAdmin = $this->isAdmin();
        trace('isAdmin: ' . $isAdmin, 'debug');
        // 如果不是管理员，只查询自己创建的或自己需要处理的工单
        if (!$isAdmin) {
            // 查询用户参与处理的所有工单ID
            $userTicketIds = Db::name('ticket_handler')
                ->where('handler_id', $userId)
                ->column('ticket_id');
                
            // 构建查询条件：自己创建的或者自己需要处理的
            $query = Db::name('ticket')->where('status', 0);
            $query->where(function($query) use ($userId, $userTicketIds) {
                // 自己创建的工单
                $query->where('creator_id', $userId);
                // 自己需要处理的工单
                if (!empty($userTicketIds)) {
                    $query->whereOr('id', 'in', $userTicketIds);
                }
            });
            
            // 获取符合条件的工单ID
            $ticketIds = $query->column('id');
            
            if (!empty($ticketIds)) {
                $where[] = ['id', 'in', $ticketIds];
            } else {
                // 如果没有工单，设置一个不可能满足的条件
                $where[] = ['id', '=', 0];
            }
        }
        
        return $this->getTicketList($params, $where);
    }
    
    /**
     * 获取已办工单列表
     * @param array $params 查询参数
     * @param int $userId 当前用户ID
     * @param bool $isAdmin 是否为管理员
     * @return array
     */
    public function getDoneList($params, $userId, $isAdmin = false)
    {
        $where = [
            ['status', '=', 2],  // 已完成的工单
        ];
        
        // 如果不是管理员，只查询自己创建的或自己处理的工单
        if (!$isAdmin) {
            // 查询用户参与处理的所有工单ID
            $userTicketIds = Db::name('ticket_handler')
                ->where('handler_id', $userId)
                ->column('ticket_id');
                
            // 构建查询条件：自己创建的或者自己处理的
            $query = Db::name('ticket')->where('status', 2);
            $query->where(function($query) use ($userId, $userTicketIds) {
                // 自己创建的工单
                $query->where('creator_id', $userId);
                // 自己处理的工单
                if (!empty($userTicketIds)) {
                    $query->whereOr('id', 'in', $userTicketIds);
                }
            });
            
            // 获取符合条件的工单ID
            $ticketIds = $query->column('id');
            
            if (!empty($ticketIds)) {
                $where[] = ['id', 'in', $ticketIds];
            } else {
                // 如果没有工单，设置一个不可能满足的条件
                $where[] = ['id', '=', 0];
            }
        }
        
        return $this->getTicketList($params, $where);
    }
    
    /**
     * 获取在办工单列表
     * @param array $params 查询参数
     * @param int $userId 当前用户ID
     * @param bool $isAdmin 是否为管理员
     * @return array
     */
    public function getProcessingList($params, $userId, $isAdmin = false)
    {
        // 构建基础查询条件
        $where = [
            ['status', '=', 1]  // 在办工单状态为1
        ];
        
        // 如果不是管理员，只查询自己创建的或自己处理的工单
        if (!$isAdmin) {
            // 查询用户参与处理的所有工单ID
            $userTicketIds = Db::name('ticket_handler')
                ->where('handler_id', $userId)
                ->column('ticket_id');
                
            // 构建查询条件：自己创建的或者自己处理的
            $query = Db::name('ticket')->where('status', 1);
            $query->where(function($query) use ($userId, $userTicketIds) {
                // 自己创建的工单
                $query->where('creator_id', $userId);
                // 自己处理的工单
                if (!empty($userTicketIds)) {
                    $query->whereOr('id', 'in', $userTicketIds);
                }
            });
            
            // 获取符合条件的工单ID
            $ticketIds = $query->column('id');
            
            if (!empty($ticketIds)) {
                $where[] = ['id', 'in', $ticketIds];
            } else {
                // 如果没有找到任何工单，设置一个不可能满足的条件
                $where[] = ['id', '=', 0];
            }
        }
        
        return $this->getTicketList($params, $where);
    }
    
    /**
     * 获取工单列表通用方法
     * @param array $params 查询参数
     * @param array $where 查询条件
     * @return array
     */
    private function getTicketList($params, $where = [])
    {
        // 解析参数
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $limit = isset($params['limit']) ? intval($params['limit']) : 10;
        $keyword = isset($params['keyword']) ? trim($params['keyword']) : '';
        $priority = isset($params['priority']) ? trim($params['priority']) : '';
        $categoryId = isset($params['category_id']) ? intval($params['category_id']) : 0;
        $tagId = isset($params['tag_id']) ? intval($params['tag_id']) : 0;
        $departmentId = isset($params['department_id']) ? intval($params['department_id']) : 0;
        $startDate = isset($params['start_date']) ? trim($params['start_date']) : '';
        $endDate = isset($params['end_date']) ? trim($params['end_date']) : '';
        $sortField = isset($params['sort_field']) ? trim($params['sort_field']) : 'create_at';
        $sortOrder = isset($params['sort_order']) ? trim($params['sort_order']) : 'desc';
        
        // 关键词搜索
        if ($keyword) {
            $where[] = function ($query) use ($keyword) {
                $query->where('title', 'like', "%{$keyword}%")
                      ->whereOr('ticket_no', 'like', "%{$keyword}%")
                      ->whereOr('content', 'like', "%{$keyword}%");
            };
        }
        
        // 优先级筛选
        if ($priority) {
            $where[] = ['priority', '=', $priority];
        }
        
        // 分类筛选
        if ($categoryId) {
            $where[] = ['category_id', '=', $categoryId];
        }
        
        // 部门筛选
        if ($departmentId) {
            $where[] = ['department_id', '=', $departmentId];
        }
        
        // 日期范围筛选
        if ($startDate && $endDate) {
            $where[] = ['create_at', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        } else if ($startDate) {
            $where[] = ['create_at', '>=', $startDate . ' 00:00:00'];
        } else if ($endDate) {
            $where[] = ['create_at', '<=', $endDate . ' 23:59:59'];
        }
        
        // 构建查询对象
        $query = Db::name('ticket');
        
        // 应用查询条件
        foreach ($where as $condition) {
            if (is_array($condition) && count($condition) === 3) {
                $query->where($condition[0], $condition[1], $condition[2]);
            } else if (is_callable($condition)) {
                $query->where($condition);
            }
        }
        
        // 标签筛选（需要单独处理，因为是多对多关系）
        if ($tagId) {
            $query->whereExists(function ($exist) use ($tagId) {
                $exist->table('sp_ticket_tag_relation')
                    ->where('ticket_id', '=', Db::raw('sp_ticket.id'))
                    ->where('tag_id', '=', $tagId);
            });
        }
        
        // 获取总记录数
        $total = $query->count();
        
        // 验证排序字段（防止SQL注入）
        $allowedSortFields = ['create_at', 'priority', 'expect_time', 'handlers_count', 'transfer_count'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'create_at';
        }
        
        // 验证排序方向
        $sortOrder = strtolower($sortOrder) === 'asc' ? 'asc' : 'desc';
        
        // 查询数据
        $list = $query->page($page, $limit)
            ->order($sortField, $sortOrder)
            ->select()
            ->each(function($item) {
                // 获取标签数据
                $item['tags'] = Db::name('ticket_tag_relation')
                    ->alias('r')
                    ->join('ticket_tag t', 'r.tag_id = t.id')
                    ->where('r.ticket_id', $item['id'])
                    ->field('t.id, t.name, t.color')
                    ->select();
                
                // 获取处理人数据
                $item['handlers'] = Db::name('ticket_handler')
                    ->where('ticket_id', $item['id'])
                    ->field('handler_id, handler_name, status')
                    ->select();
                
                // 处理日期格式
                if (!empty($item['create_at'])) {
                    $item['create_at_formatted'] = date('Y-m-d H:i', strtotime($item['create_at']));
                }
                
                // 优先级格式化
                switch ($item['priority']) {
                    case 'low':
                        $item['priority_text'] = '低';
                        $item['priority_color'] = 'success';
                        break;
                    case 'medium':
                        $item['priority_text'] = '中';
                        $item['priority_color'] = 'warning';
                        break;
                    case 'high':
                        $item['priority_text'] = '高';
                        $item['priority_color'] = 'danger';
                        break;
                    default:
                        $item['priority_text'] = '未知';
                        $item['priority_color'] = 'info';
                }
                
                return $item;
            });
        
        return [
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'items' => $list,
                'total' => $total
            ]
        ];
    }

    /**
     * 获取工单详情
     * @param int $ticketId 工单ID
     * @param int $userId 当前用户ID
     * @param bool $isAdmin 是否为管理员
     * @return array
     */
    public function getTicketDetail($ticketId, $userId, $isAdmin = false)
    {
        try {
            // 日志记录
            trace('获取工单详情 - 工单ID: ' . $ticketId . ', 用户ID: ' . $userId . ', 是否管理员: ' . ($isAdmin ? 'true' : 'false'), 'debug');
            
            // 查询工单基本信息
            $ticket = Db::name('ticket')->where('id', $ticketId)->find();
            
            if (!$ticket) {
                return [
                    'code' => 404,
                    'message' => '工单不存在',
                    'data' => null
                ];
            }
            
            // 如果不是管理员，需要检查权限
            if (!$isAdmin) {
                // 检查是否为创建者或处理者
                $isCreator = ($ticket['creator_id'] == $userId);
                $isHandler = Db::name('ticket_handler')
                    ->where('ticket_id', $ticketId)
                    ->where('handler_id', $userId)
                    ->count() > 0;
                
                if (!$isCreator && !$isHandler) {
                    return [
                        'code' => 403,
                        'message' => '您没有权限查看此工单',
                        'data' => null
                    ];
                }
            }
            
            // 获取工单创建者信息
            $creator = Db::name('user')
                ->field('id, username, realname, department_id, mobile, email')
                ->where('id', $ticket['creator_id'])
                ->find();
            
            // 获取工单分类信息
            $category = Db::name('ticket_category')
                ->field('id, name, parent_id, code')
                ->where('id', $ticket['category_id'])
                ->find();
            
            // 获取工单处理人列表
            $handlers = Db::name('ticket_handler')
                ->alias('th')
                ->join('user u', 'th.handler_id = u.id')
                ->field('th.id as handler_id, th.handler_id as user_id, th.status as handler_status, th.feedback, th.create_at, th.update_at, th.complete_at, th.accepted_at, th.handling_time, u.username, u.realname, u.department_id')
                ->where('th.ticket_id', $ticketId)
                ->select()
                ->toArray();
            
            // 为每个处理人添加必要信息
            foreach ($handlers as &$handler) {
                // 默认头像为空
                $handler['avatar'] = '';
                $handler['name'] = $handler['realname'] ?: $handler['username'];
                
                // 添加状态描述
                switch ($handler['handler_status']) {
                    case 0:
                        $handler['status_text'] = '待处理';
                        $handler['status_color'] = 'warning';
                        break;
                    case 1:
                        $handler['status_text'] = '处理中';
                        $handler['status_color'] = 'primary';
                        break;
                    case 2:
                        $handler['status_text'] = '已完成';
                        $handler['status_color'] = 'success';
                        break;
                    default:
                        $handler['status_text'] = '未知状态';
                        $handler['status_color'] = 'info';
                }
                
                // 获取处理人的部门信息
                if ($handler['department_id']) {
                    $dept = Db::name('department')
                        ->field('id, name')
                        ->where('id', $handler['department_id'])
                        ->find();
                    $handler['department_name'] = $dept ? $dept['name'] : '';
                } else {
                    $handler['department_name'] = '';
                }
            }
            
            // 获取工单附件
            $attachments = Db::name('ticket_attachment')
                ->field('id, name as file_name, path as file_path, size as file_size, type as file_type, create_at')
                ->where('ticket_id', $ticketId)
                ->select()
                ->toArray();
            
            // 为附件添加下载链接
            foreach ($attachments as &$attachment) {
                $attachment['download_url'] = url('ticket_new/download', ['id' => $attachment['id']])->build();
                // 确保文件名存在
                if (empty($attachment['file_name']) && !empty($attachment['file_path'])) {
                    $pathParts = explode('/', $attachment['file_path']);
                    $attachment['file_name'] = end($pathParts);
                }
            }
            
            // 获取部门名称
            if ($creator && $creator['department_id']) {
                $department = Db::name('department')
                    ->field('id, name')
                    ->where('id', $creator['department_id'])
                    ->find();
                $creator['department_name'] = $department ? $department['name'] : '';
            }
            
            // 组装返回数据
            $data = [
                'id' => $ticket['id'],
                'ticket_no' => $ticket['ticket_no'],
                'title' => $ticket['title'],
                'content' => $ticket['content'],
                'priority' => $ticket['priority'],
                'status' => $ticket['status'],
                'category_id' => $ticket['category_id'],
                'category_name' => $category ? $category['name'] : '',
                'creator_id' => $ticket['creator_id'],
                'creator_name' => $creator ? ($creator['realname'] ?: $creator['username']) : '',
                'create_at' => $ticket['create_at'],
                'update_at' => $ticket['update_at'],
                'expect_time' => isset($ticket['expect_time']) ? $ticket['expect_time'] : null,
                'department_id' => $creator ? $creator['department_id'] : 0,
                'department_name' => isset($creator['department_name']) ? $creator['department_name'] : '',
                'handlers' => $handlers,
                'attachments' => $attachments,
                'feedback' => $ticket['feedback']
            ];
            
            // 添加当前用户是否为处理人的标志
            $data['is_handler'] = false;
            foreach ($handlers as $handler) {
                if ($handler['user_id'] == $userId) {
                    $data['is_handler'] = true;
                    $data['handler_status'] = $handler['handler_status'];
                    break;
                }
            }
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $data
            ];
            
        } catch (\Exception $e) {
            trace('获取工单详情异常: ' . $e->getMessage(), 'error');
            return [
                'code' => 500,
                'message' => '获取工单详情失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
} 