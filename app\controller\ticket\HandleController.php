<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\service\ticket\HandleService;
use think\facade\Request;

class HandleController extends BaseTicketController
{
    protected $handleService;
    
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->handleService = new HandleService();
    }
    
    /**
     * 接受工单处理
     * @param int $id 工单ID
     */
    public function accept($id)
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            
            // 接受工单
            $result = $this->handleService->accept($id, $userId);
            
            return json($result);
        } catch (\Exception $e) {
            trace('接受工单异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 拒绝工单处理
     * @param int $id 工单ID
     */
    public function reject($id)
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            
            // 获取拒绝原因
            $reason = input('post.reason', '');
            if (empty($reason)) {
                return json(['code' => 400, 'message' => '请填写拒绝原因']);
            }
            
            // 拒绝工单
            $result = $this->handleService->reject($id, $userId, $reason);
            
            return json($result);
        } catch (\Exception $e) {
            trace('拒绝工单异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 完成工单处理
     * @param int $id 工单ID
     */
    public function complete($id)
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            
            // 获取处理备注
            $remark = input('post.remark', '');
            
            // 完成工单
            $result = $this->handleService->complete($id, $userId, $remark);
            
            return json($result);
        } catch (\Exception $e) {
            trace('完成工单异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
} 