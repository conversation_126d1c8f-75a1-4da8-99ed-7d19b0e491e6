class OperationLog
{
    public function handle($request, \Closure $next)
    {
        $response = $next($request);
        
        // 记录财务相关操作
        if (strpos($request->pathinfo(), 'finance/') === 0) {
            Db::name('sp_operation_log')->insert([
                'user_id' => session('user_id'),
                'module' => 'finance',
                'action' => $request->pathinfo(),
                'params' => json_encode($request->param()),
                'ip' => $request->ip(),
                'create_time' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $response;
    }
}
