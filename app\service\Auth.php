<?php
declare (strict_types = 1);

namespace app\service;

use app\model\User;
use think\facade\Cache;
use think\facade\Request;

class Auth
{
    /**
     * 获取当前用户
     */
    public function user()
    {
        $token = Request::header('Authorization');
        if (!$token) {
            return null;
        }

        $userInfo = Cache::get('token_' . $token);
        if (!$userInfo) {
            return null;
        }
        $userId = $userInfo['id'];
        return User::find($userId);
    }

    /**
     * 检查是否已登录
     */
    public function check()
    {
        return $this->user() !== null;
    }

    /**
     * 检查权限
     */
    public function can($permission)
    {
        $user = $this->user();
        if (!$user) {
            return false;
        }

        // 获取用户所有角色的权限
        $permissions = [];
        foreach ($user->roles as $role) {
            foreach ($role->permissions as $perm) {
                $permissions[$perm->code] = true;
            }
        }

        return isset($permissions[$permission]);
    }

    /**
     * 检查角色
     */
    public function hasRole($role)
    {
        $user = $this->user();
        if (!$user) {
            return false;
        }

        foreach ($user->roles as $userRole) {
            if ($userRole->code === $role) {
                return true;
            }
        }

        return false;
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        $token = Request::header('Authorization');
        if ($token) {
            Cache::delete('token_' . $token);
            return true;
        }
        return false;
    }
} 