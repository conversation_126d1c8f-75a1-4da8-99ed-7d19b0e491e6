<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Department;
use think\facade\Db;

class DepartmentService extends BaseService
{
    /**
     * 获取部门列表
     */
    public function getList()
    {
        try {
            $departments = Department::select();
            return $this->success($departments);
        } catch (\Exception $e) {
            return $this->error('获取部门列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取部门树
     */
    public function getTree()
    {
        try {
            $tree = Department::getTree();
            return $this->success($tree);
        } catch (\Exception $e) {
            return $this->error('获取部门树失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建部门
     */
    public function create($data)
    {
        try {
            $department = new Department;
            $department->name = $data['name'];
            $department->code = $data['code'];
            $department->parent_id = $data['parent_id'] ?? 0;
            $department->sort = $data['sort'] ?? 0;
            $department->save();

            return $this->success($department, '部门创建成功');
        } catch (\Exception $e) {
            return $this->error('部门创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新部门
     */
    public function update($id, $data)
    {
        try {
            $department = Department::find($id);
            if (!$department) {
                return $this->error('部门不存在');
            }

            // 检查是否将部门移动到其子部门下
            if (isset($data['parent_id']) && $data['parent_id'] != 0) {
                $children = Department::getTree($id);
                $childrenIds = array_column($this->flattenTree($children), 'id');
                if (in_array($data['parent_id'], $childrenIds)) {
                    return $this->error('不能将部门移动到其子部门下');
                }
            }

            $department->name = $data['name'] ?? $department->name;
            $department->parent_id = $data['parent_id'] ?? $department->parent_id;
            $department->sort = $data['sort'] ?? $department->sort;
            $department->save();

            return $this->success($department, '部门更新成功');
        } catch (\Exception $e) {
            return $this->error('部门更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除部门
     */
    public function delete($id)
    {
        try {
            $department = Department::find($id);
            if (!$department) {
                return $this->error('部门不存在');
            }

            // 检查是否有子部门
            if (Department::where('parent_id', $id)->count() > 0) {
                return $this->error('请先删除子部门');
            }

            // 检查是否有用户
            if ($department->users()->count() > 0) {
                return $this->error('请先移除该部门下的用户');
            }

            $department->delete();
            return $this->success(null, '部门删除成功');
        } catch (\Exception $e) {
            return $this->error('部门删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 扁平化部门树
     */
    private function flattenTree($tree)
    {
        $result = [];
        foreach ($tree as $node) {
            $item = $node;
            unset($item['children']);
            $result[] = $item;
            if (isset($node['children'])) {
                $result = array_merge($result, $this->flattenTree($node['children']));
            }
        }
        return $result;
    }

    /**
     * 获取带有用户信息的部门列表
     */
    public function getDepartmentsWithUsers()
    {
        try {
            // 获取所有部门
            $departments = Department::select()->toArray();
            
            // 获取所有用户并按部门分组
            $users = \app\model\User::where('status', 1)
                ->select()
                ->toArray();
            
            // 将用户添加到对应的部门中
            foreach ($departments as &$dept) {
                $dept['users'] = [];
                
                foreach ($users as $user) {
                    if ($user['department_id'] == $dept['id']) {
                        $dept['users'][] = $user;
                    }
                }
                
                // 按照用户姓名排序
                usort($dept['users'], function($a, $b) {
                    $nameA = $a['realname'] ?: $a['username'];
                    $nameB = $b['realname'] ?: $b['username'];
                    return strcmp($nameA, $nameB);
                });
            }
            
            // 按照 sort 字段和 id 字段排序部门
            usort($departments, function($a, $b) {
                if ($a['sort'] == $b['sort']) {
                    return $a['id'] - $b['id'];
                }
                return $a['sort'] - $b['sort'];
            });
            
            return $departments;
        } catch (\Exception $e) {
            trace('获取部门和用户数据失败: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }
} 