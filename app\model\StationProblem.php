<?php
namespace app\model;

use think\Model;

class StationProblem extends Model
{
    protected $name = 'station_problem';
    protected $pk = 'id';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'register_time';
    protected $updateTime = 'update_time';

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'station_id' => 'integer',
        'handler_id' => 'integer',
        'register_time' => 'datetime',
        'update_time' => 'datetime'
    ];

    /**
     * 关联电站
     */
    public function station()
    {
        return $this->belongsTo(Station::class, 'station_id', 'id');
    }

    /**
     * 关联处理人
     */
    public function handler()
    {
        return $this->belongsTo(User::class, 'handler_id', 'id');
    }

    // 添加全局查询范围，筛选未删除记录
    public static function onBeforeQuery($query)
    {
        $query->where('is_deleted', 0);
    }
} 