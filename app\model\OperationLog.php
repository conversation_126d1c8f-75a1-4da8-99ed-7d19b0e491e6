<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class OperationLog extends Model
{
    protected $name = 'operation_log';
    protected $prefix = 'sp_';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = false;

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'user_id'     => 'int',
        'username'    => 'string',
        'module'      => 'string',
        'action'      => 'string',
        'method'      => 'string',
        'url'         => 'string',
        'params'      => 'string',
        'ip'          => 'string',
        'user_agent'  => 'string',
        'create_at'  => 'datetime'
    ];
} 