<?php
declare (strict_types = 1);

namespace app\middleware;

use think\Exception;

class CheckPermission
{
    /**
     * 权限检查中间件
     */
    public function handle($request, \Closure $next, $permission)
    {
        // 获取用户权限
        $userPermissions = $request->userPermissions ?? [];
        
        // 检查是否有权限
        if (!in_array($permission, $userPermissions)) {
            throw new Exception('没有操作权限');
        }
        
        return $next($request);
    }
} 