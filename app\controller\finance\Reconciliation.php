<?php
declare(strict_types=1);

namespace app\controller\finance;

use think\facade\Request;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use app\BaseController;
use app\model\Station;
use think\facade\Validate;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Reconciliation extends BaseController
{
    /**
     * 获取资金对账列表
     */
    public function getList()
    {
        $page = input('page', 1, 'intval');
        $pageSize = input('pageSize', 10, 'intval');
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $month = input('month', '', 'trim');
        $status = input('status', '', 'trim');

        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($month)) {
            $where[] = ['a.reconcile_month', '=', $month];
        }
        
        if (!empty($status)) {
            $where[] = ['a.status', '=', $status];
        }
        
        try {
            // 获取数据总数
            $total = Db::table('sp_fund_reconciliation')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->count();
            
            // 查询数据
            $list = Db::table('sp_fund_reconciliation')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.reconcile_month DESC, a.id DESC')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return $this->success([
                'items' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取资金对账列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取对账详情
     */
    public function getDetail($id)
    {
        try {
            // 获取对账记录
            $reconciliation = Db::table('sp_fund_reconciliation')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where('a.id', $id)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->find();
            
            if (empty($reconciliation)) {
                return $this->error('记录不存在');
            }
            
            // 获取当月归集明细
            $collectionDetails = Db::table('sp_fund_collection_detail')
                ->where('station_id', $reconciliation['station_id'])
                ->whereRaw("CONCAT(collection_year, '-', LPAD(collection_month, 2, '0')) = ?", [$reconciliation['reconcile_month']])
                ->order('collection_date DESC')
                ->select()
                ->toArray();
            
            // 获取当月国网打款明细
            $gridDetails = Db::table('sp_fund_state_grid')
                ->where('station_id', $reconciliation['station_id'])
                ->whereRaw("DATE_FORMAT(payment_date, '%Y-%m') = ?", [$reconciliation['reconcile_month']])
                ->order('payment_date DESC')
                ->select()
                ->toArray();
            
            // 获取调整记录
            $adjustments = Db::table('sp_fund_adjustment')
                ->where('reconciliation_id', $id)
                ->order('create_time DESC')
                ->select()
                ->toArray();
            
            return $this->success([
                'reconciliation' => $reconciliation,
                'collectionDetails' => $collectionDetails,
                'gridDetails' => $gridDetails,
                'adjustments' => $adjustments
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取对账详情失败：' . $e->getMessage());
        }
    }
    
    /**
     * 手动调整对账
     */
    public function adjustReconciliation()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = Validate::rule([
            'reconciliation_id' => 'require|number',
            'adjustment_type' => 'require|in:归集,国网',
            'amount' => 'require|float',
            'reason' => 'require'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 获取对账记录
            $reconciliation = Db::table('sp_fund_reconciliation')
                ->where('id', $params['reconciliation_id'])
                ->find();
            
            if (empty($reconciliation)) {
                return $this->error('对账记录不存在');
            }
            
            // 计算调整前后的金额
            $collectedAmount = $reconciliation['collected_amount'];
            $gridAmount = $reconciliation['grid_amount'];
            $beforeDifference = $reconciliation['difference'];
            
            if ($params['adjustment_type'] == '归集') {
                $collectedAmount += $params['amount'];
            } else { // 国网
                $gridAmount += $params['amount'];
            }
            
            // 计算新差额
            $afterDifference = $collectedAmount - $gridAmount;
            
            // 状态判断
            $status = '正常';
            if (abs($afterDifference) > 0.01) { // 考虑浮点数比较，使用一个很小的阈值
                $status = $afterDifference > 0 ? '多收' : '少收';
            }
            
            Db::startTrans();
            
            // 记录调整
            $adjustmentData = [
                'reconciliation_id' => $params['reconciliation_id'],
                'adjustment_type' => $params['adjustment_type'],
                'amount' => $params['amount'],
                'reason' => $params['reason'],
                'before_difference' => $beforeDifference,
                'after_difference' => $afterDifference,
                'operator' => $this->getUserInfo('name') ?? 'system',
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            Db::table('sp_fund_adjustment')->insert($adjustmentData);
            
            // 更新对账记录
            $reconciliationData = [
                'collected_amount' => $collectedAmount,
                'grid_amount' => $gridAmount,
                'difference' => $afterDifference,
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            Db::table('sp_fund_reconciliation')
                ->where('id', $params['reconciliation_id'])
                ->update($reconciliationData);
            
            Db::commit();
            
            return $this->success('调整成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('调整失败：' . $e->getMessage());
        }
    }
    
    /**
     * 重新计算所有对账信息
     */
    public function recalculateAll()
    {
        try {
            // 获取需要检查的电站和月份组合
            $months = input('months', '');
            
            if (empty($months)) {
                // 如果未指定月份，默认重新计算最近3个月的数据
                $startMonth = date('Y-m', strtotime('-2 month'));
                $endMonth = date('Y-m');
                
                // 生成月份数组
                $months = [];
                $currentMonth = $startMonth;
                
                while ($currentMonth <= $endMonth) {
                    $months[] = $currentMonth;
                    // 计算下一个月
                    $currentMonth = date('Y-m', strtotime($currentMonth . '-01 +1 month'));
                }
            } else {
                $months = explode(',', $months);
            }
            
            if (empty($months)) {
                return $this->error('未指定有效的月份');
            }
            
            // 获取所有电站
            $stations = Station::field('id')->select()->toArray();
            
            $success = 0;
            $fail = 0;
            
            foreach ($stations as $station) {
                foreach ($months as $month) {
                    if ($this->calculateReconciliation($station['id'], $month)) {
                        $success++;
                    } else {
                        $fail++;
                    }
                }
            }
            
            return $this->success([
                'success' => $success,
                'fail' => $fail,
                'total' => count($stations) * count($months)
            ], '重新计算完成');
        } catch (\Exception $e) {
            return $this->error('重新计算失败：' . $e->getMessage());
        }
    }
    
    /**
     * 计算某个电站某月的对账
     * 
     * @param int $stationId 电站ID
     * @param string $month 月份（格式：yyyy-mm）
     * @return bool 是否计算成功
     */
    private function calculateReconciliation($stationId, $month)
    {
        try {
            // 获取该月的国网打款总额
            $gridAmount = Db::table('sp_fund_state_grid')
                ->where('station_id', $stationId)
                ->whereRaw("DATE_FORMAT(payment_date, '%Y-%m') = ?", [$month])
                ->sum('amount');
            
            // 获取该月的资金归集总额
            $collectionAmount = Db::table('sp_fund_collection_detail')
                ->where('station_id', $stationId)
                ->whereRaw("CONCAT(collection_year, '-', LPAD(collection_month, 2, '0')) = ?", [$month])
                ->sum('amount');
            
            // 如果没有任何交易记录，则跳过
            if ($gridAmount == 0 && $collectionAmount == 0) {
                return true;
            }
            
            // 计算差额
            $difference = $collectionAmount - $gridAmount;
            
            // 状态判断
            $status = '正常';
            if (abs($difference) > 0.01) { // 考虑浮点数比较，使用一个很小的阈值
                $status = $difference > 0 ? '多收' : '少收';
            }
            
            // 检查是否存在对账记录
            $reconciliation = Db::table('sp_fund_reconciliation')
                ->where('station_id', $stationId)
                ->where('reconcile_month', $month)
                ->find();
            
            // 准备数据
            $data = [
                'collected_amount' => $collectionAmount,
                'grid_amount' => $gridAmount,
                'difference' => $difference,
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            if ($reconciliation) {
                // 更新对账记录
                Db::table('sp_fund_reconciliation')
                    ->where('id', $reconciliation['id'])
                    ->update($data);
            } else {
                // 创建新对账记录
                $data['station_id'] = $stationId;
                $data['reconcile_month'] = $month;
                $data['create_time'] = date('Y-m-d H:i:s');
                
                Db::table('sp_fund_reconciliation')->insert($data);
            }
            
            return true;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('对账计算失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 导出对账数据
     */
    public function export()
    {
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $month = input('month', '', 'trim');
        $status = input('status', '', 'trim');
        
        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($month)) {
            $where[] = ['a.reconcile_month', '=', $month];
        }
        
        if (!empty($status)) {
            $where[] = ['a.status', '=', $status];
        }
        
        try {
            // 查询数据
            $list = Db::table('sp_fund_reconciliation')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.reconcile_month DESC, a.id DESC')
                ->select()
                ->toArray();
            
            // 创建电子表格
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $sheet->setCellValue('A1', '总序号');
            $sheet->setCellValue('B1', '国网号');
            $sheet->setCellValue('C1', '户主名称');
            $sheet->setCellValue('D1', '对账月份');
            $sheet->setCellValue('E1', '归集金额');
            $sheet->setCellValue('F1', '国网金额');
            $sheet->setCellValue('G1', '差额');
            $sheet->setCellValue('H1', '状态');
            
            // 填充数据
            $row = 2;
            foreach ($list as $item) {
                $sheet->setCellValue('A' . $row, $item['total_serial']);
                $sheet->setCellValue('B' . $row, $item['gongwang_account']);
                $sheet->setCellValue('C' . $row, $item['contact_name']);
                $sheet->setCellValue('D' . $row, $item['reconcile_month']);
                $sheet->setCellValue('E' . $row, $item['collected_amount']);
                $sheet->setCellValue('F' . $row, $item['grid_amount']);
                $sheet->setCellValue('G' . $row, $item['difference']);
                $sheet->setCellValue('H' . $row, $item['status']);
                $row++;
            }
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(12);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(12);
            $sheet->getColumnDimension('D')->setWidth(12);
            $sheet->getColumnDimension('E')->setWidth(12);
            $sheet->getColumnDimension('F')->setWidth(12);
            $sheet->getColumnDimension('G')->setWidth(12);
            $sheet->getColumnDimension('H')->setWidth(10);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="资金对账数据_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取当前登录用户信息
     * 
     * @param string $field 要获取的字段
     * @return mixed 用户信息
     */
    private function getUserInfo($field = null)
    {
        $user = session('user');
        if (empty($user)) {
            return null;
        }
        
        if ($field !== null) {
            return isset($user[$field]) ? $user[$field] : null;
        }
        
        return $user;
    }
    
    /**
     * 获取月度对账统计
     * 按月份统计对账数据
     */
    public function monthlyStats()
    {
        try {
            $year = input('year', date('Y'), 'intval');
            
            // 构建12个月的月份数组
            $months = [];
            for ($i = 1; $i <= 12; $i++) {
                $months[] = [
                    'month' => sprintf('%s-%02d', $year, $i),
                    'balanced_count' => 0,
                    'unbalanced_count' => 0
                ];
            }
            
            // 查询数据库获取每月的对账状态统计
            $stats = Db::table('sp_fund_reconciliation')
                ->whereRaw("LEFT(reconcile_month, 4) = ?", [$year])
                ->field("
                    reconcile_month as month,
                    SUM(CASE WHEN status = 'balanced' OR status = '正常' THEN 1 ELSE 0 END) as balanced_count,
                    SUM(CASE WHEN status = 'unbalanced' OR status = '多收' OR status = '少收' THEN 1 ELSE 0 END) as unbalanced_count
                ")
                ->group('reconcile_month')
                ->select()
                ->toArray();
            
            // 将查询结果合并到月份数组中
            foreach ($stats as $stat) {
                $month = $stat['month'];
                foreach ($months as &$item) {
                    if ($item['month'] === $month) {
                        $item['balanced_count'] = intval($stat['balanced_count']);
                        $item['unbalanced_count'] = intval($stat['unbalanced_count']);
                        break;
                    }
                }
            }
            
            return $this->success($months, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取月度统计失败：' . $e->getMessage());
        }
    }
} 