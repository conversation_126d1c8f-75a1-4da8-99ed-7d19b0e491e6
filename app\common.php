<?php
// 应用公共文件

/**
 * 导出Excel文件
 */
function download_excel($data, $filename)
{
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // 写入数据
    $sheet->fromArray($data);
    
    // 输出Excel文件
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
    $writer->save('php://output');
    exit;
}

/**
 * 增强的日志记录函数，包含文件名
 * @param string $message 日志信息
 * @param string $type 日志级别
 * @param array $context 上下文信息
 * @return void
 */
function log_with_file($message, $type = 'info', $context = [])
{
    // 获取调用者信息
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $caller = isset($backtrace[1]) ? $backtrace[1] : [];
    $file = isset($caller['file']) ? basename($caller['file']) : 'unknown';
    
    // 将文件名作为消息的一部分
    \think\facade\Log::$type("[{$file}] {$message}", $context);
}

/**
 * 重写trace函数，添加文件名
 * @param string $message 日志信息
 * @param string $type 日志级别
 * @return void
 */
function trace($message, $type = 'debug')
{
    // 获取调用者信息
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $caller = isset($backtrace[0]) ? $backtrace[0] : [];
    $file = isset($caller['file']) ? basename($caller['file']) : 'unknown';
    
    // 将文件名作为消息的一部分，而不是作为格式参数
    \think\facade\Log::record("[{$file}] {$message}", $type);
}
