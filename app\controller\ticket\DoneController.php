<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\service\ticket\ListService;
use think\facade\Request;

class DoneController extends BaseTicketController
{
    protected $listService;
    
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->listService = new ListService();
    }
    
    /**
     * 获取已办工单列表
     */
    public function index()
    {
        try {
            trace('开始获取已办工单列表', 'debug');
            
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取请求参数
            $params = [
                'page' => input('page/d', 1),
                'limit' => input('limit/d', 10),
                'keyword' => input('keyword/s', ''),
                'priority' => input('priority/s', ''),
                'category_id' => input('category_id/d', 0),
                'tag_id' => input('tag_id/d', 0),
                'department_id' => input('department_id/d', 0),
                'start_date' => input('start_date/s', ''),
                'end_date' => input('end_date/s', ''),
                'sort_field' => input('sort_field/s', 'create_at'),
                'sort_order' => input('sort_order/s', 'desc')
            ];
            
            // 记录查询参数
            trace('已办工单列表查询参数: ' . json_encode($params), 'debug');
            
            // 获取当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            
            // 获取已办工单列表
            $result = $this->listService->getDoneList($params, $userId, $isAdmin);
            
            trace('查询完成，返回' . count($result['data']['items']) . '条记录', 'debug');
            
            return json($result);
        } catch (\Exception $e) {
            trace('获取已办工单列表异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 导出已办工单
     */
    public function export()
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取查询参数
            $params = [
                'keyword' => input('keyword/s', ''),
                'priority' => input('priority/s', ''),
                'category_id' => input('category_id/d', 0),
                'tag_id' => input('tag_id/d', 0),
                'department_id' => input('department_id/d', 0),
                'start_date' => input('start_date/s', ''),
                'end_date' => input('end_date/s', ''),
                // 导出时不分页
                'page' => 1,
                'limit' => 1000,
                'sort_field' => 'create_at',
                'sort_order' => 'desc'
            ];
            
            // 获取当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            
            // 获取已办工单列表（不分页）
            $result = $this->listService->getDoneList($params, $userId, $isAdmin);
            $list = $result['data']['items'];
            
            if (empty($list)) {
                return json(['code' => 400, 'message' => '没有数据可导出']);
            }
            
            // 准备导出数据
            $excelData = [];
            $header = ['工单编号', '标题', '分类', '优先级', '创建时间', '完成时间', '创建人', '处理人'];
            $excelData[] = $header;
            
            foreach ($list as $item) {
                // 获取处理人列表
                $handlers = [];
                if (!empty($item['handlers'])) {
                    foreach ($item['handlers'] as $handler) {
                        $handlers[] = $handler['handler_name'];
                    }
                }
                
                $priorityMap = [
                    'low' => '低',
                    'medium' => '中',
                    'high' => '高'
                ];
                
                // 完成时间格式化
                $completedAt = '';
                if (!empty($item['completed_at'])) {
                    $completedAt = date('Y-m-d H:i', strtotime($item['completed_at']));
                }
                
                $row = [
                    $item['ticket_no'] ?? '',
                    $item['title'] ?? '',
                    $item['category_name'] ?? '',
                    $priorityMap[$item['priority']] ?? '未知',
                    $item['create_at_formatted'] ?? '',
                    $completedAt,
                    $item['creator_name'] ?? '',
                    implode(', ', $handlers)
                ];
                
                $excelData[] = $row;
            }
            
            // 创建Excel文件
            $date = date('YmdHis');
            $filename = "已办工单_{$date}.xlsx";
            $excelHelper = new \app\common\ExcelHelper();
            $filePath = $excelHelper->exportExcel($excelData, $filename);
            
            // 返回文件下载地址
            $downloadUrl = url('common.download/excel', ['filename' => basename($filePath)])->build();
            
            return json([
                'code' => 200,
                'message' => '导出成功',
                'data' => [
                    'url' => $downloadUrl
                ]
            ]);
        } catch (\Exception $e) {
            trace('导出工单异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '导出失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
} 