<?php
namespace app\controller\finance;

use app\BaseController;
use think\facade\Db;
use app\model\Station;
use think\facade\Validate;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\facade\Log;

class FundCollection extends BaseController
{
    /**
     * 获取资金归集列表
     */
    public function getList()
    {
        $page = input('page', 1, 'intval');
        $pageSize = input('pageSize', 10, 'intval');
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $collectionType = input('collection_type', '', 'trim');

        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.collection_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.collection_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.collection_date', '<=', $endDate];
        }
        
        if (!empty($collectionType)) {
            $where[] = ['a.collection_type', '=', $collectionType];
        }
        
        try {
            // 获取数据总数
            $total = Db::table('sp_fund_collection_detail')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->count();
            
            // 查询数据
            $list = Db::table('sp_fund_collection_detail')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.collection_date DESC, a.id DESC')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return $this->success([
                'items' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取资金归集列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加资金归集记录
     */
    public function save()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'collection_date' => 'require|date',
            'collection_type' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 获取电站信息
            $station = Station::where('id', $params['station_id'])->find();
            if (empty($station)) {
                return $this->error('电站不存在');
            }
            
            // 分解日期
            $dateInfo = date_parse($params['collection_date']);
            
            // 准备数据
            $data = [
                'station_id' => $params['station_id'],
                'collection_date' => $params['collection_date'],
                'collection_year' => $dateInfo['year'],
                'collection_month' => $dateInfo['month'],
                'collection_day' => $dateInfo['day'],
                'collection_type' => $params['collection_type'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            Db::startTrans();
            
            // 插入明细记录
            $detailId = Db::table('sp_fund_collection_detail')->insertGetId($data);
            
            // 更新或创建月度汇总
            $this->updateMonthlyTotal($params['station_id'], $dateInfo['year'], $dateInfo['month']);
            
            Db::commit();
            
            return $this->success('添加成功', ['id' => $detailId]);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('添加失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新资金归集记录
     */
    public function update($id)
    {
        $params = $this->request->put();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'collection_date' => 'require|date',
            'collection_type' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 获取原记录
            $oldRecord = Db::table('sp_fund_collection_detail')->where('id', $id)->find();
            if (empty($oldRecord)) {
                return $this->error('记录不存在');
            }
            
            // 分解日期
            $dateInfo = date_parse($params['collection_date']);
            
            // 准备数据
            $data = [
                'collection_date' => $params['collection_date'],
                'collection_year' => $dateInfo['year'],
                'collection_month' => $dateInfo['month'],
                'collection_day' => $dateInfo['day'],
                'collection_type' => $params['collection_type'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            Db::startTrans();
            
            // 更新明细记录
            Db::table('sp_fund_collection_detail')->where('id', $id)->update($data);
            
            // 更新旧月份的汇总
            $this->updateMonthlyTotal($oldRecord['station_id'], $oldRecord['collection_year'], $oldRecord['collection_month']);
            
            // 如果月份发生变化，更新新月份的汇总
            if ($oldRecord['collection_year'] != $dateInfo['year'] || $oldRecord['collection_month'] != $dateInfo['month']) {
                $this->updateMonthlyTotal($params['station_id'], $dateInfo['year'], $dateInfo['month']);
            }
            
            Db::commit();
            
            return $this->success('更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除资金归集记录
     */
    public function delete($id)
    {
        try {
            // 获取原记录
            $record = Db::table('sp_fund_collection_detail')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            Db::startTrans();
            
            // 删除记录
            Db::table('sp_fund_collection_detail')->where('id', $id)->delete();
            
            // 更新月度汇总
            $this->updateMonthlyTotal($record['station_id'], $record['collection_year'], $record['collection_month']);
            
            Db::commit();
            
            return $this->success('删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新月度汇总表
     */
    private function updateMonthlyTotal($stationId, $year, $month)
    {
        // 查询该月的各种类型金额
        $result = Db::table('sp_fund_collection_detail')
            ->where('station_id', $stationId)
            ->where('collection_year', $year)
            ->where('collection_month', $month)
            ->field([
                'sum(CASE WHEN collection_type = "pos" THEN amount ELSE 0 END) as pos_amount',
                'sum(CASE WHEN collection_type = "临商" THEN amount ELSE 0 END) as linshang_amount',
                'sum(CASE WHEN collection_type = "国网" THEN amount ELSE 0 END) as guowang_amount',
                'sum(CASE WHEN collection_type = "存现" THEN amount ELSE 0 END) as cash_amount',
                'sum(CASE WHEN collection_type = "其他" THEN amount ELSE 0 END) as other_amount',
                'sum(amount) as total_amount'
            ])
            ->find();
        
        // 检查是否已有月度记录
        $monthlyRecord = Db::table('sp_fund_collection_monthly')
            ->where('station_id', $stationId)
            ->where('collection_year', $year)
            ->where('collection_month', $month)
            ->find();
        
        $monthlyData = [
            'pos_amount' => $result['pos_amount'] ?? 0,
            'linshang_amount' => $result['linshang_amount'] ?? 0,
            'guowang_amount' => $result['guowang_amount'] ?? 0,
            'cash_amount' => $result['cash_amount'] ?? 0,
            'other_amount' => $result['other_amount'] ?? 0,
            'total_amount' => $result['total_amount'] ?? 0,
        ];
        
        if ($monthlyRecord) {
            // 更新现有记录
            Db::table('sp_fund_collection_monthly')
                ->where('id', $monthlyRecord['id'])
                ->update($monthlyData);
        } else {
            // 创建新记录
            $monthlyData['station_id'] = $stationId;
            $monthlyData['collection_year'] = $year;
            $monthlyData['collection_month'] = $month;
            $monthlyData['create_time'] = date('Y-m-d H:i:s');
            
            Db::table('sp_fund_collection_monthly')->insert($monthlyData);
        }
    }
    
    /**
     * 导入资金归集数据
     */
    public function import()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请上传文件');
        }
        
        // 验证文件类型
        $fileExt = $file->getOriginalExtension();
        if (!in_array($fileExt, ['xlsx', 'xls'])) {
            return $this->error('仅支持 Excel 文件导入');
        }
        
        try {
            // 获取文件内容
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // 移除表头
            array_shift($rows);
            
            // 导入结果统计
            $success = 0;
            $fail = 0;
            $errors = [];
            
            Db::startTrans();
            
            foreach ($rows as $index => $row) {
                // 根据实际Excel模板调整字段索引
                $totalSerial = trim($row[0] ?? '');
                $gongwangAccount = trim($row[1] ?? '');  // 户号，对应国网号
                $collectionDate = trim($row[2] ?? '');
                $collectionType = trim($row[3] ?? '');
                $amount = trim($row[4] ?? '');
                $remark = trim($row[5] ?? '');
                
                // 数据验证和处理
                if (empty($totalSerial) || empty($gongwangAccount) || empty($collectionDate) || 
                    empty($collectionType) || !is_numeric($amount)) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行数据不完整或格式错误";
                    continue;
                }
                
                // 查找电站ID
                $station = Station::where('total_serial', $totalSerial)
                    ->where('gongwang_account', $gongwangAccount)
                    ->find();
                
                if (!$station) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行: 找不到匹配的电站信息，户号: {$gongwangAccount}, 总序号: {$totalSerial}";
                    continue;
                }
                
                try {
                    // 处理日期格式
                    if (!strtotime($collectionDate)) {
                        $fail++;
                        $errors[] = "第" . ($index + 2) . "行: 日期格式错误 '{$collectionDate}'";
                        continue;
                    }
                    
                    // 标准化日期格式
                    $formattedDate = date('Y-m-d', strtotime($collectionDate));
                    $dateInfo = date_parse($formattedDate);
                    
                    // 检查归集方式是否在允许的值范围内
                    $allowedTypes = ['pos', '临商', '国网', '存现', '其他'];
                    if (!in_array($collectionType, $allowedTypes)) {
                        $fail++;
                        $errors[] = "第" . ($index + 2) . "行: 归集方式不合法 '{$collectionType}'";
                        continue;
                    }
                    
                    // 准备数据
                    $data = [
                        'station_id' => $station['id'],
                        'collection_date' => $formattedDate,
                        'collection_year' => $dateInfo['year'],
                        'collection_month' => $dateInfo['month'],
                        'collection_day' => $dateInfo['day'],
                        'collection_type' => $collectionType,
                        'amount' => floatval($amount),
                        'remark' => $remark,
                        'create_time' => date('Y-m-d H:i:s')
                    ];
                    
                    // 插入记录
                    Db::table('sp_fund_collection_detail')->insert($data);
                    
                    // 更新月度汇总
                    $this->updateMonthlyTotal($station['id'], $dateInfo['year'], $dateInfo['month']);
                    
                    $success++;
                } catch (\Exception $e) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行: " . $e->getMessage();
                }
            }
            
            Db::commit();
            
            return $this->success([
                'success' => $success,
                'fail' => $fail,
                'errors' => $errors
            ], '导入完成');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导出资金归集数据
     */
    public function export()
    {
        $contactName = input('contact_name', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $collectionType = input('collection_type', '', 'trim');
        
        $where = [];
        
        // 构建查询条件 - 使用联系人姓名搜索
        if (!empty($contactName)) {
            $where[] = ['d.contact_name', 'like', "%{$contactName}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.collection_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.collection_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.collection_date', '<=', $endDate];
        }
        
        if (!empty($collectionType)) {
            $where[] = ['a.collection_type', '=', $collectionType];
        }
        
        try {
            // 查询数据
            $list = Db::table('sp_fund_collection_detail')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.collection_date DESC, a.id DESC')
                ->select()
                ->toArray();
            
            // 创建电子表格
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $sheet->setCellValue('A1', '总序号');
            $sheet->setCellValue('B1', '户号');
            $sheet->setCellValue('C1', '归集日期');
            $sheet->setCellValue('D1', '归集方式');
            $sheet->setCellValue('E1', '归集金额');
            $sheet->setCellValue('F1', '备注');
            
            // 填充数据
            $row = 2;
            foreach ($list as $item) {
                $sheet->setCellValue('A' . $row, $item['total_serial']);
                $sheet->setCellValue('B' . $row, $item['gongwang_account']);
                $sheet->setCellValue('C' . $row, $item['collection_date']);
                $sheet->setCellValue('D' . $row, $item['collection_type']);
                $sheet->setCellValue('E' . $row, $item['amount']);
                $sheet->setCellValue('F' . $row, $item['remark']);
                $row++;
            }
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(12);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(12);
            $sheet->getColumnDimension('D')->setWidth(10);
            $sheet->getColumnDimension('E')->setWidth(12);
            $sheet->getColumnDimension('F')->setWidth(25);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="资金归集数据_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取导入模板
     */
    public function template()
    {
        try {
            $spreadsheet = new Spreadsheet();
            
            // 创建数据录入工作表
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('资金归集数据');
            
            // 设置表头
            $sheet->setCellValue('A1', '总序号');
            $sheet->setCellValue('B1', '户号');
            $sheet->setCellValue('C1', '归集日期');
            $sheet->setCellValue('D1', '归集方式');
            $sheet->setCellValue('E1', '归集金额');
            $sheet->setCellValue('F1', '备注');
            
            // 添加示例数据行
            $sheet->setCellValue('A2', 'LS0001');
            $sheet->setCellValue('B2', '10200003');
            $sheet->setCellValue('C2', '2023-06-01');
            $sheet->setCellValue('D2', 'pos');
            $sheet->setCellValue('E2', '1000.00');
            $sheet->setCellValue('F2', '例行扣款');
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(12);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(12);
            $sheet->getColumnDimension('D')->setWidth(10);
            $sheet->getColumnDimension('E')->setWidth(12);
            $sheet->getColumnDimension('F')->setWidth(25);
            
            // 创建说明工作表
            $guideSheet = $spreadsheet->createSheet();
            $guideSheet->setTitle('填表说明');
            
            // 添加表格与数据库字段对应关系
            $guideSheet->setCellValue('A1', '导入表格字段与数据库对应关系说明');
            $guideSheet->mergeCells('A1:E1');
            $guideSheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $guideSheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // 添加表头
            $guideSheet->setCellValue('A3', '序号');
            $guideSheet->setCellValue('B3', '导入表格列名');
            $guideSheet->setCellValue('C3', '对应数据库字段');
            $guideSheet->setCellValue('D3', '字段说明');
            $guideSheet->setCellValue('E3', '填写要求');
            
            // 添加对应关系数据
            $guideSheet->setCellValue('A4', '1');
            $guideSheet->setCellValue('B4', '总序号');
            $guideSheet->setCellValue('C4', 'total_serial (sp_station表)');
            $guideSheet->setCellValue('D4', '电站的唯一编号');
            $guideSheet->setCellValue('E4', '必填，必须与系统中的电站总序号一致');
            
            $guideSheet->setCellValue('A5', '2');
            $guideSheet->setCellValue('B5', '户号');
            $guideSheet->setCellValue('C5', 'gongwang_account (sp_station表)');
            $guideSheet->setCellValue('D5', '电站在系统中的国网户号');
            $guideSheet->setCellValue('E5', '必填，必须与系统中的电站国网户号一致');
            
            $guideSheet->setCellValue('A6', '3');
            $guideSheet->setCellValue('B6', '归集日期');
            $guideSheet->setCellValue('C6', 'collection_date (sp_fund_collection_detail表)');
            $guideSheet->setCellValue('D6', '资金归集的日期');
            $guideSheet->setCellValue('E6', '必填，格式：YYYY-MM-DD');
            
            $guideSheet->setCellValue('A7', '4');
            $guideSheet->setCellValue('B7', '归集方式');
            $guideSheet->setCellValue('C7', 'collection_type (sp_fund_collection_detail表)');
            $guideSheet->setCellValue('D7', '资金归集的方式');
            $guideSheet->setCellValue('E7', '必填，可选值：pos、临商、国网、存现、其他');
            
            $guideSheet->setCellValue('A8', '5');
            $guideSheet->setCellValue('B8', '归集金额');
            $guideSheet->setCellValue('C8', 'amount (sp_fund_collection_detail表)');
            $guideSheet->setCellValue('D8', '归集的金额');
            $guideSheet->setCellValue('E8', '必填，数字格式');
            
            $guideSheet->setCellValue('A9', '6');
            $guideSheet->setCellValue('B9', '备注');
            $guideSheet->setCellValue('C9', 'remark (sp_fund_collection_detail表)');
            $guideSheet->setCellValue('D9', '额外说明信息');
            $guideSheet->setCellValue('E9', '选填');
            
            // 添加注意事项
            $guideSheet->setCellValue('A11', '注意事项：');
            $guideSheet->setCellValue('A12', '1. 总序号和户号必须与系统中已存在的电站信息匹配，否则导入将失败');
            $guideSheet->setCellValue('A13', '2. 归集日期格式必须为YYYY-MM-DD，例如2023-01-01');
            $guideSheet->setCellValue('A14', '3. 归集方式必须是以下之一：pos、临商、国网、存现、其他');
            $guideSheet->setCellValue('A15', '4. 金额必须为数字，可以包含小数点');
            $guideSheet->setCellValue('A16', '5. 请勿修改表格的格式和结构');
            
            // 设置列宽
            $guideSheet->getColumnDimension('A')->setWidth(8);
            $guideSheet->getColumnDimension('B')->setWidth(15);
            $guideSheet->getColumnDimension('C')->setWidth(30);
            $guideSheet->getColumnDimension('D')->setWidth(25);
            $guideSheet->getColumnDimension('E')->setWidth(35);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="资金归集导入模板.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 输出文件
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('获取模板失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导入自定义格式的资金归集Excel
     */
    public function importCustomFormat()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请上传文件');
        }
        
        // 记录详细的请求信息
        Log::info('开始处理资金归集导入请求', [
            'file_info' => [
                'name' => $file->getOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getMime(),
                'extension' => $file->getOriginalExtension()
            ],
            'request_data' => $this->request->param()
        ]);
        
        // 验证文件类型
        $fileExt = $file->getOriginalExtension();
        if (!in_array($fileExt, ['xlsx', 'xls'])) {
            return $this->error('仅支持 Excel 文件导入');
        }
        
        try {
            // 获取文件内容
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            
            // 直接获取所有单元格值，不使用toArray避免类型转换问题
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            
            // 提取标题行
            $title = $worksheet->getCellByColumnAndRow(1, 1)->getValue();
            $departmentInfo = '';
            $yearInfo = '';
            
            // 从标题中提取部门和年份
            if (preg_match('/^(.*?)(\d{4})年/', $title, $matches)) {
                $departmentInfo = $matches[1];
                $yearInfo = $matches[2];
            } else {
                // 如果无法提取，使用默认值
                $departmentInfo = '未知部门';
                $yearInfo = date('Y');
            }
            
            // 获取表头(第二行)
            $headers = [];
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $headers[$col] = $worksheet->getCellByColumnAndRow($col, 2)->getValue();
            }
            
            // 记录前5行数据用于调试
            $sampleRows = [];
            for ($row = 1; $row <= min(5, $highestRow); $row++) {
                $rowData = [];
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                    $cellType = $worksheet->getCellByColumnAndRow($col, $row)->getDataType();
                    $rowData[$col] = [
                        'value' => $cellValue,
                        'type' => $cellType
                    ];
                }
                $sampleRows[$row] = $rowData;
            }
            // 使用json_encode避免数组转字符串错误
            Log::info('Excel文件前5行内容: ' . json_encode($sampleRows, JSON_UNESCAPED_UNICODE));
            
            // 查找归集月份和方式的列索引
            $collectionIndexes = [];
            foreach ($headers as $index => $header) {
                // 查找形如"2月pos"的列
                if (preg_match('/^(\d+)月(pos|临商|国网|存现)$/i', $header, $matches)) {
                    $month = $matches[1];
                    $type = $matches[2];
                    $collectionIndexes[$index] = [
                        'month' => $month,
                        'type' => $type
                    ];
                }
            }
            
            // 调试：记录找到的所有列头
            $debugInfo = "表头列数: " . count($headers) . ", 列头内容: " . json_encode($headers, JSON_UNESCAPED_UNICODE);
            Log::debug('资金归集导入 - ' . $debugInfo);
            
            // 调试：记录找到的归集索引
            $debugCollection = "找到的归集列: " . json_encode($collectionIndexes, JSON_UNESCAPED_UNICODE);
            Log::debug('资金归集导入 - ' . $debugCollection);
            
            // 额外检查第20-23列，这些列可能包含特定格式的归集数据
            for ($col = 20; $col <= 23; $col++) {
                if (isset($headers[$col]) && !isset($collectionIndexes[$col])) {
                    $header = $headers[$col];
                    // 尝试其他格式，如"2月pos扣款"、"2月临商银行"等
                    if (preg_match('/^(\d+)月(pos|临商|国网|存现|其他).*$/i', $header, $matches)) {
                        $month = $matches[1];
                        $type = $matches[2];
                        $collectionIndexes[$col] = [
                            'month' => $month,
                            'type' => $type
                        ];
                        Log::debug('资金归集导入 - 额外找到归集列: ' . $col . ' => ' . $header . ', 月份: ' . $month . ', 类型: ' . $type);
                    }
                }
            }
            
            // 如果没有找到任何归集列，尝试基于固定列索引添加
            if (empty($collectionIndexes)) {
                for ($col = 20; $col <= 23; $col++) {
                    if (isset($headers[$col])) {
                        // 假设20-23列是按月份顺序排列的
                        $month = $col - 19; // 20对应1月，21对应2月，依此类推
                        $type = '临商'; // 默认类型
                        
                        // 尝试从列头推断类型
                        $header = strtolower($headers[$col]);
                        if (strpos($header, 'pos') !== false) {
                            $type = 'pos';
                        } else if (strpos($header, '临商') !== false || strpos($header, '银行') !== false) {
                            $type = '临商';
                        } else if (strpos($header, '国网') !== false) {
                            $type = '国网';
                        } else if (strpos($header, '现金') !== false || strpos($header, '存现') !== false) {
                            $type = '存现';
                        } else if (strpos($header, '其他') !== false) {
                            $type = '其他';
                        }
                        
                        $collectionIndexes[$col] = [
                            'month' => $month,
                            'type' => $type
                        ];
                        Log::debug('资金归集导入 - 强制添加归集列: ' . $col . ' => ' . $headers[$col] . ', 推断月份: ' . $month . ', 推断类型: ' . $type);
                    }
                }
            }
            
            // 检查数据表结构
            $this->checkTableFields();
            
            // 导入结果统计
            $success = 0;
            $fail = 0;
            $errors = [];
            $stationUpdated = 0;
            $stationCreated = 0;
            $collectionsCreated = 0;
            
            Db::startTrans();
            
            // 从第三行开始处理数据
            for ($rowIndex = 3; $rowIndex <= $highestRow; $rowIndex++) {
                // 创建一个空的行数据数组
                $rowData = [];
                
                // 获取当前行的所有单元格值
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $rowData[$col] = $worksheet->getCellByColumnAndRow($col, $rowIndex)->getValue();
                }
                
                try {
                    // 获取各字段的值，保持原始类型
                    $arcId = trim($rowData[1] ?? '');  // 序号
                    $county = trim($rowData[2] ?? '');  // 县区
                    $town = trim($rowData[3] ?? '');  // 乡镇
                    $village = trim($rowData[4] ?? '');  // 村名
                    $address = trim($rowData[5] ?? '');  // 安装地址
                    $contactName = trim($rowData[6] ?? '');  // 户名 = 联系人姓名/station_name
                    $gongwangAccount = trim($rowData[7] ?? '');  // 户号 = 国网号
                    $idCard = trim($rowData[8] ?? '');  // 客户身份证号
                    $contactPhone = trim($rowData[9] ?? '');  // 联系电话
                    $componentCount = trim($rowData[10] ?? '');  // 验收表格上点过的块数
                    $productPrice = trim($rowData[11] ?? '');  // A11 产品价格
                    $capacity = trim($rowData[12] ?? '');  // A12 装机容量
                    // A13 收益计算 - 不需要
                    $archiveStatus = trim($rowData[14] ?? '');  // A14 档案接收情况
                    $bankCardStatus = trim($rowData[15] ?? '');  // A15 接收的银行卡
                    $electricityCard = trim($rowData[16] ?? '');  // A16 电费卡卡号
                    $bankName = trim($rowData[17] ?? '');  // A17 开户行
                    $bankCard = trim($rowData[18] ?? '');  // A18 最新卡号
                    $bankBranch = trim($rowData[19] ?? '');  // A19 开户行
                    $cardStatus = trim($rowData[24] ?? '');  // A24 电站电费卡状态
                    $remark = trim($rowData[25] ?? '');  // A25 备注
                    $cardOpenTime = trim($rowData[26] ?? '');  // A26 开卡时间
                    $gridContractStatus = trim($rowData[27] ?? '');  // A27 国网签约
                    
                    // 数据验证
                    if (empty($gongwangAccount)) {
                        $fail++;
                        $errors[] = "第{$rowIndex}行: 户号(国网号)不能为空";
                        continue;
                    }
                    
                    // 查找或创建电站
                    $station = Db::table('sp_station')->where('gongwang_account', $gongwangAccount)->find();
                    
                    // 准备电站数据
                    $stationData = [
                        'arc_id' => $arcId,
                        'county' => $county,
                        'town' => $town,
                        'village' => $village,
                        'address' => $address,
                        'contact_name' => $contactName,
                        'id_card' => $idCard,
                        'contact_phone' => $contactPhone,
                        'component_count' => $componentCount, // 使用正确的字段名称
                        'product_price' => $productPrice,
                        'capacity' => $capacity,
                        'archive_status' => $archiveStatus,
                        'bank_card_status' => $bankCardStatus,
                        'electricity_card' => $electricityCard,
                        'bank_name' => $bankName,
                        'bank_card' => $bankCard,
                        'bank_branch' => $bankBranch,
                        'card_status' => $cardStatus,
                        'card_open_time' => $cardOpenTime,
                        'grid_contract_status' => $gridContractStatus,
                        'update_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $stationId = 0;
                    if (!$station) {
                        // 创建新电站
                        $stationData['station_name'] = $contactName;
                        $stationData['gongwang_account'] = $gongwangAccount;
                        $stationData['create_at'] = date('Y-m-d H:i:s');
                        
                        $stationId = Db::table('sp_station')->insertGetId($stationData);
                        $stationCreated++;
                    } else {
                        // 更新现有电站
                        $stationId = $station['id'];
                        Db::table('sp_station')->where('id', $stationId)->update($stationData);
                        $stationUpdated++;
                    }
                    
                    // 处理归集信息
                    $hasCollection = false;
                    Log::debug("处理第{$rowIndex}行归集数据，国网户号: {$gongwangAccount}, 户名: {$contactName}");
                    
                    // 记录所有可能的金额数据
                    $amountData = [];
                    for ($col = 20; $col <= 23; $col++) {
                        if (isset($rowData[$col])) {
                            $amountData[$col] = $rowData[$col];
                        }
                    }
                    Log::debug("20-23列数据: " . json_encode($amountData, JSON_UNESCAPED_UNICODE));
                    
                    // 如果collectionIndexes为空但有金额数据，尝试强制创建一些归集记录
                    if (empty($collectionIndexes) && !empty($amountData)) {
                        Log::info("未找到标准归集列但有金额数据，尝试强制创建归集记录");
                        
                        // 为每个非空的金额列创建一个临时归集索引
                        foreach ($amountData as $col => $amount) {
                            if (!empty($amount) && (is_numeric($amount) || is_string($amount))) {
                                // 尝试格式化金额
                                if (!is_numeric($amount)) {
                                    $amount = preg_replace('/[^\d.]/', '', $amount);
                                }
                                
                                if (!empty($amount) && is_numeric($amount) && floatval($amount) > 0) {
                                    // 计算月份(基于列号)
                                    $month = $col - 19; // 20=1月，21=2月...
                                    if ($month < 1 || $month > 12) {
                                        $month = 1; // 默认为1月
                                    }
                                    
                                    $collectionIndexes[$col] = [
                                        'month' => $month,
                                        'type' => '临商' // 默认类型
                                    ];
                                    
                                    Log::info("强制创建归集列: 列{$col}，月份{$month}，金额{$amount}");
                                }
                            }
                        }
                    }
                    
                    foreach ($collectionIndexes as $colIndex => $info) {
                        // 使用原始索引获取单元格值
                        $amount = $rowData[$colIndex] ?? '';
                        Log::debug("检查列 {$colIndex}, 归集类型: {$info['type']}, 月份: {$info['month']}, 值: {$amount}");
                        
                        // 处理金额格式，移除非数字字符
                        if (!empty($amount) && !is_numeric($amount)) {
                            $originalAmount = $amount;
                            
                            // 处理可能的科学计数法格式 (如Excel中的大数字)
                            if (is_string($amount) && preg_match('/^\d+(\.\d+)?E\+\d+$/i', $amount)) {
                                $amount = (string)floatval($amount);
                            }
                            
                            // 处理带千分位分隔符的数字 (如1,234.56)
                            if (is_string($amount) && preg_match('/^\d{1,3}(,\d{3})+(\.\d+)?$/', $amount)) {
                                $amount = str_replace(',', '', $amount);
                            }
                            
                            // 尝试移除非数字字符，保留小数点
                            if (!is_numeric($amount)) {
                                $amount = preg_replace('/[^\d.]/', '', $amount);
                            }
                            
                            Log::debug("格式化金额 '{$originalAmount}' 为 '{$amount}'");
                        }
                        
                        // 只处理非空且是数字的值
                        if (!empty($amount) && is_numeric($amount) && floatval($amount) > 0) {
                            $month = $info['month'];
                            $type = $info['type'];
                            
                            // 详细记录有效金额数据
                            Log::info('发现有效金额数据: ' . json_encode([
                                'row' => $rowIndex,
                                'column' => $colIndex,
                                'amount' => $amount,
                                'month' => $month,
                                'type' => $type
                            ], JSON_UNESCAPED_UNICODE));
                            
                            // 构造日期 (使用年份和月份)
                            $collectionDate = sprintf('%s-%02d-15', $yearInfo, $month); // 默认使用每月15日
                            
                            // 查询该电站该月该类型是否已有记录
                            $existingRecord = Db::table('sp_fund_collection_detail')
                                ->where('station_id', $stationId)
                                ->where('collection_year', $yearInfo)
                                ->where('collection_month', $month)
                                ->where('collection_type', $type)
                                ->find();
                            
                            if (!$existingRecord) {
                                // 创建新归集记录
                                $collectionData = [
                                    'station_id' => $stationId,
                                    'collection_date' => $collectionDate,
                                    'collection_year' => $yearInfo,
                                    'collection_month' => $month,
                                    'collection_day' => 15, // 默认15号
                                    'collection_type' => $type,
                                    'amount' => $amount,
                                    'remark' => $remark,
                                    'create_time' => date('Y-m-d H:i:s')
                                ];
                                
                                Log::debug("插入新归集记录: 电站ID {$stationId}, 日期 {$collectionDate}, 类型 {$type}, 金额 {$amount}");
                                Db::table('sp_fund_collection_detail')->insert($collectionData);
                                $collectionsCreated++;
                                $hasCollection = true;
                            } else {
                                Log::debug("已存在归集记录，跳过: 电站ID {$stationId}, 年 {$yearInfo}, 月 {$month}, 类型 {$type}");
                            }
                        } else {
                            Log::debug("列 {$colIndex} 金额无效或为空: '{$amount}'");
                        }
                    }
                    
                    // 如果有归集记录则表示成功
                    if ($hasCollection) {
                        $success++;
                        Log::debug("第{$rowIndex}行处理成功，已创建归集记录");
                    } else {
                        $fail++;
                        $errors[] = "第{$rowIndex}行: 没有找到有效的归集金额数据";
                        Log::debug("第{$rowIndex}行处理失败，无有效归集金额数据");
                    }
                } catch (\Exception $e) {
                    $fail++;
                    $errors[] = "第{$rowIndex}行: " . $e->getMessage();
                }
            }
            
            // 在返回结果中添加调试信息
            $debugLogs = [];
            $logFile = runtime_path() . 'logs/fund_import_' . date('Ymd') . '.log';
            if (file_exists($logFile)) {
                $debugLogs = array_slice(file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES), -20);
            }
            
            Db::commit();
            
            return $this->success([
                'success' => $success,
                'fail' => $fail,
                'errors' => $errors,
                'stations_created' => $stationCreated,
                'stations_updated' => $stationUpdated,
                'collections_created' => $collectionsCreated,
                'department' => $departmentInfo,
                'year' => $yearInfo,
                'debug_logs' => $debugLogs  // 添加最近的日志记录
            ], '导入完成');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('资金归集导入异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 检查数据表字段是否存在，如不存在则添加
     */
    private function checkTableFields()
    {
        $tableInfo = Db::query('SHOW COLUMNS FROM sp_station');
        $existingFields = array_column($tableInfo, 'Field');
        
        // 定义可能需要添加的字段及其定义
        $requiredFields = [
            'component_count' => "ADD COLUMN `component_count` VARCHAR(50) COMMENT '组件数量' AFTER `panel_count`",
            'arc_id' => "ADD COLUMN `arc_id` VARCHAR(50) COMMENT '档案ID/序号' AFTER `id`",
            'county' => "ADD COLUMN `county` VARCHAR(100) COMMENT '县区' AFTER `address`",
            'town' => "ADD COLUMN `town` VARCHAR(100) COMMENT '乡镇' AFTER `county`",
            'village' => "ADD COLUMN `village` VARCHAR(100) COMMENT '村名' AFTER `town`",
            'product_price' => "ADD COLUMN `product_price` DECIMAL(10,2) COMMENT '产品价格' AFTER `capacity`",
            'archive_status' => "ADD COLUMN `archive_status` VARCHAR(50) COMMENT '档案接收情况' AFTER `product_price`",
            'bank_card_status' => "ADD COLUMN `bank_card_status` VARCHAR(50) COMMENT '银行卡接收状态' AFTER `archive_status`",
            'electricity_card' => "ADD COLUMN `electricity_card` VARCHAR(100) COMMENT '电费卡卡号' AFTER `bank_card_status`",
            'bank_name' => "ADD COLUMN `bank_name` VARCHAR(100) COMMENT '电费卡开户行' AFTER `electricity_card`",
            'bank_card' => "ADD COLUMN `bank_card` VARCHAR(100) COMMENT '最新银行卡号' AFTER `bank_name`",
            'bank_branch' => "ADD COLUMN `bank_branch` VARCHAR(100) COMMENT '银行卡开户行' AFTER `bank_card`",
            'card_status' => "ADD COLUMN `card_status` VARCHAR(50) COMMENT '电站电费卡状态' AFTER `bank_branch`",
            'card_open_time' => "ADD COLUMN `card_open_time` VARCHAR(50) COMMENT '开卡时间' AFTER `card_status`",
            'grid_contract_status' => "ADD COLUMN `grid_contract_status` VARCHAR(50) COMMENT '国网签约状态' AFTER `card_open_time`",
        ];
        
        // 检查并添加缺失的字段
        foreach ($requiredFields as $field => $alterSql) {
            if (!in_array($field, $existingFields)) {
                try {
                    Db::execute("ALTER TABLE `sp_station` $alterSql");
                } catch (\Exception $e) {
                    // 忽略字段已存在的错误
                    continue;
                }
            }
        }
    }
} 