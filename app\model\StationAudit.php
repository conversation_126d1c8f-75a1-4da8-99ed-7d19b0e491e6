<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class StationAudit extends Model
{
    protected $name = 'station_audit';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'station_id'  => 'int',
        'type'        => 'string',
        'content'     => 'text',
        'status'      => 'tinyint',
        'submitter_id' => 'int',
        'reviewer_id' => 'int',
        'approver_id' => 'int',
        'review_time' => 'datetime',
        'approve_time' => 'datetime',
        'create_at' => 'datetime',
        'update_at' => 'datetime',
    ];
    
    // 关联电站
    public function station()
    {
        return $this->belongsTo(Station::class);
    }
} 