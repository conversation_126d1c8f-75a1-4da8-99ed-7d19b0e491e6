<?php
declare (strict_types = 1);

namespace app\service;

use app\model\LoginLog;
use app\model\OperationLog;
use think\facade\Request;
use think\facade\Session;
use think\facade\Db;

class LogService extends BaseService
{
    /**
     * 记录登录日志
     */
    public static function recordLogin($userId, $username, $status, $message = '')
    {
        try {
            // 打印调试信息
            trace('开始记录登录日志 ' . $userId . ' ' . $username . ' ' . $status . ' ' . $message, 'debug');
            // echo "Recording login log...\n";
            // var_dump([
            //     'user_id' => $userId,
            //     'username' => $username,
            //     'status' => $status,
            //     'message' => $message
            // ]);

            // 准备数据
            $data = [
                'user_id' => intval($userId),
                'username' => strval($username),
                'ip' => Request::ip(),
                'user_agent' => Request::header('user-agent'),
                'status' => intval($status),
                'message' => strval($message),
                'create_at' => date('Y-m-d H:i:s')
            ];

            // 直接执行 SQL 插入
            $result = Db::table('sp_login_log')->insert($data);

            // 记录 SQL 和结果
            trace('Login log SQL: ' . Db::getLastSql(), 'debug');
            trace('Login log result: ' . var_export($result, true), 'debug');

            return $result;
        } catch (\Exception $e) {
            // 记录错误信息
            trace('Record login log error: ' . $e->getMessage(), 'error');
            trace('Error trace: ' . $e->getTraceAsString(), 'error');
            return false;
        }
    }

    /**
     * 记录操作日志
     */
    public static function recordOperation($module, $action, $params = [])
    {
        try {
            // 确保参数不为空
            $module = $module ?: 'unknown';
            $action = $action ?: 'unknown';
            
            // 调试日志
            trace('Recording operation log - Module: ' . $module . ', Action: ' . $action, 'debug');

            // 创建日志记录
            $log = new OperationLog;
            
            // 检查params中是否包含用户信息
            if (isset($params['_user_id']) && $params['_user_id'] > 0 && isset($params['_username']) && !empty($params['_username'])) {
                // 如果参数中包含有效的用户信息，则使用
                $log->user_id = intval($params['_user_id']);
                $log->username = strval($params['_username']);
                
                // 移除这些特殊字段，避免它们被保存到params中
                unset($params['_user_id']);
                unset($params['_username']);
                
                // 添加调试日志
                trace('Using user info from params - UserID: ' . $log->user_id . ', Username: ' . $log->username, 'debug');
            } else {
                // 如果params中没有有效的用户信息，先尝试从token缓存获取
                $token = str_replace('Bearer ', '', Request::header('Authorization'));
                if ($token) {
                    $userInfo = \think\facade\Cache::get('token_' . $token);
                    if ($userInfo && isset($userInfo['id']) && isset($userInfo['username'])) {
                        $log->user_id = intval($userInfo['id']);
                        $log->username = strval($userInfo['username']);
                        trace('Using user info from token cache in service - UserID: ' . $log->user_id . ', Username: ' . $log->username, 'debug');
                    } else {
                        // 最后才尝试从Session获取
                        $log->user_id = intval(Session::get('user_id', 0));
                        $log->username = strval(Session::get('username', ''));
                        trace('Using user info from session in service - UserID: ' . $log->user_id . ', Username: ' . $log->username, 'debug');
                    }
                } else {
                    // 如果没有token，只能从Session获取
                    $log->user_id = intval(Session::get('user_id', 0));
                    $log->username = strval(Session::get('username', ''));
                    trace('Using user info from session only in service - UserID: ' . $log->user_id . ', Username: ' . $log->username, 'debug');
                }
                
                // 如果用户ID有效但用户名为空，尝试从数据库获取
                if ($log->user_id > 0 && empty($log->username)) {
                    try {
                        $user = \app\model\User::where('id', $log->user_id)->find();
                        if ($user && !empty($user->username)) {
                            $log->username = $user->username;
                            trace('Retrieved username from database - Username: ' . $log->username, 'debug');
                        }
                    } catch (\Exception $e) {
                        trace('Error retrieving user from database: ' . $e->getMessage(), 'error');
                    }
                }
            }
            
            $log->module = strval($module);
            $log->action = strval($action);
            $log->method = strval(Request::method());
            $log->url = strval(Request::url(true));
            $log->params = is_array($params) ? json_encode($params, JSON_UNESCAPED_UNICODE) : strval($params);
            $log->ip = strval(Request::ip());
            $log->user_agent = strval(Request::header('user-agent'));

            // 记录要保存的数据
            trace('Operation log data: ' . json_encode($log->toArray(), JSON_UNESCAPED_UNICODE), 'debug');

            // 保存日志
            $result = $log->save();
            
            if (!$result) {
                trace('Failed to save operation log: ' . json_encode($log->getError()), 'error');
            }
            
            return $result;
        } catch (\Exception $e) {
            trace('Record operation log error: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return false;
        }
    }

    /**
     * 获取登录日志列表
     */
    public function getLoginLogs($params = [])
    {
        try {
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            $query = Db::name('login_log');

            // 应用搜索条件
            if (!empty($params['keyword'])) {
                $query->where('username|ip', 'like', "%{$params['keyword']}%");
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('status', intval($params['status']));
            }
            if (!empty($params['start_time'])) {
                $query->whereTime('create_at', '>=', $params['start_time']);
            }
            if (!empty($params['end_time'])) {
                $query->whereTime('create_at', '<=', $params['end_time']);
            }

            // 按时间倒序排序
            $query->order('create_at', 'desc');

            // 获取总数
            $total = $query->count();
            
            // 获取分页数据
            $items = $query->page($page, $limit)->select()->toArray();

            // 记录调试信息
            trace('Login logs query SQL: ' . Db::getLastSql(), 'debug');
            trace('Login logs count: ' . $total, 'debug');

            return $this->success([
                'items' => $items,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            trace('Get login logs error: ' . $e->getMessage(), 'error');
            return $this->error('获取登录日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取操作日志列表
     */
    public function getOperationLogs($params = [])
    {
        try {
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            $query = OperationLog::order('id', 'desc');

            // 应用搜索条件
            if (!empty($params['keyword'])) {
                $query->where('username|module|action|url|ip', 'like', "%{$params['keyword']}%");
            }
            if (!empty($params['module'])) {
                $query->where('module', $params['module']);
            }
            if (!empty($params['action'])) {
                $query->where('action', $params['action']);
            }
            if (!empty($params['start_time'])) {
                $query->whereTime('create_at', '>=', $params['start_time']);
            }
            if (!empty($params['end_time'])) {
                $query->whereTime('create_at', '<=', $params['end_time']);
            }

            $total = $query->count();
            $items = $query->page($page, $limit)->select();

            return $this->success([
                'items' => $items,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            trace('Get operation logs error: ' . $e->getMessage(), 'error');
            return $this->error('获取操作日志失败: ' . $e->getMessage());
        }
    }

} 