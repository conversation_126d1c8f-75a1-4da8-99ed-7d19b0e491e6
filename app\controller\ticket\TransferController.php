<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\service\ticket\TransferService;
use think\facade\Db;
use think\facade\Request;

class TransferController extends BaseTicketController
{
    protected $transferService;
    
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->transferService = new TransferService();
    }
    
    /**
     * 转派工单
     * @param int $id 工单ID
     */
    public function index($id)
    {
        try {
            trace('开始转派工单，ID='.$id, 'debug');
            
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            
            // 获取工单信息
            $ticket = Db::name('ticket')->where('id', $id)->find();
            
            if (!$ticket) {
                return json(['code' => 404, 'message' => '工单不存在']);
            }
            
            // 检查权限：只有管理员和工单创建者可以转派工单
            if (!$isAdmin && $ticket['creator_id'] != $userId) {
                return json(['code' => 403, 'message' => '无权转派此工单']);
            }
            
            // 获取转派数据
            $data = Request::post();
            
            // 验证参数
            if (empty($data['handler_id'])) {
                return json(['code' => 400, 'message' => '请选择转派目标']);
            }
            
            if (empty($data['remark'])) {
                return json(['code' => 400, 'message' => '请填写转派原因']);
            }
            
            // 调用服务执行转派
            $result = $this->transferService->transfer($id, $data, $userId);
            
            return json($result);
        } catch (\Exception $e) {
            trace('转派工单异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取用户列表，用于转派选择
     */
    public function getUsers()
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取部门ID
            $departmentId = Request::param('department_id/d', 0);
            
            // 构建查询
            $query = Db::name('user')
                ->alias('u')
                ->leftJoin('department d', 'u.department_id = d.id')
                ->field('u.id, u.username, u.realname, u.department_id, d.name as department_name')
                ->where('u.status', 1); // 只查询正常状态的用户
                
            // 如果指定了部门，则只查询该部门的用户
            if ($departmentId > 0) {
                $query->where('u.department_id', $departmentId);
            }
            
            // 获取用户列表
            $users = $query->select();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $users
            ]);
        } catch (\Exception $e) {
            trace('获取用户列表异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取部门列表，用于转派选择
     */
    public function getDepartments()
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取部门列表
            $departments = Db::name('department')
                ->field('id, name, parent_id')
                ->where('status', 1) // 只查询正常状态的部门
                ->order('sort', 'asc')
                ->select();
                
            // 将部门列表转换为树形结构
            $tree = $this->buildDepartmentTree($departments->toArray());
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            trace('获取部门列表异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 构建部门树
     * @param array $departments 部门列表
     * @param int $parentId 父级ID
     * @return array
     */
    protected function buildDepartmentTree($departments, $parentId = 0)
    {
        $tree = [];
        
        foreach ($departments as $department) {
            if ($department['parent_id'] == $parentId) {
                $children = $this->buildDepartmentTree($departments, $department['id']);
                
                if (!empty($children)) {
                    $department['children'] = $children;
                }
                
                $tree[] = $department;
            }
        }
        
        return $tree;
    }
} 