// 村集体打款管理组件
const VillageManagement = {
    template: `
        <div class="village-management">
            <h2>村集体打款管理</h2>
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-form :inline="true" :model="filterForm">
                    <el-form-item label="项目年度">
                        <el-select v-model="filterForm.project_year" placeholder="选择项目年度" clearable>
                            <el-option v-for="year in projectYears" :key="year" :label="year" :value="year"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放年度">
                        <el-select v-model="filterForm.payment_year" placeholder="选择发放年度" clearable>
                            <el-option v-for="year in paymentYears" :key="year" :label="year" :value="year"></el-option>
                        </el-select>
                    </el-form-item>                    
                    <el-form-item label="所在县">
                        <el-input v-model="filterForm.county" placeholder="请输入所在县"></el-input>
                    </el-form-item>
                    <el-form-item label="所在乡镇">
                        <el-input v-model="filterForm.township" placeholder="请输入乡镇名称"></el-input>
                    </el-form-item>
                    <el-form-item label="所在村">
                        <el-input v-model="filterForm.village" placeholder="请输入村居名称"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button @click="resetFilter">重置</el-button>
                        <el-button type="success" @click="showVillageDialog">登记收益</el-button>
                        <el-button type="primary" @click="handleImport">导入数据</el-button>
                        <el-button type="primary" @click="handleExport">导出数据</el-button>
                        <el-button type="info" @click="downloadTemplate">下载模板</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 数据展示区域 -->
            <div class="data-section">
                <!-- 直接显示数据列表 -->
                <div class="table-section">
                    <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
                        <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                        <el-table-column prop="project_year" label="项目年度" width="90"></el-table-column>
                        <el-table-column prop="payment_year" label="发放年度" width="90"></el-table-column>
                        <el-table-column prop="county" label="所在县" width="100"></el-table-column>
                        <el-table-column prop="township" label="所在乡镇" width="120"></el-table-column>
                        <el-table-column prop="village" label="所在村" width="220"></el-table-column>
                        <el-table-column prop="payment_date" label="导入日期" width="120"></el-table-column>
                        <el-table-column prop="amount" label="收益金额" width="120">
                            <template slot-scope="scope">
                                {{ formatCurrency(scope.row.amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注说明" min-width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button type="text" size="small" @click="handleDelete(scope.row)" style="color: #F56C6C;">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalItems">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 村集体打款登记对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="所在县" prop="county">
                        <el-input v-model="form.county" placeholder="请输入所在县"></el-input>
                    </el-form-item>
                    <el-form-item label="所在乡镇" prop="township">
                        <el-input v-model="form.township" placeholder="请输入所在乡镇"></el-input>
                    </el-form-item>
                    <el-form-item label="所在村" prop="village">
                        <el-input v-model="form.village" placeholder="请输入所在村"></el-input>
                    </el-form-item>
                    <el-form-item label="项目年度" prop="project_year">
                        <el-select v-model="form.project_year" placeholder="选择项目年度" style="width: 100%;">
                            <el-option v-for="year in projectYears" :key="year" :label="year" :value="year"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放年度" prop="payment_year">
                        <el-select v-model="form.payment_year" placeholder="选择发放年度" style="width: 100%;">
                            <el-option v-for="year in paymentYears" :key="year" :label="year" :value="year"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="打款日期" prop="payment_date">
                        <el-date-picker
                            v-model="form.payment_date"
                            type="date"
                            placeholder="选择打款日期"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="收益金额" prop="amount">
                        <el-input v-model.number="form.amount" placeholder="请输入金额" @blur="handleAmountBlur">
                            <template slot="append">元</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="备注说明" prop="remark">
                        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 导入数据对话框 -->
            <el-dialog title="导入村集体打款数据" :visible.sync="importDialogVisible" width="500px">
                <el-upload
                    class="upload-demo"
                    drag
                    action="/finance/village/import"
                    :headers="uploadHeaders"
                    :on-success="handleImportSuccess"
                    :on-error="handleImportError"
                    :before-upload="beforeImportUpload"
                    accept=".xlsx, .xls">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">
                        只能上传xlsx/xls文件，且不超过5MB<br>
                        <p>支持多种表格标题格式，例如：</p>
                        <p>- "2019年项目平邑县光伏项目村集体收益（2024年度）"</p>
                        <p>- "2019年项目蒙阴县光伏项目村集体收益明细表（2024年度）"</p>
                        <p>- 或工作表名称为县名的其他格式</p>
                        <p>第二行必须包含字段：乡镇、村居、村集体收益（元）</p>
                        <p>支持乡镇列合并单元格的表格格式（一个乡镇对应多个村），系统会自动识别</p>
                        <p>系统会自动检查重复数据，如果发放年度、所在县、所在镇、所在村、收益金额完全相同的记录已存在，则不会重复导入</p>
                        <el-link type="primary" @click="downloadTemplate">下载导入模板</el-link>
                    </div>
                </el-upload>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            // 过滤表单
            filterForm: {
                project_year: '',
                payment_year: '',
                dateRange: [],
                county: '',
                township: '',
                village: '',
                payment_status: ''
            },
            // 表格数据
            tableData: [],
            tableLoading: false,
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            
            // 对话框控制
            dialogVisible: false,
            dialogTitle: '登记村集体打款',
            importDialogVisible: false,
            
            // 年份选项
            projectYears: this.generateYearOptions(2018, new Date().getFullYear()),
            paymentYears: this.generateYearOptions(2018, new Date().getFullYear() + 1),
            
            // 表单数据
            form: {
                county: '平邑县',
                township: '',
                village: '',
                project_year: 2019,
                payment_year: new Date().getFullYear(),
                payment_date: '',
                amount: '',
                remark: ''
            },
            rules: {
                township: [
                    { required: true, message: '请输入所在乡镇', trigger: 'blur' }
                ],
                village: [
                    { required: true, message: '请输入所在村', trigger: 'blur' }
                ],
                payment_date: [
                    { required: true, message: '请选择打款日期', trigger: 'change' }
                ],
                amount: [
                    { required: true, message: '请输入收益金额', trigger: 'blur' },
                    { 
                        validator: (rule, value, callback) => {
                            // 允许空值，因为required规则会处理
                            if (!value) {
                                callback();
                                return;
                            }
                            // 检查是否为有效数字
                            if (isNaN(parseFloat(value)) || !isFinite(value)) {
                                callback(new Error('金额必须为数字值'));
                            } else {
                                // 将值转换为数字类型
                                this.form.amount = parseFloat(value);
                                callback();
                            }
                        }, 
                        trigger: 'blur' 
                    }
                ]
            },
            
            // 上传相关
            uploadHeaders: {
                Authorization: 'Bearer ' + localStorage.getItem('token')
            },
            // 新增API交互相关状态
            loading: false,
            importLoading: false,
            submitLoading: false,
            deleteLoading: false
        };
    },
    mounted() {
        this.fetchData();
    },
    methods: {
        // 生成年份选项
        generateYearOptions(startYear, endYear) {
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            return years;
        },
        
        // 获取村集体打款列表数据
        async fetchData() {
            this.tableLoading = true;
            try {
                // 构建请求参数
                const params = {
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                // 添加筛选条件
                if (this.filterForm.project_year) {
                    params.project_year = this.filterForm.project_year;
                }
                if (this.filterForm.payment_year) {
                    params.payment_year = this.filterForm.payment_year;
                }
                if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                    params.start_date = this.filterForm.dateRange[0];
                    params.end_date = this.filterForm.dateRange[1];
                }
                if (this.filterForm.county) {
                    params.county = this.filterForm.county;
                }
                if (this.filterForm.township) {
                    params.township = this.filterForm.township;
                }
                if (this.filterForm.village) {
                    params.village = this.filterForm.village;
                }
                if (this.filterForm.payment_status) {
                    params.payment_status = this.filterForm.payment_status;
                }
                
                // 调用API
                const response = await this.$http.get('/finance/village/list', { params });
                
                if (response.data.code === 200) {
                    this.tableData = response.data.data.items;
                    this.totalItems = response.data.data.total;
                } else {
                    this.$message.error(response.data.message || '获取村集体打款数据失败');
                }
            } catch (error) {
                console.error('获取村集体打款数据异常:', error);
                this.$message.error('获取村集体打款数据失败: ' + (error.message || '未知错误'));
            } finally {
                this.tableLoading = false;
            }
        },
        
        // 显示新增对话框
        showVillageDialog() {
            this.dialogTitle = '登记村集体打款';
            this.form = {
                county: '平邑县',
                township: '',
                village: '',
                project_year: 2019,
                payment_year: new Date().getFullYear(),
                payment_date: this.formatDate(new Date()),
                amount: '',
                remark: ''
            };
            this.dialogVisible = true;
        },
        
        // 格式化日期为 YYYY-MM-DD
        formatDate(date) {
            const d = new Date(date);
            let month = '' + (d.getMonth() + 1);
            let day = '' + d.getDate();
            const year = d.getFullYear();
            
            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;
            
            return [year, month, day].join('-');
        },
        
        // 显示编辑对话框
        handleEdit(row) {
            this.dialogTitle = '编辑村集体打款';
            this.form = {
                id: row.id,
                county: row.county || '平邑县',
                township: row.township || '',
                village: row.village || '',
                project_year: row.project_year || 2019,
                payment_year: row.payment_year || new Date().getFullYear(),
                payment_date: row.payment_date,
                amount: parseFloat(row.amount),
                remark: row.remark || ''
            };
            this.dialogVisible = true;
        },
        
        // 删除村集体打款记录
        async handleDelete(row) {
            try {
                await this.$confirm('确定要删除这条村集体打款记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                this.deleteLoading = true;
                const response = await this.$http.delete(`/finance/village/delete/${row.id}`);
                
                if (response.data.code === 200) {
                    this.$message.success('删除成功');
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除村集体打款记录异常:', error);
                    this.$message.error('删除失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.deleteLoading = false;
            }
        },
        
        // 提交表单
        async submitForm() {
            try {
                // 表单验证
                await this.$refs.form.validate();
                
                this.submitLoading = true;
                let response;
                
                if (this.form.id) {
                    // 更新
                    response = await this.$http.put(`/finance/village/update/${this.form.id}`, this.form);
                } else {
                    // 新增
                    response = await this.$http.post('/finance/village/save', this.form);
                }
                
                if (response.data.code === 200) {
                    this.$message.success(this.form.id ? '更新成功' : '添加成功');
                    this.dialogVisible = false;
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || (this.form.id ? '更新失败' : '添加失败'));
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('提交村集体打款表单异常:', error);
                    this.$message.error('提交失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.submitLoading = false;
            }
        },
        
        // 处理导入
        handleImport() {
            this.importDialogVisible = true;
        },
        
        // 上传前检查
        beforeImportUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                           file.type === 'application/vnd.ms-excel';
            const isLt5M = file.size / 1024 / 1024 < 5;
            
            if (!isExcel) {
                this.$message.error('请上传Excel文件！');
                return false;
            }
            if (!isLt5M) {
                this.$message.error('文件大小不能超过5MB！');
                return false;
            }
            return true;
        },
        
        // 导入成功回调
        handleImportSuccess(response) {
            this.importDialogVisible = false;
            this.tableLoading = false;
            
            if (response.code === 200) {
                const result = response.data;
                let message = `导入完成：成功 ${result.success} 条`;
                
                if (result.fail > 0) {
                    message += `，失败 ${result.fail} 条`;
                }
                
                if (result.duplicate > 0) {
                    message += `，跳过重复 ${result.duplicate} 条`;
                }
                
                this.$message.success(message);
                
                // 如果有错误信息，显示详情
                if (result.errors && result.errors.length > 0) {
                    // 最多显示5条错误信息，避免界面过长
                    const displayErrors = result.errors.slice(0, 5);
                    const remainingErrors = result.errors.length - 5;
                    
                    let errorMessage = '<div style="text-align: left; max-height: 300px; overflow-y: auto;">';
                    errorMessage += '<p>导入过程中发现以下问题：</p>';
                    errorMessage += '<ul>';
                    
                    displayErrors.forEach(error => {
                        errorMessage += `<li>${error}</li>`;
                    });
                    
                    if (remainingErrors > 0) {
                        errorMessage += `<li>...以及其他 ${remainingErrors} 条错误信息</li>`;
                    }
                    
                    errorMessage += '</ul></div>';
                    
                    this.$alert(errorMessage, '导入详情', {
                        dangerouslyUseHTMLString: true,
                        confirmButtonText: '确定'
                    });
                }
                
                // 刷新数据列表
                this.fetchData();
            } else {
                this.$message.error(response.message || '导入失败');
            }
        },
        
        // 导入失败回调
        handleImportError(error) {
            this.importDialogVisible = false;
            this.tableLoading = false;
            
            let errorMessage = '导入失败';
            if (error.response && error.response.data) {
                errorMessage = error.response.data.message || '导入失败';
            }
            
            this.$message.error(errorMessage);
        },
        
        // 下载导入模板
        downloadTemplate() {
            window.open('/finance/village/template', '_blank');
        },
        
        // 处理搜索
        handleSearch() {
            this.currentPage = 1;
            this.fetchData();
        },
        
        // 重置筛选条件
        resetFilter() {
            this.filterForm = {
                project_year: '',
                payment_year: '',
                dateRange: [],
                county: '',
                township: '',
                village: '',
                payment_status: ''
            };
            this.currentPage = 1;
            this.fetchData();
        },
        
        // 重置表单
        resetForm() {
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.fetchData();
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.fetchData();
        },
        
        // 格式化金额
        formatCurrency(value) {
            if (!value) return '0.00';
            // 转换为两位小数的金额格式
            const num = parseFloat(value);
            return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        // 金额输入框失焦时格式化
        handleAmountBlur() {
            if (this.form.amount) {
                // 先确保为数字，然后格式化为两位小数后，再转为数字类型保存
                const value = parseFloat(this.form.amount);
                if (!isNaN(value)) {
                    this.form.amount = parseFloat(value.toFixed(2));
                }
            }
        },
        
        // 导出数据
        handleExport() {
            // 构建参数
            const params = {};
            if (this.filterForm.project_year) {
                params.project_year = this.filterForm.project_year;
            }
            if (this.filterForm.payment_year) {
                params.payment_year = this.filterForm.payment_year;
            }
            if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                params.start_date = this.filterForm.dateRange[0];
                params.end_date = this.filterForm.dateRange[1];
            }
            if (this.filterForm.county) {
                params.county = this.filterForm.county;
            }
            if (this.filterForm.township) {
                params.township = this.filterForm.township;
            }
            if (this.filterForm.village) {
                params.village = this.filterForm.village;
            }
            if (this.filterForm.payment_status) {
                params.payment_status = this.filterForm.payment_status;
            }
            
            // 构建URL参数字符串
            const queryString = Object.keys(params)
                .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
                .join('&');
            
            // 打开导出URL
            window.open(`/finance/village/export?${queryString}`, '_blank');
        }
    }
};

// 修改导出方式
export default VillageManagement; 