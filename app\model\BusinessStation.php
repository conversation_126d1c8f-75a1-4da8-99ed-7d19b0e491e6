<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class BusinessStation extends Model
{
    protected $name = 'business_station';
    protected $prefix = 'sp_';

    // 如果数据库中的 create_at 和 update_at 设置了自动更新，可以注释掉下面三行
    // 否则，取消注释以让模型管理时间戳
    protected $autoWriteTimestamp = 'datetime'; // 使用 datetime 类型
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    // 定义模型可以写入的字段及其类型
    protected $schema = [
        'id'                 => 'int',
        'department'         => 'string',
        'company_name'       => 'string',
        'address'            => 'string',
        'cooperation_mode'   => 'string',
        'contact_method'     => 'string',
        'account_number'     => 'string',
        'capacity'           => 'float', // 数据库存储 MW (兆瓦)
        'unit_price'         => 'float',
        'investment_amount'  => 'float',
        'panel_brand_power'  => 'string',
        'panel_count'        => 'int',
        'inverter_count'     => 'int',
        'grid_connection_time' => 'date', // 存储日期类型 YYYY-MM-DD
        'create_at'          => 'datetime',
        'update_at'          => 'datetime',
    ];


} 