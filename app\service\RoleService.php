<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Role;
use think\facade\Db;

class RoleService extends BaseService
{
    /**
     * 获取角色列表
     */
    public function getList()
    {
        try {
            $roles = Role::select();
            return $this->success($roles);
        } catch (\Exception $e) {
            return $this->error('获取角色列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建角色
     */
    public function create($data)
    {
        try {
            $role = new Role;
            $role->code = $data['code'];
            $role->name = $data['name'];
            $role->description = $data['description'] ?? '';
            $role->save();

            return $this->success($role, '角色创建成功');
        } catch (\Exception $e) {
            return $this->error('角色创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新角色
     */
    public function update($id, $data)
    {
        try {
            $role = Role::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }

            $role->code = $data['code'] ?? $role->code;
            $role->name = $data['name'] ?? $role->name;
            $role->description = $data['description'] ?? $role->description;
            $role->save();

            return $this->success($role, '角色更新成功');
        } catch (\Exception $e) {
            return $this->error('角色更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除角色
     */
    public function delete($id)
    {
        try {
            $role = Role::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }

            // 检查是否有用户使用该角色
            if ($role->users()->count() > 0) {
                return $this->error('该角色下还有用户，不能删除');
            }

            // 删除角色权限关联
            $role->permissions()->detach();
            // 删除角色
            $role->delete();

            return $this->success(null, '角色删除成功');
        } catch (\Exception $e) {
            return $this->error('角色删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取角色权限
     */
    public function getPermissions($id)
    {
        try {
            $role = Role::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }

            $permissions = $role->permissions()->select();
            return $this->success($permissions);
        } catch (\Exception $e) {
            return $this->error('获取角色权限失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配权限
     */
    public function assignPermissions($id, $permissionIds)
    {
        try {
            $role = Role::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }

            // 更新角色权限
            $role->permissions()->detach();
            if (!empty($permissionIds)) {
                $role->permissions()->attach($permissionIds);
            }

            return $this->success(null, '权限分配成功');
        } catch (\Exception $e) {
            return $this->error('权限分配失败: ' . $e->getMessage());
        }
    }
}