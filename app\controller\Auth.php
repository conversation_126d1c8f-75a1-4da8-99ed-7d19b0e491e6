<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Request;
use app\model\User;
use think\facade\Cache;
use think\facade\Session;
use think\facade\Config;
use app\service\LogService;
use think\facade\Db;
use app\service\UserService;
use think\App;

class Auth extends BaseController
{
    protected $userService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->userService = new UserService();
    }

    /**
     * 用户登录
     */
    public function login()
    {
        trace('=== Login Process Started ===', 'debug');
        
        $username = Request::post('username');
        $password = Request::post('password');
        $remember = Request::post('remember', false);
        $ip = Request::ip();
        $userAgent = Request::header('user-agent');

        if (empty($username) || empty($password)) {
            LogService::recordLogin(0, $username, 0, '用户名或密码为空', $ip, $userAgent);
            return json([
                'code' => 400,
                'message' => '用户名和密码不能为空',
                'data' => null
            ]);
        }

        try {
            $user = User::where('username', $username)->find();
            
            // 验证用户存在
            if (!$user) {
                LogService::recordLogin(0, $username, 0, '用户不存在', $ip, $userAgent);
                return json([
                    'code' => 401,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            // 验证密码
            trace('验证密码，输入密码: ' . $password . ', 数据库密码哈希: ' . $user->password, 'debug');
            if (!password_verify($password, $user->password)) {
                LogService::recordLogin($user->id, $username, 0, '密码错误', $ip, $userAgent);
                return json([
                    'code' => 401,
                    'message' => '密码错误',
                    'data' => null
                ]);
            }

            // 检查用户状态
            if ($user->status != 1) {
                LogService::recordLogin($user->id, $username, 0, '账号已被禁用', $ip, $userAgent);
                return json([
                    'code' => 403,
                    'message' => '账号已被禁用',
                    'data' => null
                ]);
            }

            // 1. 在获取用户角色名称后，添加获取角色ID的代码：
            // 获取用户角色
            $roles = Db::name('user_role')
            ->alias('ur')
            ->join('role r', 'ur.role_id = r.id')
            ->where('ur.user_id', $user->id)
            ->column('r.name');

            // 获取用户角色ID - 添加这段代码
            $roleIds = Db::name('user_role')
            ->where('user_id', $user->id)
            ->column('role_id');

            trace('User roles: ' . json_encode($roles), 'debug');
            trace('User role IDs: ' . json_encode($roleIds), 'debug'); // 添加日志

            // 生成 token
            $token = md5(uniqid() . time());
            
           // 2. 在userInfo数组中添加role_ids字段：
            $userInfo = [
                'id' => $user->id,
                'username' => $user->username,
                'realname' => $user->realname,
                'roles' => $roles,
                'role_ids' => $roleIds, // 添加这行
                'department_id' => $user->department_id
            ];
            
            Cache::set('token_' . $token, $userInfo, 7200);  // 2小时过期
            
            // 更新最后登录时间
            $user->last_login = date('Y-m-d H:i:s');
            $user->save();

            // 设置 Session
            $expire = 7 * 24 * 3600;  // 7天
            Config::set(['session' => ['expire' => $expire]]);
            Session::set('user_id', $user->id);
            Session::set('username', $user->username);
            Session::set('realname', $user->realname);
            Session::set('roles', $roles);
            Session::set('role_ids', $roleIds); // 添加这行
            
            // 记录登录成功日志
            LogService::recordLogin(
                $user->id, 
                $username, 
                1, 
                '登录成功', 
                $ip, 
                $userAgent, 
                [
                    'remember' => $remember,
                    'roles' => $roles
                ]
            );

            trace('=== Login Process Completed Successfully ===', 'debug');

            // 返回标准格式的响应
            $responseData = [
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'expires_in' => $expire,
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'realname' => $user->realname,
                        'department_id' => $user->department_id,
                        'status' => $user->status,
                        'roles' => $roles
                    ]
                ]
            ];

            trace('Login response data: ' . json_encode($responseData), 'debug');
            return json($responseData);

        } catch (\Exception $e) {
            trace('Login error: ' . $e->getMessage(), 'error');
            LogService::recordLogin(
                isset($user) ? $user->id : 0, 
                $username, 
                0, 
                '登录异常：' . $e->getMessage(),
                $ip,
                $userAgent
            );
            return json([
                'code' => 500,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        $token = Request::header('Authorization');
        if ($token) {
            // 移除 Bearer 前缀
            $token = str_replace('Bearer ', '', $token);
            Cache::delete('token_' . $token);
        }
        Session::clear();
        return $this->success(null, '退出成功');
    }

    /**
     * 获取当前用户信息
     */
    public function getUserInfo()
    {
        try {
            if (!$this->isLogin()) {
                return json([
                    'code' => 401,
                    'message' => '未登录',
                    'data' => null
                ]);
            }

            $user = request()->user;
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'username' => $user->username,
                    'realname' => $user->realname,
                    'department_id' => $user->department_id,
                    'roles' => $user->roles ?? []
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取失败',
                'data' => null
            ]);
        }
    }

    /**
     * 修改密码
     */
    public function changePassword()
    {
        try {
            $userId = Session::get('user_id');
            if (!$userId) {
                return json(['code' => 401, 'message' => '未登录']);
            }

            $params = $this->request->post();
            if (empty($params['old_password']) || empty($params['new_password'])) {
                return json(['code' => 400, 'message' => '参数错误']);
            }

            $result = $this->userService->changePassword($userId, $params['old_password'], $params['new_password']);
            
            if ($result['code'] === 200) {
                // 记录操作日志
                LogService::recordOperation('用户', '修改密码', [
                    'user_id' => $userId
                ]);
            }
            
            return json($result);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '修改密码失败：' . $e->getMessage()]);
        }
    }

    /**
     * 测试密码验证（仅用于调试）
     */
    public function testPassword()
    {
        $params = Request::post();
        $hash = '$2y$10$ktvdIDP8qqBwm4SD7/UZeOTIt1nrV9DgL1mAhZFzW4PNyRpjQYKjK'; // admin的密码哈希

        $result = password_verify($params['password'], $hash);
        
        trace('Password test - Input: ' . $params['password'] . ', Result: ' . ($result ? 'true' : 'false'), 'debug');
        
        return json([
            'code' => 200,
            'message' => 'Test result',
            'data' => [
                'result' => $result,
                'input' => $params['password'],
                'hash' => $hash
            ]
        ]);
    }

    /**
     * 验证登录状态
     */
    protected function isLogin()
    {
        try {
            $token = Request::header('Authorization');
            if (!$token) {
                return false;
            }

            $token = str_replace('Bearer ', '', $token);
            $userInfo = Cache::get('token_' . $token); // 获取存储的用户信息数组

            if (!$userInfo || !isset($userInfo['id'])) {
                return false;
            }

            $user = User::find($userInfo['id']);
            if (!$user) {
                return false;
            }

            request()->user = $user;
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 