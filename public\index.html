<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分布式光伏运维平台</title>
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css"> -->
    <link rel="stylesheet" href="/js/lib/element-ui/2.15.14/index.css">
    <link rel="stylesheet" href="/css/ticket.css">
    <link rel="stylesheet" href="css/style.css">
    <script src="/js/lib/xlsx/0.17.5/xlsx.full.min.js"></script>
    <style>
        /* 添加加载状态样式 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
        }

        .loading-text {
            margin-top: 20px;
            color: #409EFF;
            font-size: 16px;
        }

        /* 隐藏主内容，直到加载完成 */
        [v-cloak] {
            display: none;
        }

        body {
            margin: 0;
            padding: 0;
            height: 100vh;
        }
        .app-container {
            height: 100vh;
            display: flex;
        }
        .sidebar {
            width: 200px;
            background-color: #304156;
            color: #fff;
        }
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .header {
            height: 55px;
            background-color: #fff;
            border-bottom: 1px solid #dcdfe6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f7fa;
            overflow: auto;
        }
        .el-menu {
            border-right: none;
        }
        .logo-container {
            height: 55px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #375283;
            /* border-bottom: 1px solid #dcdfe6; */
        }
        
        .logo-container img {
            height: 110px;
            width: auto;
        }

        .menu-container {
            height: calc(100% - 65px);
            overflow-y: auto;
        }

        /* 消息盒子样式 */
        .message-box {
            display: inline-block;
            padding: 0 8px;
            cursor: pointer;
            position: relative;
        }
        
        .message-box:hover {
            background-color: rgba(0,0,0,.05);
        }
        
        .message-badge {
            position: absolute;
            top: 10px;
            right: 20px;
        }
        .el-dropdown{
            margin-right: 50px;
        }
        .el-avatar--small {
            width: 35px;
            height: 35px;
            line-height: 35px;
        }
        .el-button .is-circle{
            padding: 8px;
        }
        .el-table .cell {
            text-overflow: clip;
        }
        .ticket-item {
            border-bottom: 1px solid #eee;
            padding: 10px;
            margin: 0;
        }
        
        .ticket-item:last-child {
            border-bottom: none;
        }
        
        .ticket-item:hover {
            background-color: #f5f7fa;
        }

        .ticket-dropdown {
            width: 350px;
            max-height: 500px;
            overflow-y: auto;
        }

        .ticket-header {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ticket-content {
            padding: 0;
        }

        .ticket-empty {
            text-align: center;
            color: #999;
            padding: 20px;
        }

        .message-popover {
            padding: 12px !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            z-index: 2000 !important;
        }

        .message-table .el-table__header th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .ticket-title {
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .priority-tag {
            margin-right: 8px;
            flex-shrink: 0;
        }

        .message-button {
            font-size: 18px;
            padding: 8px !important;
            color: #606266;
            transition: all 0.3s;
            /* margin-right: 15px; */
        }

        .message-button:hover {
            color: #409EFF;
            background-color: #ecf5ff;
        }

        .message-badge .el-badge__content {
            top: 5px;
            right: 2px;
            transform: translate(50%, -50%);
        }

        /* 用户菜单样式 */
        .user-menu-trigger {
            display: flex;
            align-items: center;
            padding: 0 15px;
            height: 40px;
            border-radius: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .user-menu-trigger:hover {
            background-color: #f5f7fa;
        }

        .user-avatar {
            margin-right: 8px;
            background-color: #409EFF;
        }

        .user-dropdown-menu {
            margin-top: 5px !important;
            border-radius: 8px !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 0 16px;
            width: 120px;
            height: 36px;
            font-size: 14px;
            color: #606266;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background-color: #f5f7fa;
            color: #409EFF;
        }

        .menu-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* 新增样式 */
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px; /* 控制消息图标和用户菜单的间距 */
        }
    </style>
</head>
<body>
    <!-- 添加加载状态 -->
    <div id="loading" class="loading-container" v-if="loading">
        <div class="loading-content">
            <i class="el-icon-loading" style="font-size: 30px; color: #409EFF;"></i>
            <div class="loading-text">系统加载中...</div>
        </div>
    </div>

    <!-- 使用 v-cloak 指令隐藏未编译的模板 -->
    <div id="app" v-cloak>
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 添加 logo 容器 -->
                <div class="logo-container">
                    <img src="/images/logo.png" alt="光伏运维平台" style="height:65px;">
                </div>
                
                <!-- 包装菜单，添加滚动功能 -->
                <div class="menu-container">
                    <el-menu
                        :default-active="activeMenu"
                        class="el-menu-vertical"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409EFF"
                        router>
                        <!-- 数据统计 -->
                        <el-menu-item index="/stats">
                            <i class="el-icon-data-line"></i>
                            <span slot="title">可视化面板</span>
                        </el-menu-item>

                        <!-- 电站管理 -->
                        <el-submenu index="2">
                            <template slot="title">
                                <i class="el-icon-office-building"></i>
                                <span>电站管理</span>
                            </template>
                            <el-menu-item index="/station/farmer">农户电站管理</el-menu-item>
                            <el-menu-item index="/station/business">工商业电站管理</el-menu-item>
                            <el-menu-item index="/station/problem">电站运维管理</el-menu-item>
                            <el-menu-item index="/archive-init">农户档案初始化</el-menu-item>
                        </el-submenu>
                        <!-- 财务管理 -->
                        <el-submenu index="finance">
                            <template slot="title">
                                <i class="el-icon-money"></i>
                                <span>财务管理</span>
                            </template>
                            <el-menu-item index="/finance/fund">资金归集</el-menu-item>
                            <el-menu-item index="/finance/rent">租金发放</el-menu-item>
                            <el-menu-item index="/finance/grid">国网打款</el-menu-item>
                            <el-menu-item index="/finance/village">村集体收益</el-menu-item>
                            <el-menu-item index="/finance/reconciliation">资金对账</el-menu-item>
                        </el-submenu>
                        <!-- 数据分析 -->
                        <el-submenu index="data">
                            <template slot="title">
                                <i class="el-icon-data-analysis"></i>
                                <span>数据分析</span>
                            </template>
                            <el-menu-item index="/data/power">发电量分析</el-menu-item>
                            <el-menu-item index="/data/income">收益分析</el-menu-item>
                            <el-menu-item index="/data/target">目标管理</el-menu-item>
                        </el-submenu>

                        

                        <!-- 系统管理 -->
                        <el-submenu index="system">
                            <template slot="title">
                                <i class="el-icon-setting"></i>
                                <span>系统管理</span>
                            </template>
                            <el-menu-item index="/user">用户管理</el-menu-item>
                            <el-menu-item index="/role">角色管理</el-menu-item>
                            <el-menu-item index="/permission">权限管理</el-menu-item>
                            <el-menu-item index="/department">部门管理</el-menu-item>                            
                        </el-submenu>

                        <!-- 日志管理（新增独立菜单） -->
                        <el-submenu index="logs">
                            <template slot="title">
                                <i class="el-icon-document"></i>
                                <span>安全管理</span>
                            </template>
                            <el-menu-item index="/log/login">登录日志</el-menu-item>
                            <el-menu-item index="/log/operation">操作日志</el-menu-item>
                        </el-submenu>

                        <!-- 工单管理 -->
                        <el-submenu index="/ticket">
                            <template slot="title">
                                <i class="el-icon-tickets"></i>
                                <span>工单管理</span>
                            </template>
                            <el-menu-item index="/ticket/todo">待办工单</el-menu-item>
                            <el-menu-item index="/ticket/handling">在办工单</el-menu-item> <!-- 新增 -->
                            <el-menu-item index="/ticket/done">已办工单</el-menu-item>
                            <el-menu-item index="/ticket/category">分类管理</el-menu-item>
                            <el-menu-item index="/ticket/tag">标签管理</el-menu-item>
                        </el-submenu>
                    </el-menu>
                </div>
            </div>
            
            <!-- 主容器 -->
            <div class="main-container">
                <!-- 头部 -->
                <div class="header">
                    <div class="left">
                        <h2>分布式光伏运维平台</h2>
                    </div>
                    <div class="header-right">
                        <div class="header-controls">
                            <!-- 消息盒子 -->
                            <el-popover
                                placement="bottom"
                                width="450"
                                trigger="click"
                                popper-class="message-popover">
                                <div v-if="todoTickets.length > 0">
                                    <el-table 
                                        :data="todoTickets"
                                        @row-click="handleTicketCommand"
                                        class="message-table"
                                        style="cursor: pointer;">
                                        <el-table-column
                                            prop="title"
                                            label="工单信息"
                                            min-width="280">
                                            <template slot-scope="scope">
                                                <div class="ticket-title">
                                                    <el-tag 
                                                        size="mini" 
                                                        :type="getPriorityType(scope.row.priority)"
                                                        class="priority-tag">
                                                        {{ getPriorityLabel(scope.row.priority) }}
                                                    </el-tag>
                                                    <!-- <span class="ticket-no">{{ scope.row.ticket_no }}</span> -->
                                                    {{ scope.row.title }}
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            prop="create_at"
                                            label="创建时间"
                                            width="150"
                                            align="center">
                                            <template slot-scope="scope">
                                                <el-tag 
                                                    :type="statusType(scope.row.create_at)"
                                                    size="mini"
                                                    effect="dark">
                                                    {{ statusLabel(scope.row.create_at) }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                                <div v-else class="ticket-empty">
                                    暂无待办工单
                                </div>
                                <el-badge 
                                    :value="unreadCount" 
                                    :max="99" 
                                    class="message-badge" 
                                    slot="reference">
                                    <el-button 
                                        icon="el-icon-bell" 
                                        circle
                                        class="message-button"></el-button>
                                </el-badge>
                            </el-popover>

                            <!-- 用户下拉菜单 -->
                            <el-dropdown trigger="click">
                                <div class="user-menu-trigger">
                                    <el-avatar 
                                        icon="el-icon-user-solid"
                                        size="small"
                                        class="user-avatar"></el-avatar>
                                    {{ userInfo.realname || userInfo.username }}
                                    <i class="el-icon-arrow-down el-icon--right"></i>
                                </div>
                                <el-dropdown-menu 
                                    slot="dropdown"
                                    class="user-dropdown-menu">
                                    <el-dropdown-item>
                                        <div class="menu-item" @click="showChangePassword">
                                            <i class="el-icon-key menu-icon"></i>
                                            修改密码
                                        </div>
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <div class="menu-item" @click="handleLogout">
                                            <i class="el-icon-switch-button menu-icon"></i>
                                            退出登录
                                        </div>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </div>
                </div>
                
                <!-- 内容区 -->
                <div class="content">
                    <router-view></router-view>
                </div>
            </div>
        </div>

        <!-- 将对话框移到这里，作为 app 的直接子元素 -->
        <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="400px">
            <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
                <el-form-item label="原密码" prop="oldPassword">
                    <el-input type="password" v-model="passwordForm.oldPassword" show-password></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                    <el-input type="password" v-model="passwordForm.newPassword" show-password></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input type="password" v-model="passwordForm.confirmPassword" show-password></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="passwordDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitChangePassword">确 定</el-button>
            </div>
        </el-dialog>
    </div>

    <!-- 路由配置 -->
    <script>
        // 避免重复声明，使用 window.routes
        if (!window.routes) {
            window.routes = [];
        }
    </script>

<!-- 核心库文件 -->
<script src="/js/lib/vue/2.6.14/vue.min.js"></script>
<script src="/js/lib/vue-router/3.5.3/vue-router.min.js"></script>
<script src="/js/lib/element-ui/2.15.14/index.js"></script>
<script src="/js/lib/axios/0.21.1/axios.min.js"></script>
<script src="/js/lib/echarts/5.4.3/echarts.min.js"></script>

<!-- 配置文件 -->
<script src="/js/config/api.js"></script>
<script src="/js/config/http.js"></script>


<!-- 工具类文件 -->
<script src="/js/utils/passive-events.js"></script>
<script src="/js/utils/ticket.js"></script>

<!-- 组件文件 -->
<script src="/js/views/station/problem.js"></script>
<script src="/js/views/user.js"></script>
<script src="/js/views/role.js"></script>
<script src="/js/views/permission.js"></script>
<script src="/js/views/department.js"></script>
<script src="/js/views/station.js"></script>
<script src="/js/views/station-farmer.js"></script>
<script src="/js/views/station-business.js"></script>
<script src="/js/views/archive-init.js"></script>
<script src="/js/views/stats.js"></script>
<script src="/js/views/login-log.js"></script>
<script src="/js/views/operation-log.js"></script>

<!-- 数据分析组件 -->
<script src="/js/views/data/power.js"></script>
<script src="/js/views/data/income.js"></script>
<script src="/js/views/data/target.js"></script>


<!-- 工单组件 -->
<script src="/js/views/ticket/layout.js"></script>
<script src="/js/views/ticket/publish.js"></script>
<script src="/js/views/ticket/todo.js"></script>
<script src="/js/views/ticket/handling.js"></script>
<script src="/js/views/ticket/done.js"></script>
<script src="/js/views/ticket/detail.js"></script>
<script src="/js/views/ticket/handle.js"></script>
<script src="/js/views/ticket/category.js"></script>
<script src="/js/views/ticket/tag.js"></script>
<script src="/js/config/router.js"></script>

    <script>
        // 正确的初始化顺序
        window.setupHttp();  // 1. 初始化HTTP配置
        const router = window.setupRouter();  // 2. 初始化路由
        
        // 在Vue实例创建前添加全局事件总线
        Vue.prototype.$eventHub = new Vue();
        
        new Vue({
            el: '#app',
            router,  // 注入路由实例
            data() {
                return {
                    loading: true,
                    activeMenu: '/stats',
                    userInfo: {
                        username: '',
                        realname: '',
                        roles: []
                    },
                    passwordDialogVisible: false,
                    passwordForm: {
                        oldPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                    },
                    passwordRules: {
                        oldPassword: [
                            { required: true, message: '请输入原密码', trigger: 'blur' },
                            { min: 6, max: 16, message: '密码长度为6到16个字符', trigger: 'blur' }
                        ],
                        newPassword: [
                            { required: true, message: '请输入新密码', trigger: 'blur' },
                            { min: 6, max: 16, message: '密码长度应为6到16个字符', trigger: 'blur' }
                        ],
                        confirmPassword: [
                            { required: true, message: '请确认密码', trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    if (value !== this.passwordForm.newPassword) {
                                        callback(new Error('两次输入的密码不一致'));
                                    } else {
                                        callback();
                                    }
                                },
                                trigger: 'blur'
                            }
                        ]
                    },
                    unreadCount: 0,
                    todoTickets: [],
                    eventHub: new Vue()
                }
            },
            methods: {
                // 获取用户信息
                async getUserInfo() {
                    try {
                        // console.log('获取用户信息开始：'+API.auth.getUserInfo);
                        const res = await this.$http.get(API.auth.getUserInfo);
                        console.log('获取用户信息响应:', res.data);
                        if (res.data.code === 200) {
                            // 处理嵌套的 data 结构
                            const userData = res.data.data || {};
                            // console.log('用户信息:', userData);
                            this.userInfo = {
                                username: userData.username || userData.account,
                                realname: userData.realname || userData.nickname,
                                department_id: userData.department_id || userData.deptId
                            };
                            localStorage.setItem('userInfo', JSON.stringify(this.userInfo));
                            return this.userInfo;
                        }
                        throw new Error(res.data.message || '获取用户信息失败');
                    } catch (error) {
                        console.error('获取用户信息失败:', error);
                        return null;
                    }
                },

                // 退出登录处理函数
                async handleLogout() {
                    try {
                        await this.$confirm('确定退出系统吗？', '提示', {
                            type: 'warning'
                        });
                        await this.logout();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('退出失败:', error);
                        }
                    }
                },

                // 退出登录
                async logout() {
                    try {
                        await this.$http.post('/auth/logout');
                    } catch (error) {
                        console.error('退出登录失败:', error);
                    } finally {
                        // 清除所有用户相关信息
                        localStorage.removeItem('token');
                        localStorage.removeItem('userInfo');
                        localStorage.removeItem('permissions');
                        window.location.href = '/login.html';
                    }
                },

                // 显示修改密码对话框
                showChangePassword() {
                    this.passwordForm = {
                        oldPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                    };
                    this.passwordDialogVisible = true;
                    // 重置表单验证
                    this.$nextTick(() => {
                        if (this.$refs.passwordForm) {
                            this.$refs.passwordForm.resetFields();
                        }
                    });
                },

                // 提交修改密码
                submitChangePassword() {
                    this.$refs.passwordForm.validate((valid) => {
                        if (valid) {
                            this.$http.post(API.auth.changePassword, {
                                old_password: this.passwordForm.oldPassword,
                                new_password: this.passwordForm.newPassword
                            })
                                .then(response => {
                                    console.log('修改密码响应:', response.data);
                                    if (response.data.code === 200) {
                                        this.$message.success('密码修改成功，请重新登录');
                                        this.passwordDialogVisible = false;
                                        
                                        // 清除 token 并延迟跳转
                                        localStorage.removeItem('token');
                                        setTimeout(() => {
                                            window.location.href = '/login.html';
                                        }, 1500);
                                    } else {
                                        this.$message.error(response.data.message);
                                    }
                                })
                                .catch(error => {
                                    console.error('密码修改失败:', error);
                                    this.$message.error('密码修改失败');
                                });
                        }
                    });
                },

                // 初始化应用
                async initializeApp() {
                    try {
                        const token = localStorage.getItem('token');
                        const userInfoStr = localStorage.getItem('userInfo');
                        
                        // 如果路由守卫已经跳转，这里不需要再次处理
                        if (!token || !userInfoStr) {
                            return; // 由路由守卫处理跳转
                        }
                        
                        // console.log('[初始化] 开始应用初始化');
                        if (!this.$http) {
                            throw new Error('HTTP 实例未初始化');
                        }
                        
                        // console.log('token:', token);
                        // console.log('userInfoStr:', userInfoStr);
                        let userInfo = null;
                        
                        try {
                            userInfo = JSON.parse(userInfoStr || '{}');
                            // console.log('userInfo:', userInfo);
                            // console.log('解析用户信息:', userInfo);
                        } catch (e) {
                            console.error('解析用户信息失败:', e);
                        }

                        // 基础检查
                        // console.log('token:', token);
                        // console.log('userInfoStr:', userInfoStr);
                        if (!token || !userInfoStr) {
                            console.log('[初始化] 缺少认证信息');
                            throw new Error('未登录');
                        }

                        // 获取最新用户信息
                        const freshUserInfo = await this.getUserInfo();
                        if (!freshUserInfo) {
                            // console.log('[初始化] 获取用户信息失败');
                            throw new Error('用户信息无效');
                        }

                        // 验证必要字段
                        if (!freshUserInfo.username || !freshUserInfo.department_id) {
                            // console.log('[初始化] 用户信息不完整');
                            throw new Error('用户信息不完整');
                        }

                        // console.log('[初始化] 完成');
                        this.loading = false;
                    } catch (error) {
                        // console.error('[初始化] 失败:', error);
                        this.$message.error('系统初始化失败: ' + error.message);
                        localStorage.clear();
                        window.location.href = '/login.html';
                    }
                },

                // 获取待办工单
                async fetchTodoTickets() {
                    try {
                        // console.log('[待办工单] 开始请求，接口地址:', API.ticket.todo);
                        const res = await this.$http.get('/ticket_new/todo', {
                            params: {
                                page: 1,
                                limit: 5,
                                status: 'pending,processing'
                            }
                        });
                        // console.log('[待办工单] 响应数据:', res.data);

                        if (res.data.code === 200) {
                            this.todoTickets = res.data.data.items || [];
                            this.unreadCount = this.todoTickets.length || 0;
                        } else if (res.data.code === 404) {
                            // console.warn('[待办工单] 接口未实现，使用模拟数据');
                            this.todoTickets = this.generateMockTickets();
                            this.unreadCount = this.todoTickets.length || 0;
                        }
                    } catch (error) {
                        // console.error('[待办工单] 请求失败:', {
                        //     message: error.message,
                        //     config: error.config,
                        //     response: error.response?.data
                        // });
                        
                        // 显示更友好的错误提示
                        const errorMsg = error.response?.data?.message || 
                                      error.message || 
                                      '网络连接异常';
                        this.$message.error(`获取待办工单失败: ${errorMsg}`);
                        
                        this.todoTickets = [];
                        this.unreadCount = 0;
                    }
                },

                // 处理工单点击
                handleTicketCommand(ticket) {
                    const target = {
                        path: '/ticket/todo',
                        query: { highlight: ticket.id }
                    };
                    if (this.$route.path !== target.path) {
                        this.$router.push(target).catch(err => {
                            if (err.name !== 'NavigationDuplicated') {
                                console.error('路由跳转失败:', err);
                            }
                        });
                    }
                },

                // 查看全部工单
                viewAllTickets() {
                    if (this.$route.path !== '/ticket/todo') {
                        this.$router.push('/ticket/todo');
                    }
                },

                // 获取优先级类型
                getPriorityType(priority) {
                    const types = {
                        'high': 'danger',
                        'medium': 'warning',
                        'low': 'info'
                    };
                    return types[priority] || 'info';
                },

                // 获取优先级标签
                getPriorityLabel(priority) {
                    const labels = {
                        'high': '高',
                        'medium': '中',
                        'low': '低'
                    };
                    return labels[priority] || priority;
                },

                statusType(status) {
                    const types = {
                        'pending': 'danger',
                        'processing': 'warning',
                        'completed': 'success'
                    };
                    return types[status] || 'info';
                },

                statusLabel(status) {
                    const labels = {
                        'pending': '待处理',
                        'processing': '进行中',
                        'completed': '已完成'
                    };
                    return labels[status] || status;
                },

                generateMockTickets() {
                    return [
                        {
                            id: 1,
                            ticket_no: 'GD-20230801-001',
                            title: '光伏板清洁维护',
                            priority: 'high',
                            status: 'pending',
                            expect_time: '2023-08-25',
                            create_at: '2023-08-01 09:00'
                        },
                        {
                            id: 2,
                            ticket_no: 'GD-20230801-002',
                            title: '逆变器故障检修',
                            priority: 'medium',
                            status: 'processing',
                            expect_time: '2023-08-28',
                            create_at: '2023-08-01 14:30'
                        }
                    ];
                }
            },
            created() {
                this.initializeApp();
                // this.fetchTodoTickets();
                // 每5分钟自动刷新数据
                this.timer = setInterval(() => {
                    //注意：调试完这个要打开，用于定时刷新获取消息盒子。上面也要打开。
                    // this.fetchTodoTickets();
                }, 300000);
            },
            beforeDestroy() {
                if (this.$root.eventHub) {
                    this.$root.eventHub.$off('ticket-updated', this.fetchTodoTickets);
                    this.$root.eventHub.$off('global-error', this.handleGlobalError);
                }
                // 清理所有定时器
                clearInterval(this.timer);
                if (this.refreshTimer) clearInterval(this.refreshTimer);
            },
            mounted() {
                document.getElementById('loading').style.display = 'none';
                if (this.$root.eventHub) {
                    this.$root.eventHub.$on('ticket-updated', this.fetchTodoTickets);
                    this.$root.eventHub.$on('global-error', this.handleGlobalError);
                }
            },
            watch: {
                '$route'(to, from) {
                    console.log('路由变化:', from.path, '=>', to.path);
                }
            }
        });

        // 在初始化时添加调试信息
        // console.log('Token:', localStorage.getItem('token'));
        // console.log('UserInfo:', localStorage.getItem('userInfo'));
    </script>

    <script>
        Vue.config.productionTip = false;
        Vue.config.performance = true;
        
        // 添加全局事件配置
        Vue.prototype.$passiveEvents = {
            wheel: true,
            mousewheel: true
        };
    </script>

</body>
</html>