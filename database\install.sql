/*
Navicat MariaDB Data Transfer

Source Server         : MariaDB
Source Server Version : 100506
Source Host           : localhost:3306
Source Database       : solar_power

Target Server Type    : MariaDB
Target Server Version : 100506
File Encoding         : 65001

Date: 2025-03-17 19:19:16
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for sp_alarm
-- ----------------------------
DROP TABLE IF EXISTS `sp_alarm`;
CREATE TABLE `sp_alarm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `station_id` int(11) NOT NULL COMMENT '电站ID',
  `type` varchar(50) NOT NULL COMMENT '告警类型',
  `level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '告警级别：1-一般，2-重要，3-紧急',
  `content` varchar(255) NOT NULL COMMENT '告警内容',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0-未处理，1-已处理',
  `handle_user_id` int(11) DEFAULT NULL COMMENT '处理人ID',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_result` varchar(255) DEFAULT NULL COMMENT '处理结果',
  `create_at` datetime DEFAULT NULL COMMENT '创建时间',
  `update_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_station` (`station_id`),
  KEY `idx_handle_user` (`handle_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='告警信息表';

-- ----------------------------
-- Table structure for sp_department
-- ----------------------------
DROP TABLE IF EXISTS `sp_department`;
CREATE TABLE `sp_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '部门名称',
  `code` varchar(50) NOT NULL COMMENT '部门编码',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- ----------------------------
-- Table structure for sp_failed_jobs
-- ----------------------------
DROP TABLE IF EXISTS `sp_failed_jobs`;
CREATE TABLE `sp_failed_jobs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for sp_fund_adjustment
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_adjustment`;
CREATE TABLE `sp_fund_adjustment` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `reconciliation_id` int(10) unsigned NOT NULL COMMENT '对账表ID',
  `station_id` int(10) unsigned NOT NULL COMMENT '电站ID',
  `adjustment_type` enum('collection','grid') NOT NULL COMMENT '调整类型',
  `amount` decimal(12,2) NOT NULL COMMENT '调整金额',
  `before_amount` decimal(12,2) NOT NULL COMMENT '调整前金额',
  `after_amount` decimal(12,2) NOT NULL COMMENT '调整后金额',
  `remark` varchar(255) NOT NULL COMMENT '备注说明',
  `operator_id` int(10) unsigned NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `create_time` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_reconciliation` (`reconciliation_id`),
  KEY `idx_station` (`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金手动调整记录表';

-- ----------------------------
-- Table structure for sp_fund_collection_base
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_collection_base`;
CREATE TABLE `sp_fund_collection_base` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `total_serial` varchar(50) DEFAULT '' COMMENT '总序号',
  `arc_id` varchar(50) DEFAULT NULL COMMENT '档案业务编号',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '户名',
  `gongwang_account` varchar(50) DEFAULT NULL COMMENT '户号',
  `county` varchar(100) DEFAULT NULL COMMENT '县区',
  `township` varchar(100) DEFAULT NULL COMMENT '乡镇',
  `village` varchar(100) DEFAULT NULL COMMENT '村名',
  `address` varchar(200) DEFAULT NULL COMMENT '安装地址',
  `id_card` varchar(18) DEFAULT NULL COMMENT '客户身份证号',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `panel_count` int(11) DEFAULT NULL COMMENT '光伏板块数',
  `product_price` decimal(12,2) DEFAULT NULL COMMENT '产品价格',
  `capacity` decimal(10,2) DEFAULT 0.00 COMMENT '装机容量（单位：kW）',
  `income_calculation` decimal(12,2) DEFAULT 0.00 COMMENT '收益计算',
  `archive_status` varchar(50) DEFAULT NULL COMMENT '档案接收情况',
  `receive_bank_card` varchar(20) DEFAULT NULL COMMENT '接收的银行卡',
  `elec_card_number` varchar(20) DEFAULT NULL COMMENT '电费卡卡号',
  `opening_bank` varchar(100) DEFAULT NULL COMMENT '开户行',
  `latest_card_number` varchar(20) DEFAULT NULL COMMENT '最新卡号',
  `latest_opening_bank` varchar(100) DEFAULT NULL COMMENT '最新开户行',
  `card_status` tinyint(4) DEFAULT NULL COMMENT '电站电费卡状态 0-正常 1-冻结 2-注销',
  `card_open_time` datetime DEFAULT NULL COMMENT '开卡时间',
  `state_grid_signed` tinyint(1) DEFAULT 0 COMMENT '国网签约 0-未签约 1-已签约',
  `remarks` text DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_county` (`county`),
  KEY `idx_village` (`village`),
  KEY `idx_account` (`gongwang_account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金归集基础信息表';

-- ----------------------------
-- Table structure for sp_fund_collection_detail
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_collection_detail`;
CREATE TABLE `sp_fund_collection_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_id` int(10) unsigned NOT NULL COMMENT '电站ID',
  `collection_date` date NOT NULL COMMENT '归集日期',
  `collection_year` int(11) NOT NULL COMMENT '归集年份',
  `collection_month` int(11) NOT NULL COMMENT '归集月份',
  `collection_day` int(11) NOT NULL COMMENT '归集日期(日)',
  `collection_type` enum('pos','临商','国网','存现','其他') NOT NULL COMMENT '归集方式',
  `amount` decimal(12,2) NOT NULL COMMENT '归集金额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_collection_date` (`collection_date`),
  KEY `idx_collection_ym` (`collection_year`,`collection_month`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='资金归集明细表';

-- ----------------------------
-- Table structure for sp_fund_collection_monthly
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_collection_monthly`;
CREATE TABLE `sp_fund_collection_monthly` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_id` int(10) unsigned NOT NULL COMMENT '电站ID',
  `collection_year` int(11) NOT NULL COMMENT '归集年份',
  `collection_month` int(11) NOT NULL COMMENT '归集月份',
  `pos_amount` decimal(12,2) DEFAULT 0.00 COMMENT 'POS扣款总额',
  `linshang_amount` decimal(12,2) DEFAULT 0.00 COMMENT '临商银行总额',
  `guowang_amount` decimal(12,2) DEFAULT 0.00 COMMENT '国网总额',
  `cash_amount` decimal(12,2) DEFAULT 0.00 COMMENT '存现总额',
  `other_amount` decimal(12,2) DEFAULT 0.00 COMMENT '其他方式总额',
  `total_amount` decimal(12,2) DEFAULT 0.00 COMMENT '月度总额',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_year_month` (`station_id`,`collection_year`,`collection_month`),
  KEY `idx_collection_ym` (`collection_year`,`collection_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金归集月度汇总表';

-- ----------------------------
-- Table structure for sp_fund_reconciliation
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_reconciliation`;
CREATE TABLE `sp_fund_reconciliation` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `station_id` int(10) unsigned NOT NULL,
  `gongwang_account` varchar(50) NOT NULL,
  `total_serial` varchar(50) NOT NULL,
  `reconcile_month` varchar(7) NOT NULL COMMENT '对账月份 yyyy-MM',
  `collection_amount` decimal(12,2) NOT NULL COMMENT '归集金额',
  `grid_amount` decimal(12,2) NOT NULL COMMENT '国网金额',
  `difference` decimal(12,2) GENERATED ALWAYS AS (`collection_amount` - `grid_amount`) VIRTUAL,
  `status` enum('balanced','unbalanced') NOT NULL DEFAULT 'unbalanced',
  `create_time` datetime NOT NULL DEFAULT current_timestamp(),
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_month_station` (`reconcile_month`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金对账表';

-- ----------------------------
-- Table structure for sp_fund_rent
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_rent`;
CREATE TABLE `sp_fund_rent` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `arc_id` varchar(50) DEFAULT NULL COMMENT '序号/档案ID',
  `station_id` int(10) unsigned NOT NULL COMMENT '电站ID',
  `department_name` varchar(100) DEFAULT '' COMMENT '所属部门',
  `year` int(11) DEFAULT NULL COMMENT '项目年度',
  `rent_year` int(11) DEFAULT NULL COMMENT '租金发放年度',
  `gongwang_account` varchar(50) NOT NULL COMMENT '国网号',
  `total_serial` varchar(50) NOT NULL COMMENT '总序号',
  `payment_date` date NOT NULL COMMENT '发放日期',
  `actual_amount` decimal(12,2) NOT NULL COMMENT '实发租金',
  `payment_status` varchar(50) DEFAULT NULL COMMENT '发放状态',
  `payment_method` enum('bank','cash','other') NOT NULL COMMENT '发放方式',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `county` varchar(100) DEFAULT NULL COMMENT '县区',
  `town` varchar(100) DEFAULT NULL COMMENT '乡镇',
  `village` varchar(100) DEFAULT NULL COMMENT '村名',
  `bank_card` varchar(100) DEFAULT NULL COMMENT '银行卡号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `expected_amount` decimal(12,2) DEFAULT NULL COMMENT '应发租金',
  `create_time` datetime NOT NULL DEFAULT current_timestamp(),
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_station` (`station_id`),
  KEY `idx_date` (`payment_date`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='租金发放表';

-- ----------------------------
-- Table structure for sp_fund_state_grid
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_state_grid`;
CREATE TABLE `sp_fund_state_grid` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `station_id` int(10) unsigned NOT NULL,
  `gongwang_account` varchar(50) NOT NULL COMMENT '国网户号/项目编号',
  `total_serial` varchar(50) NOT NULL COMMENT '总序号',
  `unit` varchar(100) DEFAULT NULL COMMENT '单位',
  `powerSupplyOffice` varchar(100) DEFAULT NULL COMMENT '所属供电所',
  `projectName` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `feeMonth` varchar(20) DEFAULT NULL COMMENT '电费年月',
  `payment_date` date NOT NULL COMMENT '结算日期',
  `customerType` varchar(50) DEFAULT NULL COMMENT '客户类别',
  `isPovertyProject` tinyint(1) DEFAULT 0 COMMENT '是否扶贫项目',
  `povertyProjectType` varchar(50) DEFAULT NULL COMMENT '扶贫项目类型',
  `isInSubsidyList` tinyint(1) DEFAULT 0 COMMENT '是否已纳入补贴目录',
  `energyType` varchar(50) DEFAULT NULL COMMENT '电能类型',
  `gridConnectionType` varchar(50) DEFAULT NULL COMMENT '上网类型',
  `isSigned` tinyint(1) DEFAULT 0 COMMENT '是否签约',
  `installedCapacity` decimal(10,2) DEFAULT NULL COMMENT '装机容量',
  `gridPrice` decimal(10,4) DEFAULT NULL COMMENT '上网电价',
  `subsidyStandard` decimal(10,4) DEFAULT NULL COMMENT '补助标准',
  `generationAmount` decimal(15,2) DEFAULT NULL COMMENT '发电量',
  `gridConnectionAmount` decimal(15,2) DEFAULT NULL COMMENT '上网电量',
  `totalPayableAmount` decimal(15,2) DEFAULT NULL COMMENT '总应付金额',
  `totalPayableCost` decimal(15,2) DEFAULT NULL COMMENT '总应付成本',
  `totalPayableTax` decimal(15,2) DEFAULT NULL COMMENT '总应付税额',
  `payablePurchaseFee` decimal(15,2) DEFAULT NULL COMMENT '应付购电费',
  `payablePurchaseCost` decimal(15,2) DEFAULT NULL COMMENT '应付购电成本',
  `payablePurchaseTax` decimal(15,2) DEFAULT NULL COMMENT '应付购电税额',
  `centralSubsidyAmount` decimal(15,2) DEFAULT NULL COMMENT '中央补助金额',
  `centralSubsidyCost` decimal(15,2) DEFAULT NULL COMMENT '中央补助成本',
  `centralSubsidyTax` decimal(15,2) DEFAULT NULL COMMENT '中央补助税额',
  `provincialSubsidyAmount` decimal(15,2) DEFAULT NULL COMMENT '省级补助金额',
  `provincialSubsidyCost` decimal(15,2) DEFAULT NULL COMMENT '省级补助成本',
  `provincialSubsidyTax` decimal(15,2) DEFAULT NULL COMMENT '省级补助税额',
  `settlementAuditBatch` varchar(100) DEFAULT NULL COMMENT '结算审核批次',
  `amount` decimal(12,2) NOT NULL COMMENT '打款金额',
  `voucher_no` varchar(50) DEFAULT NULL COMMENT '凭证号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT current_timestamp(),
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_station` (`station_id`),
  KEY `idx_gongwang_account` (`gongwang_account`),
  KEY `idx_payment_date` (`payment_date`)
) ENGINE=InnoDB AUTO_INCREMENT=460 DEFAULT CHARSET=utf8mb4 COMMENT='国网打款表';

-- ----------------------------
-- Table structure for sp_fund_village
-- ----------------------------
DROP TABLE IF EXISTS `sp_fund_village`;
CREATE TABLE `sp_fund_village` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `project_year` int(11) DEFAULT NULL COMMENT '项目建设年度',
  `payment_year` int(11) DEFAULT NULL COMMENT '发放年度',
  `county` varchar(100) DEFAULT NULL COMMENT '所在县',
  `township` varchar(100) DEFAULT NULL COMMENT '所在乡镇',
  `village` varchar(100) DEFAULT NULL COMMENT '所在村',
  `payment_date` date NOT NULL,
  `amount` decimal(12,2) NOT NULL,
  `payment_status` varchar(50) DEFAULT NULL COMMENT '发放状态',
  `recipient` varchar(100) NOT NULL COMMENT '收款方',
  `bank_account` varchar(100) NOT NULL COMMENT '银行账户',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT current_timestamp(),
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_township` (`township`),
  KEY `idx_village` (`village`),
  KEY `idx_project_year` (`project_year`),
  KEY `idx_payment_year` (`payment_year`)
) ENGINE=InnoDB AUTO_INCREMENT=1742 DEFAULT CHARSET=utf8mb4 COMMENT='村集体打款表';

-- ----------------------------
-- Table structure for sp_jobs
-- ----------------------------
DROP TABLE IF EXISTS `sp_jobs`;
CREATE TABLE `sp_jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for sp_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sp_login_log`;
CREATE TABLE `sp_login_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '登录状态：0-失败，1-成功',
  `message` varchar(255) DEFAULT NULL COMMENT '登录消息',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`create_at`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志';

-- ----------------------------
-- Table structure for sp_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sp_operation_log`;
CREATE TABLE `sp_operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名',
  `module` varchar(50) NOT NULL DEFAULT '' COMMENT '操作模块',
  `action` varchar(50) NOT NULL DEFAULT '' COMMENT '操作类型',
  `method` varchar(10) NOT NULL DEFAULT '' COMMENT '请求方法',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '请求URL',
  `params` text DEFAULT NULL COMMENT '请求参数',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`create_at`)
) ENGINE=InnoDB AUTO_INCREMENT=568 DEFAULT CHARSET=utf8mb4 COMMENT='用户操作日志';

-- ----------------------------
-- Table structure for sp_permission
-- ----------------------------
DROP TABLE IF EXISTS `sp_permission`;
CREATE TABLE `sp_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限标识',
  `type` varchar(20) NOT NULL COMMENT '权限类型：menu,button,api',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `path` varchar(100) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(100) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=304 DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- ----------------------------
-- Table structure for sp_role
-- ----------------------------
DROP TABLE IF EXISTS `sp_role`;
CREATE TABLE `sp_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- ----------------------------
-- Table structure for sp_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sp_role_permission`;
CREATE TABLE `sp_role_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `permission_id` int(11) NOT NULL COMMENT '权限ID',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`,`permission_id`),
  KEY `idx_permission` (`permission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- ----------------------------
-- Table structure for sp_station
-- ----------------------------
DROP TABLE IF EXISTS `sp_station`;
CREATE TABLE `sp_station` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `total_serial` varchar(255) DEFAULT '' COMMENT '总序号',
  `department_id` int(11) DEFAULT 0 COMMENT '所属部门ID',
  `arc_id` int(11) DEFAULT 0 COMMENT '部室档案次序号',
  `station_name` varchar(100) DEFAULT '' COMMENT '电站名称',
  `address` varchar(255) DEFAULT NULL COMMENT '电站地址',
  `capacity` decimal(10,2) DEFAULT NULL COMMENT '装机容量(kW)',
  `product_price` decimal(10,2) DEFAULT NULL COMMENT '产品价格',
  `archive_status` varchar(50) DEFAULT NULL COMMENT '档案接收情况',
  `bank_card_status` varchar(50) DEFAULT NULL COMMENT '银行卡接收状态',
  `electricity_card` varchar(100) DEFAULT NULL COMMENT '电费卡卡号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '电费卡开户行',
  `bank_card` varchar(100) DEFAULT NULL COMMENT '最新银行卡号',
  `bank_branch` varchar(100) DEFAULT NULL COMMENT '银行卡开户行',
  `card_status` varchar(50) DEFAULT NULL COMMENT '电站电费卡状态',
  `card_open_time` varchar(50) DEFAULT NULL COMMENT '开卡时间',
  `grid_contract_status` varchar(50) DEFAULT NULL COMMENT '国网签约状态',
  `install_date` date DEFAULT NULL COMMENT '并网日期',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-停用，1-正常',
  `type` tinyint(1) DEFAULT 1 COMMENT '电站类型：1-合作农户，2-工商业',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(50) DEFAULT '' COMMENT '联系电话',
  `business_type` varchar(50) DEFAULT NULL COMMENT '企业类型（工商业）',
  `contract_number` varchar(50) DEFAULT NULL COMMENT '合同编号',
  `create_at` datetime DEFAULT NULL COMMENT '创建时间',
  `update_at` datetime DEFAULT NULL COMMENT '更新时间',
  `gongwang_account` varchar(255) DEFAULT NULL COMMENT '国网户号',
  `id_card` varchar(255) DEFAULT NULL COMMENT '身份证号',
  `county` varchar(255) DEFAULT NULL COMMENT '县区',
  `town` varchar(100) DEFAULT NULL COMMENT '乡镇',
  `village` varchar(100) DEFAULT NULL COMMENT '村名',
  `component_brand` varchar(255) DEFAULT NULL COMMENT '组件品牌',
  `component_count` int(11) DEFAULT NULL COMMENT '组件数量',
  `component_power` decimal(10,0) DEFAULT NULL COMMENT '组件功率（w）',
  `inverter_brand` varchar(255) DEFAULT NULL COMMENT '逆变器品牌',
  `inverter_serial` varchar(255) DEFAULT NULL COMMENT '逆变器序列号',
  `fixed_income` decimal(10,0) DEFAULT NULL COMMENT '固定收益',
  PRIMARY KEY (`id`),
  KEY `idx_department` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19080 DEFAULT CHARSET=utf8mb4 COMMENT='电站信息表';

-- ----------------------------
-- Table structure for sp_station_archive
-- ----------------------------
DROP TABLE IF EXISTS `sp_station_archive`;
CREATE TABLE `sp_station_archive` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `station_id` int(11) NOT NULL COMMENT '电站ID',
  `name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `file_size` int(11) NOT NULL COMMENT '文件大小',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `reserve_time` datetime DEFAULT NULL COMMENT '预留时间字段',
  PRIMARY KEY (`id`),
  KEY `idx_station_id` (`station_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11980 DEFAULT CHARSET=utf8mb4 COMMENT='电站档案表';

-- ----------------------------
-- Table structure for sp_station_problem
-- ----------------------------
DROP TABLE IF EXISTS `sp_station_problem`;
CREATE TABLE `sp_station_problem` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `station_id` int(11) NOT NULL COMMENT '电站ID',
  `problem_type` varchar(20) NOT NULL COMMENT '问题类型',
  `description` text NOT NULL COMMENT '问题描述',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态',
  `handler_id` int(11) DEFAULT NULL COMMENT '处理人ID',
  `register_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '登记时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_station` (`station_id`),
  KEY `idx_handler` (`handler_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='问题电站表';

-- ----------------------------
-- Table structure for sp_station_problem_handler
-- ----------------------------
DROP TABLE IF EXISTS `sp_station_problem_handler`;
CREATE TABLE `sp_station_problem_handler` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `problem_id` int(11) NOT NULL COMMENT '问题ID',
  `handler_id` int(11) NOT NULL COMMENT '处理人ID',
  `create_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_problem_id` (`problem_id`),
  KEY `idx_handler_id` (`handler_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COMMENT='问题电站处理人关联表';

-- ----------------------------
-- Table structure for sp_station_problem_image
-- ----------------------------
DROP TABLE IF EXISTS `sp_station_problem_image`;

-- ----------------------------
-- Table structure for sp_business_station
-- ----------------------------
DROP TABLE IF EXISTS `sp_business_station`;
CREATE TABLE `sp_business_station` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department` varchar(50) NOT NULL COMMENT '部室',
  `company_name` varchar(100) NOT NULL COMMENT '单位名称',
  `address` varchar(255) NOT NULL COMMENT '地址',
  `cooperation_mode` varchar(100) NOT NULL COMMENT '合作模式',
  `contact_method` varchar(100) NOT NULL COMMENT '对接方式',
  `account_number` varchar(50) NOT NULL COMMENT '户号',
  `capacity` decimal(10,2) NOT NULL COMMENT '装机容量（兆瓦）',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价（元/瓦）',
  `investment_amount` decimal(12,2) DEFAULT NULL COMMENT '投资额（万元）',
  `panel_brand_power` varchar(100) DEFAULT NULL COMMENT '组件品牌功率（瓦）',
  `panel_count` int(11) DEFAULT NULL COMMENT '组件块数',
  `inverter_count` int(11) DEFAULT NULL COMMENT '逆变器数量',
  `grid_connection_time` date DEFAULT NULL COMMENT '并网时间',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_department` (`department`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_account_number` (`account_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工商业光伏项目表';
CREATE TABLE `sp_station_problem_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `problem_id` int(11) NOT NULL COMMENT '问题ID',
  `image_path` varchar(255) NOT NULL COMMENT '图片路径',
  `create_at` datetime DEFAULT NULL COMMENT '上传时间',
  PRIMARY KEY (`id`),
  KEY `idx_problem_id` (`problem_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='问题电站图片表';

-- ----------------------------
-- Table structure for sp_tag
-- ----------------------------
DROP TABLE IF EXISTS `sp_tag`;
CREATE TABLE `sp_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) NOT NULL DEFAULT '#1890ff' COMMENT '标签颜色',
  `used_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `create_at` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- ----------------------------
-- Table structure for sp_task_queue
-- ----------------------------
DROP TABLE IF EXISTS `sp_task_queue`;
CREATE TABLE `sp_task_queue` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `task_data` text NOT NULL COMMENT '任务数据(JSON)',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
  `progress` int(11) NOT NULL DEFAULT 0 COMMENT '进度百分比',
  `result` text DEFAULT NULL COMMENT '任务结果',
  `error` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`),
  KEY `task_type_status` (`task_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for sp_ticket
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket`;
CREATE TABLE `sp_ticket` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_no` varchar(20) NOT NULL COMMENT '工单编号',
  `uid` varchar(50) DEFAULT NULL COMMENT '临时唯一标识',
  `title` varchar(100) NOT NULL COMMENT '工单标题',
  `content` text NOT NULL COMMENT '工单内容',
  `station_id` int(11) NOT NULL DEFAULT 0 COMMENT '电站ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `priority` enum('low','medium','high') NOT NULL DEFAULT 'medium' COMMENT '优先级',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中,2已处理',
  `creator_id` int(11) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) NOT NULL COMMENT '创建人姓名',
  `expect_time` datetime DEFAULT NULL COMMENT '期望完成时间',
  `feedback` text DEFAULT NULL COMMENT '处理结果',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `department_id` int(11) NOT NULL DEFAULT 0 COMMENT '处理部门ID',
  `handlers_count` int(11) DEFAULT 0 COMMENT '处理人数量',
  `complete_handlers_count` int(11) DEFAULT 0 COMMENT '已完成处理的人数',
  `first_handling_at` datetime DEFAULT NULL COMMENT '首次开始处理时间',
  `last_complete_at` datetime DEFAULT NULL COMMENT '最后完成时间',
  `completed_handlers_count` int(11) DEFAULT NULL,
  `transfer_count` int(11) DEFAULT 0 COMMENT '转派次数',
  PRIMARY KEY (`id`),
  KEY `idx_ticket_no` (`ticket_no`),
  KEY `idx_station` (`station_id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_creator` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`create_at`),
  KEY `idx_uid` (`uid`),
  KEY `idx_transfer_count` (`transfer_count`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COMMENT='工单表';

-- ----------------------------
-- Table structure for sp_ticket_attachment
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_attachment`;
CREATE TABLE `sp_ticket_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) DEFAULT 0 COMMENT '工单ID',
  `handler_id` int(11) DEFAULT NULL,
  `handler_name` varchar(50) DEFAULT '',
  `uid` varchar(50) DEFAULT NULL COMMENT '临时唯一标识',
  `name` varchar(100) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `path` varchar(255) NOT NULL COMMENT '文件路径',
  `size` int(11) NOT NULL COMMENT '文件大小(字节)',
  `type` varchar(50) NOT NULL COMMENT '文件类型',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_ticket` (`ticket_id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=125 DEFAULT CHARSET=utf8mb4 COMMENT='工单附件表';

-- ----------------------------
-- Table structure for sp_ticket_category
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_category`;
CREATE TABLE `sp_ticket_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类编码',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='工单分类表';

-- ----------------------------
-- Table structure for sp_ticket_handler
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_handler`;
CREATE TABLE `sp_ticket_handler` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL COMMENT '工单ID',
  `handler_id` int(11) NOT NULL COMMENT '处理人ID',
  `handler_name` varchar(50) NOT NULL COMMENT '处理人姓名',
  `department_id` int(11) NOT NULL COMMENT '部门ID',
  `department_name` varchar(255) DEFAULT '',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已处理',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `accepted_at` datetime DEFAULT NULL COMMENT '接受时间',
  `rejected_at` datetime DEFAULT NULL COMMENT '拒绝时间',
  `complete_at` datetime DEFAULT NULL COMMENT '开始处理时间',
  `feedback` varchar(500) DEFAULT '' COMMENT '处理备注',
  `handling_time` int(11) DEFAULT 0 COMMENT '处理时长(分钟)',
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ticket_handler` (`ticket_id`,`handler_id`),
  KEY `idx_handler_id_status` (`handler_id`,`status`),
  CONSTRAINT `fk_ticket_handler_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `sp_ticket` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COMMENT='工单处理人关联表';

-- ----------------------------
-- Table structure for sp_ticket_tag
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_tag`;
CREATE TABLE `sp_ticket_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) NOT NULL DEFAULT '#1890ff' COMMENT '标签颜色',
  `used_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='工单标签表';

-- ----------------------------
-- Table structure for sp_ticket_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_tag_relation`;
CREATE TABLE `sp_ticket_tag_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL COMMENT '工单ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ticket_tag` (`ticket_id`,`tag_id`),
  KEY `idx_tag` (`tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='工单标签关联表';

-- ----------------------------
-- Table structure for sp_ticket_transfer
-- ----------------------------
DROP TABLE IF EXISTS `sp_ticket_transfer`;
CREATE TABLE `sp_ticket_transfer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL COMMENT '工单ID',
  `from_user_id` int(11) NOT NULL COMMENT '转出人ID',
  `to_user_id` varchar(255) NOT NULL COMMENT '转入人ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '转派说明',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `create_by` int(11) DEFAULT NULL,
  `handler_status_log` text DEFAULT NULL,
  `completed_handlers_count` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ticket` (`ticket_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='工单转派记录表';

-- ----------------------------
-- Table structure for sp_user
-- ----------------------------
DROP TABLE IF EXISTS `sp_user`;
CREATE TABLE `sp_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `realname` varchar(50) NOT NULL COMMENT '真实姓名',
  `department_id` int(11) DEFAULT NULL COMMENT '部门ID',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `role_ids` varchar(255) DEFAULT NULL COMMENT '角色ID，多个用逗号分隔',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_department` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- Table structure for sp_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sp_user_role`;
CREATE TABLE `sp_user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `create_at` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_role` (`user_id`,`role_id`),
  KEY `idx_role` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
