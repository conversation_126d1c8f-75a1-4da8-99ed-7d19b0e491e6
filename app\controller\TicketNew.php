<?php
declare (strict_types = 1);
namespace app\controller;

use app\BaseController;
use app\model\Ticket as TicketModel;
use think\App;
use think\facade\Request;
use think\facade\Log;
use think\facade\Db;
use think\facade\Session;
use think\facade\Filesystem;
use app\common\ExcelHelper;
use app\service\TicketService;
use app\model\TicketAttachment;
use app\service\LogService;

/**
 * 工单控制器（入口路由转发）
 */
class TicketNew extends BaseController
{
    protected $ticketModel;
    protected $ticketService;
    
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->ticketModel = new TicketModel();
        $this->ticketService = new TicketService();
    }

    /**
     * 待办工单列表
     */
    public function todo()
    {
        Log::debug('调用待办工单列表');
        return $this->invokeController('ticket.TodoController', 'index');
    }
    
    /**
     * 已办工单列表
     */
    public function done()
    {
        Log::debug('调用已办工单列表');
        return $this->invokeController('ticket.DoneController', 'index');
    }
    
    /**
     * 工单详情
     */
    public function detail($id)
    {
        Log::debug('调用工单详情，ID: ' . $id);
        return $this->invokeController('ticket.DetailController', 'index', ['id' => $id]);
    }
    
    /**
     * 处理工单
     */
    public function handle($id)
    {
        $action = input('action', '');
        Log::debug('处理工单，ID: ' . $id . '，操作: ' . $action);
        
        if ($action == 'accept') {
            return $this->invokeController('ticket.HandleController', 'accept', ['id' => $id]);
        } else if ($action == 'reject') {
            return $this->invokeController('ticket.HandleController', 'reject', ['id' => $id]);
        } else if ($action == 'complete') {
            return $this->invokeController('ticket.HandleController', 'complete', ['id' => $id]);
        } else {
            return json(['code' => 400, 'message' => '无效的操作类型']);
        }
    }
    
    /**
     * 导出工单
     */
    public function export()
    {
        $type = input('type', 'todo');
        Log::debug('导出工单，类型: ' . $type);
        
        if ($type == 'todo') {
            return $this->invokeController('ticket.TodoController', 'export');
        } else if ($type == 'done') {
            return $this->invokeController('ticket.DoneController', 'export');
        } else {
            return json(['code' => 400, 'message' => '无效的导出类型']);
        }
    }
    
    /**
     * 工单附件上传
     */
    public function upload()
    {
        try {
            $uid = input('uid');
            $ticketId = input('ticketId/d', 0);
            
            Log::info('上传参数验证', [
                'uid' => $uid,
                'ticketId' => $ticketId,
                '完整URL' => request()->url()
            ]);
            
            return $this->invokeController('ticket.AttachmentController', 'upload', [
                'uid' => $uid,
                'ticketId' => $ticketId
            ]);
        } catch (\Exception $e) {
            trace('上传附件异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 工单附件下载
     */
    public function download($id)
    {
        Log::debug('下载工单附件，ID: ' . $id);
        return $this->invokeController('ticket.AttachmentController', 'download', ['id' => $id]);
    }
    
    /**
     * 工单附件删除
     */
    public function deleteAttachment($id)
    {
        Log::debug('删除工单附件，ID: ' . $id);
        return $this->invokeController('ticket.AttachmentController', 'delete', ['id' => $id]);
    }
    
    /**
     * 在办工单列表
     */
    public function list()
    {
        Log::debug('调用在办工单列表');
        try {
            // 获取请求参数
            $params = [
                'page' => input('page/d', 1),
                'limit' => input('limit/d', 10),
                'keyword' => input('keyword/s', ''),
                'priority' => input('priority/s', ''),
                'category_id' => input('category_id/d', 0),
                'date' => input('date/a', [])
            ];
            
            // 如果有日期范围，转换为开始和结束日期
            if (!empty($params['date']) && count($params['date']) == 2) {
                $params['start_date'] = $params['date'][0];
                $params['end_date'] = $params['date'][1];
            }
            
            // 引入ListService类
            $listService = new \app\service\ticket\ListService();
            
            // 使用getProcessingList方法替代直接调用私有方法
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            Log::debug('在办工单列表请求参数: ' . json_encode($params) . ', 用户ID: ' . $userId . ', 是否管理员: ' . ($isAdmin ? 'true' : 'false'));
            
            // 获取在办工单列表
            $result = $listService->getProcessingList($params, $userId, $isAdmin);
            
            Log::debug('在办工单列表结果: ' . json_encode($result));
            return json($result);
        } catch (\Exception $e) {
            Log::error('获取在办工单列表失败: ' . $e->getMessage() . ', 错误堆栈: ' . $e->getTraceAsString());
            return json(['code' => 500, 'message' => '获取工单列表失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 处理工单详情
     */
    public function handling()
    {
        Log::debug('调用处理工单详情');
        try {
            // 获取工单ID
            $ticketId = input('id/d', 0);
            
            if (!$ticketId) {
                Log::warning('处理工单请求缺少工单ID参数');
                return json(['code' => 400, 'message' => '缺少工单ID参数', 'data' => null]);
            }
            
            // 当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            
            // 记录详细日志
            Log::debug('获取处理工单详情', [
                '工单ID' => $ticketId,
                '用户ID' => $userId,
                '是否管理员' => $isAdmin ? '是' : '否'
            ]);
            
            // 引入ListService类
            $listService = new \app\service\ticket\ListService();
            
            // 获取工单详情
            $result = $listService->getTicketDetail($ticketId, $userId, $isAdmin);
            Log::debug('处理工单详情查询结果代码: ' . $result['code'] . ', 消息: ' . $result['message']);
            
            return json($result);
        } catch (\Exception $e) {
            Log::error('获取处理工单详情失败: ' . $e->getMessage() . ', 错误堆栈: ' . $e->getTraceAsString());
            return json(['code' => 500, 'message' => '获取处理工单详情失败: ' . $e->getMessage(), 'data' => null]);
        }
    }
    
    /**
     * 工单转派
     */
    public function transfer($id)
    {
        Log::debug('工单转派，ID: ' . $id);
        return $this->invokeController('ticket.TransferController', 'index', ['id' => $id]);
    }
    
    /**
     * 获取转派用户列表
     */
    public function transferUsers()
    {
        Log::debug('获取转派用户列表');
        return $this->invokeController('ticket.TransferController', 'getUsers');
    }
    
    /**
     * 获取转派部门列表
     */
    public function transferDepartments()
    {
        Log::debug('获取转派部门列表');
        return $this->invokeController('ticket.TransferController', 'getDepartments');
    }
    
    /**
     * 创建工单
     */
    public function create()
    {
        try {
            $data = $this->request->post();
            // 数据验证
            $validate = validate([
                'title' => 'require',
                'content' => 'require',
                'priority' => 'require|in:low,medium,high',
                'category_id' => 'require|number',
                'attachments' => 'array'
            ]);
            if (!$validate->check($data)) {
                return $this->error($validate->getError());
            }
            // 添加创建人信息
            $data['creator_id'] = $this->user->id;
            $data['create_at'] = date('Y-m-d H:i:s');
            $data['status'] = 0;  // 0: 待处理
            // 创建工单
            $ticket = $this->ticketModel->create($data);
            // 处理标签
            if (!empty($data['tags'])) {
                $ticket->tags()->saveAll($data['tags']);
            }
            return $this->success([
                'id' => $ticket->id,
                'message' => '工单创建成功'
            ]);
        } catch (\Exception $e) {
            return $this->error('创建工单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 生成工单编号
     */
    private function generateTicketNo()
    {
        $date = date('Ymd');
        $key = "ticket_no_{$date}";
        // 获取当日工单序号
        $sequence = cache($key) ?: 0;
        $sequence++;
        // 更新序号缓存，有效期到当天结束
        $expire = strtotime(date('Y-m-d 23:59:59')) - time();
        cache($key, $sequence, $expire);
        // 生成工单编号：WO + 日期 + 4位序号
        return 'WO' . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 删除工单
     */
    public function delete($id)
    {
        try {
            Log::debug('开始删除工单，ID: ' . $id);
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            // 只能删除未处理的工单
            if ($ticket->status == 1) {
                return $this->error('已处理的工单不能删除');
            }
            $ticket->delete();
            // 记录操作日志
            LogService::recordOperation('ticket', 'delete', ['id' => $id]);
            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            Log::error('删除工单失败：' . $e->getMessage());
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取工单统计数据
     */
    public function statistics()
    {
        try {
            $today = date('Y-m-d');
            $data = [
                'total' => $this->ticketModel->count(),
                'pending' => $this->ticketModel->where('status', 0)->count(),
                'completed' => $this->ticketModel->where('status', 1)->count(),
                'today' => $this->ticketModel->whereDay('create_at', $today)->count()
            ];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 评价工单
     */
    public function rate($id)
    {
        $score = input('score/d');
        $feedback = input('feedback/s', '');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            if ($ticket->status != 1) {
                return $this->error('工单未完成,不能评价');
            }
            $ticket->score = $score;
            $ticket->feedback = $feedback;
            $ticket->save();
            LogService::recordOperation('ticket', 'rate', [
                'id' => $id,
                'score' => $score,
                'feedback' => $feedback
            ]);
            return $this->success(null, '评价成功');
        } catch (\Exception $e) {
            return $this->error('评价失败：' . $e->getMessage());
        }
    }
    
    /**
     * 修改优先级
     */
    public function updatePriority($id)
    {
        $priority = input('priority/s');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            $ticket->priority = $priority;
            $ticket->save();
            LogService::recordOperation('ticket', 'update_priority', [
                'id' => $id,
                'priority' => $priority
            ]);
            return $this->success(null, '优先级修改成功');
        } catch (\Exception $e) {
            return $this->error('修改失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查超时工单
     */
    public function checkTimeout()
    {
        try {
            $timeout = 24; // 超时时间(小时)
            $tickets = $this->ticketModel
                ->where('status', 0)
                ->whereTime('create_at', '<=', date('Y-m-d H:i:s', strtotime("-{$timeout} hour")))
                ->select();
            foreach ($tickets as $ticket) {
                // 发送超时提醒通知
                $this->sendTimeoutNotification($ticket);
            }
            return $this->success(null, '检查完成');
        } catch (\Exception $e) {
            return $this->error('检查失败：' . $e->getMessage());
        }
    }
    
    /**
     * 发送超时提醒
     */
    private function sendTimeoutNotification($ticket)
    {
        // 实现发送提醒逻辑(邮件/短信/站内信等)
    }
    
    /**
     * 发布工单
     */
    public function publish()
    {
        try {
            Log::debug('开始发布工单');
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            $data = Request::post();
            // 添加创建者信息
            $data['creator_id'] = $this->user->id;
            
            // 验证必要字段
            if (empty($data['department_id'])) {
                return $this->error('请选择处理部门');
            }
            
            // 验证并确保预期完成时间字段存在
            if (empty($data['expect_time'])) {
                return $this->error('请设置预期完成时间');
            }
            
            // 处理顶级部门的特殊情况
            $isTopDepartment = isset($data['is_top_department']) && $data['is_top_department'] == 1;
            trace('是否顶级部门: ' . ($isTopDepartment ? '是' : '否'), 'debug');
            
            // 如果是顶级部门但没有选择处理人，自动获取该部门及其子部门下的所有用户
            if ($isTopDepartment && (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0)) {
                trace('顶级部门自动获取处理人', 'debug');
                // 获取部门ID
                $departmentId = intval($data['department_id']);
                // 查询该部门是否存在并确认是顶级部门
                $department = \think\facade\Db::name('department')->where('id', $departmentId)->find();
                if (!$department || $department['parent_id'] != 0) {
                    return $this->error('部门数据有误，请重新选择');
                }
                // 获取该部门下的所有子部门ID
                $childDepartments = \think\facade\Db::name('department')
                    ->where('parent_id', $departmentId)
                    ->column('id');
                // 将当前部门ID也加入到查询范围
                $allDepartmentIds = array_merge([$departmentId], $childDepartments);
                trace('查询部门IDs: ' . json_encode($allDepartmentIds), 'debug');
                // 查询这些部门下的所有有效用户
                $users = \think\facade\Db::name('user')
                    ->whereIn('department_id', $allDepartmentIds)
                    ->where('status', 1)
                    ->column('id');
                if (!empty($users)) {
                    trace('自动获取到的处理人: ' . json_encode($users), 'debug');
                    $data['handler_ids'] = $users;
                } else {
                    return $this->error('所选部门下没有可用的处理人员');
                }
            }
            // 常规验证处理人
            if (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0) {
                return $this->error('请选择处理人员');
            }
            // 记录请求数据
            trace('工单发布请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            $result = $this->ticketService->publish($data);
            if ($result['code'] === 200) {
                LogService::recordOperation('ticket', 'publish', array_merge(
                    ['id' => $result['data']['id']],
                    array_diff_key($data, ['content' => '', 'attachments' => []])
                ));
            }
            return json($result);
        } catch (\Exception $e) {
            trace('Publish ticket error: ' . $e->getMessage(), 'error');
            return $this->error('发布工单失败：' . $e->getMessage());
        }
    }

    /**
     * 同步工单处理人统计数据
     * @param int $ticketId 工单ID
     */
    public function syncTicketHandlerStats($ticketId)
    {
        try {
            // 获取处理人信息
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->select()
                ->toArray();
            $totalCount = count($handlers);
            $completedCount = 0;
            $firstHandlingAt = null;
            $lastCompletedAt = null;
            foreach ($handlers as $handler) {
                // 统计已完成的处理人
                if ($handler['status'] == 4) {
                    $completedCount++;
                    // 更新最后完成时间
                    if ($handler['updated_at'] && (!$lastCompletedAt || strtotime($handler['updated_at']) > strtotime($lastCompletedAt))) {
                        $lastCompletedAt = $handler['updated_at'];
                    }
                }
                // 更新首次处理时间
                if ($handler['handling_at'] && (!$firstHandlingAt || strtotime($handler['handling_at']) < strtotime($firstHandlingAt))) {
                    $firstHandlingAt = $handler['handling_at'];
                }
            }
            // 更新工单统计字段
            Db::name('ticket')
                ->where('id', $ticketId)
                ->update([
                    'handlers_count' => $totalCount,
                    'completed_handlers_count' => $completedCount,
                    'first_handling_at' => $firstHandlingAt,
                    'last_completed_at' => $lastCompletedAt
                ]);
            return true;
        } catch (\Exception $e) {
            trace('同步工单处理人统计数据失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 更新处理人状态
     */
    public function updateHandlerStatus($ticketId, $handlerId, $status, $feedback = '')
    {
        try {
            // 现有代码保持不变...
            // 在处理人状态更新后，同步工单统计数据
            $this->syncTicketHandlerStats($ticketId);
            return ['code' => 200, 'message' => '更新成功'];
        } catch (\Exception $e) {
            trace('更新处理人状态失败: ' . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '更新失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 受理工单
     */
    public function accept()
    {
        try {
            $params = $this->request->post();
            $ticketId = isset($params['ticket_id']) ? intval($params['ticket_id']) : 0;
            $feedback = isset($params['feedback']) ? $params['feedback'] : '';
            $attachmentIds = isset($params['attachment_ids']) ? $params['attachment_ids'] : [];
            
            trace('用户尝试受理工单，ID='.$ticketId, 'debug');
            
            // 参数验证
            if (empty($ticketId)) {
                trace('缺少必要参数', 'warning');
                return json([
                    'code' => 400,
                    'message' => '缺少工单ID参数'
                ]);
            }
            
            $userId = session('user_id');
            
            // 更新工单状态为"受理中"并保存反馈
            Db::startTrans();
            try {
                // 检查工单是否存在
                $ticket = Db::name('ticket')->where('id', $ticketId)->find();
                if (!$ticket) {
                    Db::rollback();
                    return json([
                        'code' => 404,
                        'message' => '工单不存在'
                    ]);
                }
                
                // 更新工单状态
                Db::name('ticket')->where('id', $ticketId)->update([
                    'status' => 1, // 状态设为"受理中"
                    'update_at' => date('Y-m-d H:i:s')
                ]);
                
                // 保存反馈记录
                Db::name('ticket_handler')->where('ticket_id', $ticketId)->where('handler_id', $userId)->update([
                    'feedback' => $feedback,
                    'status' => 1, // 受理中
                    'accepted_at' => date('Y-m-d H:i:s')
                ]);

                trace("用户受理了工单：{$ticketId}，反馈：{$feedback},handler_id:{$userId}", 'warning');
                // 关联附件
                if (!empty($attachmentIds) && is_array($attachmentIds)) {
                    foreach ($attachmentIds as $attachmentId) {
                        Db::name('ticket_attachment')->where('id', $attachmentId)->update([
                            'ticket_id' => $ticketId
                        ]);
                    }
                }            
                
                
                
                Db::commit();
                
                return json([
                    'code' => 200,
                    'message' => '工单受理成功',
                    'data' => [
                        'ticket_id' => $ticketId,
                        'status' => 1
                    ]
                ]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            trace('受理工单失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '受理工单失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 完成工单
     */
    public function complete()
    {
        try {
            $params = $this->request->post();
            $ticketId = isset($params['ticket_id']) ? intval($params['ticket_id']) : 0;
            $feedback = isset($params['feedback']) ? $params['feedback'] : '';
            $attachmentIds = isset($params['attachment_ids']) ? $params['attachment_ids'] : [];                       

            // 参数验证
            if (empty($ticketId)) {
                trace('缺少必要参数', 'warning');
                return json([
                    'code' => 400,
                    'message' => '缺少工单ID参数'
                ]);
            }
            
            $userId = session('user_id');
            $realname = session('realname');
            // 更新工单状态为"已办结"并保存反馈
            Db::startTrans();
            try {
                // 检查工单是否存在
                $ticket = Db::name('ticket')->where('id', $ticketId)->find();
                if (!$ticket) {
                    Db::rollback();
                    return json([
                        'code' => 404,
                        'message' => '工单不存在'
                    ]);
                }
                
                // 更新工单状态
                Db::name('ticket')->where('id', $ticketId)->update([
                    'status' => 2, // 状态设为"已办结"
                    'last_complete_at' => date('Y-m-d H:i:s'),
                    'update_at' => date('Y-m-d H:i:s')
                ]);
            

                // 更新工单处理记录
                Db::name('ticket_handler')->where([
                    'ticket_id' => $ticketId,
                    'handler_id' => $userId
                ])->update([
                    'feedback' => $feedback,
                    'status' => 2, // 已完成
                    'complete_at' => date('Y-m-d H:i:s'),
                    'handling_time' => time() - strtotime($ticket['create_at']),
                    'update_at' => date('Y-m-d H:i:s')
                ]);
                
                // 关联附件
                if (!empty($attachmentIds) && is_array($attachmentIds)) {
                    foreach ($attachmentIds as $attachmentId) {
                        Db::name('ticket_attachment')->where('id', $attachmentId)->update([
                            'ticket_id' => $ticketId
                        ]);
                    }
                }

                Db::commit();
                
                return json([
                    'code' => 200,
                    'message' => '工单办结成功',
                    'data' => [
                        'ticket_id' => $ticketId,
                        'status' => 2
                    ]
                ]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            trace('办结工单失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '办结工单失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 修复附件关联
     */
    public function fixAttachments()
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 检查是否为管理员
            if (!$this->isAdmin()) {
                return json(['code' => 403, 'message' => '只有管理员可以执行此操作']);
            }
            
            // 获取所有ticket_id为0的附件
            $attachments = Db::name('ticket_attachment')
                ->where('ticket_id', 0)
                ->select()
                ->toArray();
                
            $count = count($attachments);
            if ($count === 0) {
                return json(['code' => 200, 'message' => '没有需要修复的附件']);
            }
            
            // 记录日志
            trace('开始修复附件，共' . $count . '个需要修复', 'debug');
            
            // 获取所有工单
            $tickets = Db::name('ticket')
                ->field('id, title, create_at')
                ->order('create_at', 'desc')
                ->select()
                ->toArray();
                
            if (empty($tickets)) {
                return json(['code' => 200, 'message' => '没有工单数据，无法修复']);
            }
            
            // 按照创建时间匹配附件和工单
            $fixed = 0;
            foreach ($attachments as $attachment) {
                // 找到最接近附件创建时间的工单
                $bestMatch = null;
                $minTimeDiff = PHP_INT_MAX;
                
                foreach ($tickets as $ticket) {
                    $attachTime = strtotime($attachment['create_at']);
                    $ticketTime = strtotime($ticket['create_at']);
                    $timeDiff = abs($attachTime - $ticketTime);
                    
                    if ($timeDiff < $minTimeDiff) {
                        $minTimeDiff = $timeDiff;
                        $bestMatch = $ticket;
                    }
                }
                
                // 如果时间差在1小时内，认为是同一个工单的附件
                if ($bestMatch && $minTimeDiff < 3600) {
                    Db::name('ticket_attachment')
                        ->where('id', $attachment['id'])
                        ->update(['ticket_id' => $bestMatch['id']]);
                    
                    trace('修复附件ID: ' . $attachment['id'] . '，关联到工单ID: ' . $bestMatch['id'], 'debug');
                    $fixed++;
                }
            }
            
            return json([
                'code' => 200, 
                'message' => '附件修复完成，共修复' . $fixed . '个附件，还有' . ($count - $fixed) . '个无法自动修复'
            ]);
        } catch (\Exception $e) {
            trace('修复附件异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 检查登录状态
     */
    protected function checkLogin()
    {
        $userId = session('user_id');
        if (!$userId) {
            return json(['code' => 401, 'message' => '未登录或登录已过期']);
        }
        return true;
    }
    
    /**
     * 检查是否为管理员
     */
    protected function isAdmin()
    {
        $userId = session('user_id');
        // $roleId = session('role_id'); // 原来的代码，不正确
        $roleIds = session('role_ids'); // 使用新的role_ids
        
        // 检查是否为管理员角色
        // return $roleId == 1; // 原来的代码，不正确
        
        // 新的判断逻辑：检查角色ID数组中是否包含超级管理员角色(ID为1)
        return is_array($roleIds) && in_array(1, $roleIds);
    }
    
    /**
     * 检查附件关联状态
     */
    public function checkAttachments($uid = '')
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 如果没有传递uid，从请求中获取
            if (empty($uid)) {
                $uid = input('uid', '');
            }
            
            if (empty($uid)) {
                return json(['code' => 400, 'message' => '请提供uid参数']);
            }
            
            // 查询附件信息
            $attachments = Db::name('ticket_attachment')
                ->where('uid', $uid)
                ->select()
                ->toArray();
                
            // 查询工单信息
            $ticket = Db::name('ticket')
                ->where('uid', $uid)
                ->find();
                
            return json([
                'code' => 200,
                'message' => '查询成功',
                'data' => [
                    'uid' => $uid,
                    'ticket' => $ticket,
                    'attachments' => $attachments,
                    'attachment_count' => count($attachments)
                ]
            ]);
        } catch (\Exception $e) {
            trace('检查附件关联异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 动态调用控制器
     * @param string $controller 控制器名称
     * @param string $action 方法名称
     * @param array $params 参数
     */
    protected function invokeController($controller, $action, $params = [])
    {
        try {
            // 修改获取控制器实例的方式
            $controllerClass = '\\app\\controller\\' . str_replace('.', '\\', $controller);
            Log::debug("尝试实例化控制器类: " . $controllerClass);
            
            if (!class_exists($controllerClass)) {
                Log::error("控制器类 {$controllerClass} 不存在");
                return json(['code' => 404, 'message' => "控制器 {$controller} 不存在"]);
            }
            
            $instance = new $controllerClass(app());
            
            if (!method_exists($instance, $action)) {
                Log::error("方法 {$action} 不存在于控制器 {$controller}");
                return json(['code' => 404, 'message' => "方法 {$action} 不存在"]);
            }
            
            // 传递参数并调用方法
            return call_user_func_array([$instance, $action], $params);
        } catch (\Exception $e) {
            Log::error("调用控制器方法出错: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'message' => '系统错误: ' . $e->getMessage()]);
        }
    }
} 