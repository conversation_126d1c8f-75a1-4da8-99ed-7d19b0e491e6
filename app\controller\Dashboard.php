<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\DashboardService;

class Dashboard extends BaseController
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * 获取总览数据
     */
    public function overview()
    {
        $params = $this->request->get();
        $result = $this->dashboardService->getOverview($params);
        return json($result);
    }

    /**
     * 获取电站统计
     */
    public function stationStats()
    {
        $params = $this->request->get();
        $result = $this->dashboardService->getStationStats($params);
        return json($result);
    }

    /**
     * 获取发电趋势
     */
    public function powerTrend()
    {
        $params = $this->request->get();
        $result = $this->dashboardService->getPowerTrend($params);
        return json($result);
    }

    /**
     * 获取告警统计
     */
    public function alarmStats()
    {
        $params = $this->request->get();
        $result = $this->dashboardService->getAlarmStats($params);
        return json($result);
    }
} 