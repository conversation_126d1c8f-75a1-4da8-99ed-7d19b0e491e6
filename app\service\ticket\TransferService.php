<?php
declare (strict_types = 1);
namespace app\service\ticket;

use think\facade\Db;
use think\facade\Event;
use app\service\BaseService;
use app\model\Ticket;
use app\model\TicketTransfer;

class TransferService extends BaseService
{
    /**
     * 转派工单
     * @param int $id 工单ID
     * @param array $data 转派数据
     * @param int $operatorId 操作人ID
     * @return array
     */
    public function transfer($id, $data, $operatorId = 0)
    {
        Db::startTrans();
        try {
            trace('开始转派工单，ID='.$id.', 目标处理人='.$data['handler_id'], 'debug');
            
            // 验证转派原因
            if (empty($data['remark'])) {
                trace('转派原因不能为空', 'warning');
                return ['code' => 400, 'message' => '请填写转派原因'];
            }
            
            // 获取工单信息
            $ticket = Ticket::find($id);
            if (!$ticket) {
                trace('工单不存在，ID='.$id, 'error');
                return ['code' => 404, 'message' => '工单不存在'];
            }
            
            // 检查工单状态
            if ($ticket->status != 0) {
                trace('已处理的工单不能转派，工单状态='.$ticket->status, 'warning');
                return ['code' => 400, 'message' => '已处理的工单不能转派'];
            }
            
            // 验证目标处理人是否存在
            $targetHandler = Db::name('user')->where('id', $data['handler_id'])->find();
            if (!$targetHandler) {
                trace('目标处理人不存在，ID='.$data['handler_id'], 'error');
                return ['code' => 404, 'message' => '目标处理人不存在'];
            }
            
            // 从关联表中查找当前处理人
            $currentHandlers = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->select()
                ->toArray();
                
            // 记录转派历史前的状态
            $handlerStatusLog = [];
            foreach ($currentHandlers as $handler) {
                $handlerStatusLog[] = [
                    'handler_id' => $handler['handler_id'],
                    'handler_name' => $handler['handler_name'],
                    'status' => $handler['status'],
                    'accepted_at' => $handler['accepted_at'] ?? null,
                    'handling_at' => $handler['handling_at'] ?? null,
                    'handling_time' => $handler['handling_time'] ?? 0
                ];
            }
            
            // 记录当前第一个处理人ID，用于转派记录
            $fromUserId = !empty($currentHandlers) ? $currentHandlers[0]['handler_id'] : 0;
            trace('记录当前处理人状态：'.json_encode($handlerStatusLog), 'debug');
            
            // 先清除原有处理人关联
            Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->delete();
            trace('已清除原有处理人关联', 'debug');
            
            // 添加新处理人信息
            $handler = Db::name('user')
                ->alias('u')
                ->join('department d', 'u.department_id = d.id', 'LEFT')
                ->where('u.id', $data['handler_id'])
                ->field([
                    'u.id', 
                    'u.username', 
                    'u.realname', 
                    'u.department_id',
                    'd.name as department_name'
                ])
                ->find();
                
            if ($handler) {
                // 创建新的处理人关联
                Db::name('ticket_handler')->insert([
                    'ticket_id' => $id,
                    'handler_id' => $handler['id'],
                    'handler_name' => $handler['realname'] ?: $handler['username'],
                    'department_id' => $handler['department_id'] ?: 0,
                    'department_name' => $handler['department_name'] ?: '',
                    'status' => 0,
                    'create_at' => date('Y-m-d H:i:s')
                ]);
                trace('已添加新处理人，ID='.$handler['id'], 'debug');
            } else {
                throw new \Exception('未找到处理人信息');
            }
            
            // 更新工单转派信息
            $ticket->transfer_reason = $data['remark'] ?? '';
            $ticket->transfer_time = date('Y-m-d H:i:s');
            $ticket->transfer_count = Db::raw('transfer_count + 1'); // 增加转派次数计数
            $ticket->last_transfer_user_id = $operatorId; // 记录最后转派人
            $ticket->save();
            trace('已更新工单转派信息', 'debug');
            
            // 记录转派历史
            Db::name('ticket_transfer')->insert([
                'ticket_id' => $id,
                'from_user_id' => $fromUserId,
                'to_user_id' => $data['handler_id'],
                'remark' => $data['remark'] ?? '',
                'create_by' => $operatorId,
                'handler_status_log' => json_encode($handlerStatusLog),
                'create_at' => date('Y-m-d H:i:s')
            ]);
            trace('已记录转派历史', 'debug');
            
            // 更新工单处理人计数
            Db::name('ticket')
                ->where('id', $id)
                ->update([
                    'handlers_count' => 1,
                    'completed_handlers_count' => 0
                ]);
            
            // 发送通知
            $this->sendTransferNotification($ticket, $handler, $data['remark'] ?? '');
            trace('工单转派完成', 'debug');
            
            Db::commit();
            return ['code' => 200, 'message' => '转派成功'];
        } catch (\Exception $e) {
            Db::rollback();
            trace('转派失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return ['code' => 500, 'message' => '转派失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 发送工单转派通知
     * 
     * @param Ticket $ticket 工单对象
     * @param array $handler 新处理人信息
     * @param string $reason 转派原因
     * @return bool
     */
    protected function sendTransferNotification($ticket, $handler, $reason)
    {
        try {
            // 触发工单转派事件，由事件监听器处理实际通知发送
            Event::trigger('ticket_transferred', [
                'ticket' => $ticket,
                'handler' => $handler,
                'reason' => $reason,
                'transfer_time' => date('Y-m-d H:i:s')
            ]);
            trace('已触发工单转派通知事件', 'debug');
            
            // 创建系统通知
            $this->createTransferNotification($ticket, $handler, $reason);
            
            return true;
        } catch (\Exception $e) {
            trace('发送转派通知失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 创建转派通知
     * 
     * @param Ticket $ticket 工单对象
     * @param array $handler 新处理人信息
     * @param string $reason 转派原因
     * @return bool
     */
    protected function createTransferNotification($ticket, $handler, $reason)
    {
        try {
            // 获取转派人信息
            $fromUser = Db::name('user')
                ->where('id', session('user_id'))
                ->field(['id', 'username', 'realname'])
                ->find();
                
            $fromName = $fromUser['realname'] ?: $fromUser['username'];
            $toName = $handler['realname'] ?: $handler['username'];
            
            // 创建通知给新处理人
            $notification = [
                'user_id' => $handler['id'],
                'title' => '工单转派通知',
                'content' => "工单由 {$fromName} 转派给 {$toName}，原因：{$reason}",
                'type' => 'ticket_transfer',
                'relation_id' => $ticket['id'],
                'read' => 0,
                'create_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('notification')->insert($notification);
            trace("工单转派通知已发送给新处理人(ID:{$handler['id']})", 'debug');
            
            return true;
        } catch (\Exception $e) {
            trace("创建转派通知失败：" . $e->getMessage(), 'error');
            return false;
        }
    }
} 