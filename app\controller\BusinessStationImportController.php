<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
// 移除模型引用，因为 create/update 内部未引用，且 index 已改用 Db::name
// use app\model\BusinessStation;
use think\facade\Db;
use think\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\facade\Log;
use think\facade\Config; // 确保 Config 被正确 use

class BusinessStationImportController extends BaseController
{
    public function import(Request $request)
    {
        $file = $request->file('file');
        if (!$file || !$file->isValid()) { 
            Log::warning('工商业导入失败：文件上传失败或文件无效', ['error' => $file ? $file->getError() : 'No file received']);
            return json(['code' => 400, 'message' => '文件上传失败或文件无效']);
         } 
        $uploadDir = runtime_path() . 'upload' . DIRECTORY_SEPARATOR . 'business_import';
        $tempPath = '';

        // try { // 保持注释
            $info = $file->move($uploadDir);
            if (!$info) { 
                $error = $file->getError();
                Log::error('工商业导入失败：文件移动失败', ['error' => $error, 'targetDir' => $uploadDir]);
                return json(['code' => 500, 'message' => '文件保存失败: ' . $error]);
             } 
            $tempPath = $info->getPathname();
            Log::info('工商业导入文件已暂存：' . $tempPath);

            $spreadsheet = IOFactory::load($tempPath);
            $sheet = $spreadsheet->getActiveSheet();
            $highestRow = $sheet->getHighestDataRow();
            $highestColumn = $sheet->getHighestDataColumn();
            $headerRowNum = 1;
            $minDataRowNum = $headerRowNum + 1;
            if ($highestRow < $minDataRowNum) { 
                if (!empty($tempPath) && file_exists($tempPath)) unlink($tempPath);
                Log::warning('工商业导入失败：Excel文件为空或格式不正确', ['file' => $tempPath]);
                return json(['code' => 400, 'message' => 'Excel文件为空或格式不正确（至少需要标题行和一行数据）']);
             } 
            $headerRowData = $sheet->rangeToArray('A' . $headerRowNum . ':' . $highestColumn . $headerRowNum, null, true, false, true)[$headerRowNum];
            if (empty(array_filter($headerRowData))) { 
                 if (!empty($tempPath) && file_exists($tempPath)) unlink($tempPath);
                 Log::warning('工商业导入失败：Excel标题行为空', ['file' => $tempPath]);
                return json(['code' => 400, 'message' => '无法读取Excel标题行或标题行为空']);
             } 

            // 列映射 (保持精确匹配)
            $columnMapping = Config::get('import.business_station_mapping', [
                '部室' => 'department',
                '单位名称' => 'company_name',
                '地址' => 'address',
                '合作模式' => 'cooperation_mode',
                '对接方式' => 'contact_method',
                '户号' => 'account_number',
                '装机容量（兆瓦)' => 'capacity',
                '投资额（万元）' => 'investment_amount',
                '组件品牌功率（瓦）' => 'panel_brand_power',
                '组件块数' => 'panel_count',
                '逆变器数量' => 'inverter_count',
                '并网时间' => 'grid_connection_time',
            ]);

             $dbFieldMap = [];
             foreach ($headerRowData as $colIndex => $colTitle) { 
                 $trimmedTitle = trim(strval($colTitle));
                 if (!empty($trimmedTitle) && isset($columnMapping[$trimmedTitle])) {
                     $dbFieldMap[$colIndex] = $columnMapping[$trimmedTitle];
                 }
              } 
             // 不再强制检查 capacity 映射是否存在，因为所有字段都是 varchar
             if (empty($dbFieldMap)) {
                 if (!empty($tempPath) && file_exists($tempPath)) unlink($tempPath);
                 Log::warning('工商业导入失败：Excel表头与预期模板不符或映射关系错误', ['header' => $headerRowData]);
                 return json(['code' => 400, 'message' => 'Excel表头与预期模板不符，请检查文件或导入配置']);
             }


            $dataToInsert = [];
            // 不再需要 $allErrors, 因为我们不进行验证了

            for ($rowNum = $minDataRowNum; $rowNum <= $highestRow; $rowNum++) {
                 $rowData = $sheet->rangeToArray('A' . $rowNum . ':' . $highestColumn . $rowNum, null, true, false, true)[$rowNum];
                 $item = [];
                 $hasAnyData = false; // 检查行是否完全为空

                foreach ($dbFieldMap as $colIndex => $dbField) {
                     $cellValue = $rowData[$colIndex] ?? null;
                     // --- 核心修改：直接赋值，只做 trim ---
                     $processedValue = ($cellValue !== null) ? trim(strval($cellValue)) : null;

                     // 记录下是否有任何数据
                     if ($processedValue !== null && $processedValue !== '') {
                         $hasAnyData = true;
                     }

                     $item[$dbField] = $processedValue;
                }

                // 如果整行都没有任何数据（所有映射列都为空），则跳过该行
                if (!$hasAnyData) {
                    Log::debug("第 {$rowNum} 行：所有映射列均为空，已跳过。");
                    continue;
                }

                // --- 核心修改：移除所有行级别验证 ---
                // 不再检查 empty()

                // 只要行内有数据就准备插入
                $item['create_at'] = date('Y-m-d H:i:s');
                $item['update_at'] = date('Y-m-d H:i:s');
                $dataToInsert[] = $item;
                Log::debug("第 {$rowNum} 行数据已准备插入:", $item);

            }

            // --- 处理最终结果 ---
            // 不再需要检查 $allErrors

             if (empty($dataToInsert)) {
                 if (!empty($tempPath) && file_exists($tempPath)) unlink($tempPath);
                 Log::info('工商业导入：未在文件中找到有效的可导入数据（或所有行均为空）', ['file' => $tempPath]);
                 return json(['code' => 400, 'message' => '未在文件中找到有效的可导入数据']);
             }

            // 批量插入数据库
            Db::name('business_station')->insertAll($dataToInsert);
            $originalName = $file->getOriginalName();
            Log::info('工商业电站成功导入 ' . count($dataToInsert) . ' 条记录', ['file' => $originalName]);

            if (!empty($tempPath) && file_exists($tempPath)) unlink($tempPath);
            return json(['code' => 200, 'message' => '成功导入 ' . count($dataToInsert) . ' 条记录']);

        // } catch (\Exception $e) { // 保持注释
        //     // ...
        // }
    }
} 