<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\StationService;
use think\facade\Request;
use app\service\LogService;
use think\App;
use think\facade\Db;

class Station extends BaseController
{
    protected $stationService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->stationService = new StationService();
    }

    /**
     * 获取电站列表
     */
    public function index()
    {
        try {
            $checkLogin = $this->checkLogin();
            if ($checkLogin !== true) {
                return $checkLogin;
            }

            $params = Request::get();
            trace('Station list params: ' . json_encode($params), 'debug');

            // 构建查询条件
            $query = Db::name('station');
            
            // 添加is_deleted=0的过滤条件，确保只查询未删除的电站
            $query->where('is_deleted', 0);
            
            if (!empty($params['keyword'])) {
                $query->where('station_name|address', 'like', "%{$params['keyword']}%");
            }
            
            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('status', '=', intval($params['status']));
            }
            
            if (isset($params['type']) && $params['type'] !== '') {
                $query->where('type', '=', intval($params['type']));
            }
            // 添加部门筛选条件
            if (isset($params['department_id']) && $params['department_id'] !== '') {
                $query->where('department_id', '=', intval($params['department_id']));
                trace('Filtering by department_id: ' . $params['department_id'], 'debug');
            }

            // 记录生成的SQL
            trace('Generated SQL before query: ' . $query->fetchSql(true)->select(), 'debug');
            
            // 获取总数
            $total = $query->count();
            trace('Total count: ' . $total, 'debug');

            // 分页查询
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            trace('Pagination params - page: ' . $page . ', limit: ' . $limit, 'debug');
            
            $list = $query->page($page, $limit)
                ->order('id asc')
                ->select()
                ->each(function($item) {
                    // 获取部门信息
                    $item['department'] = Db::name('department')
                        ->where('id', $item['department_id'])
                        ->find();
                    return $item;
                });

            trace('Retrieved items count: ' . count($list), 'debug');
            if(count($list) > 0) {
                trace('First item data: ' . json_encode($list[0]), 'debug');
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'items' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            trace('Station list error: ' . $e->getMessage(), 'error');
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 创建电站
     */
    public function create()
    {
        $data = $this->request->post();
        $result = $this->stationService->create($data);        
        
        return json($result);
    }

    /**
     * 更新电站
     */
    public function update($id)
    {
        $data = $this->request->put();
        $result = $this->stationService->update($id, $data);
        
        return json($result);
    }

    /**
     * 删除电站
     */
    public function delete($id)
    {
        $station = $this->stationService->detail($id);
        $result = $this->stationService->delete($id);
        
        return json($result);
    }

    /**
     * 更改电站状态
     */
    public function changeStatus($id)
    {
        try {
            $params = Request::put();
            
            // 确保 ID 是数字
            $id = intval($id);
            
            // 详细的调试日志
            trace('Change status request - ID: ' . $id, 'debug');
            trace('Request params: ' . json_encode($params), 'debug');

            // 参数验证
            if ($id <= 0) {
                return json(['code' => 400, 'message' => '无效的电站ID']);
            }

            if (!isset($params['status']) || !in_array($params['status'], [0, 1], true)) {
                return json(['code' => 400, 'message' => '无效的状态值']);
            }

            // 调用服务
            $result = $this->stationService->changeStatus($id, $params['status']);
            trace('Service result: ' . json_encode($result), 'debug');
            
            return json($result);
        } catch (\Exception $e) {
            trace('Controller exception: ' . $e->getMessage(), 'error');
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
} 