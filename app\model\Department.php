<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Department extends Model
{
    // 设置表名为 sp_department，不要让框架再添加前缀
    protected $name = 'department';
    // 设置表前缀
    protected $prefix = 'sp_';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    protected $schema = [
        'id'          => 'int',
        'name'        => 'string',
        'code'        => 'string',
        'parent_id'   => 'int',
        'sort'        => 'int',
        'status'      => 'int',
        'create_at' => 'datetime',
        'update_at' => 'datetime',
    ];

    // 获取器：确保返回的 parent_id 为整数
    public function getParentIdAttr($value)
    {
        return intval($value);
    }

    // 关联用户
    public function users()
    {
        return $this->hasMany(User::class, 'department_id', 'id');
    }

    // 关联父级部门
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    // 关联子部门
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    // 获取部门树
    public static function getTree($parentId = 0)
    {
        $departments = self::where('parent_id', $parentId)->select();
        $tree = [];
        
        foreach ($departments as $dept) {
            $node = $dept->toArray();
            $children = self::getTree($dept->id);
            if (!empty($children)) {
                $node['children'] = $children;
            }
            $tree[] = $node;
        }
        
        return $tree;
    }
} 