<?php
declare (strict_types = 1);

namespace app\middleware;

use think\facade\Log as ThinkLog;

class LogMiddleware
{
    public function handle($request, \Closure $next)
    {
        // 记录请求开始
        trace('=== Request Started ===', 'debug');
        trace('Request URI: ' . $request->url(), 'debug');
        trace('Request Method: ' . $request->method(), 'debug');
        trace('Request Data: ' . json_encode($request->param(), JSON_UNESCAPED_UNICODE), 'debug');

        // 执行请求
        $response = $next($request);

        // 记录请求结束
        trace('Response Data: ' . $response->getContent(), 'debug');
        trace('=== Request Ended ===', 'debug');

        return $response;
    }
} 