<?php
declare (strict_types = 1);

namespace app;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use Throwable;

class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * 渲染异常输出
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        $data = [
            'code' => $e instanceof HttpException ? $e->getStatusCode() : 500,
            'message' => $e->getMessage(),
        ];

        // 开发环境下输出详细信息
        if (env('APP_DEBUG')) {
            $data['file'] = $e->getFile();
            $data['line'] = $e->getLine();
            $data['trace'] = explode("\n", $e->getTraceAsString());
            
            // 添加SQL调试信息（如果是数据库异常）
            if ($e instanceof \think\db\exception\DbException) {
                $data['sql'] = $e->getData();
            }
        }

        // 记录错误日志
        trace('Exception: ' . json_encode($data), 'error');

        return json($data);
    }
} 