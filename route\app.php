<?php

use think\facade\Route;

// 页面路由
Route::get('/', function() {
    return redirect('/login.html');
});

// 允许跨域请求
Route::allowCrossDomain();

// 登录相关路由（不需要验证）
Route::group('auth', function () {
    Route::post('login', 'Auth/login');
    Route::post('logout', 'Auth/logout');
    Route::get('get_user_info', 'Auth/getUserInfo');
    Route::post('change_password', 'Auth/changePassword');
})->allowCrossDomain();

// 需要验证的路由组
Route::group(function () {
    // 用户管理路由
    Route::group('user', function () {
        Route::get('index', 'User/index')->name('user.list');
        Route::get(':id', 'User/detail')->name('user.detail')->pattern(['id' => '\d+']);
        Route::post('', 'User/create');
        Route::put(':id', 'User/update');
        Route::delete(':id', 'User/delete');
        Route::put('change_status/:id', 'User/changeStatus');
        Route::post('assign_roles/:id', 'User/assignRoles');
        Route::post('reset_password/:id', 'User/resetPassword');
        Route::get('department-user-tree', 'User/departmentUserTree');
    });

    // 角色管理路由
    Route::group('role', function () {
        Route::get('', 'Role/index');
        Route::post('', 'Role/create');
        Route::put(':id', 'Role/update');
        Route::delete(':id', 'Role/delete');
        Route::get('permissions/:id', 'Role/getPermissions');
        Route::post('assign_permissions/:id', 'Role/assignPermissions');
    });

    // 权限管理路由
    Route::group('permission', function () {
        Route::get('', 'Permission/index');
        Route::get('tree', 'Permission/tree');
        Route::post('', 'Permission/create');
        Route::put(':id', 'Permission/update');
        Route::delete(':id', 'Permission/delete');
        Route::post('init', 'Permission/init');
    });

    // 部门管理路由
    Route::group('department', function () {
        Route::get('', 'Department/index');
        Route::get('tree', 'Department/tree');
        Route::post('', 'Department/create');
        Route::put(':id', 'Department/update');
        Route::delete(':id', 'Department/delete');
        Route::get('getDepartmentsWithUsers', 'Department/getDepartmentsWithUsers');
        Route::get('getDepartmentUsers', 'Department/getDepartmentUsers');
    });

    // 电站管理路由
    Route::group('station', function () {
        Route::get('', 'Station/index');
        Route::post('', 'Station/create');
        Route::put(':id', 'Station/update');
        Route::delete(':id', 'Station/delete');
        Route::put('change_status/:id', 'Station/changeStatus');
        // 添加电站导入路由
        Route::post('import', 'StationImportController/import');
    });

    // 日志管理路由
    Route::group('log', function () {
        Route::get('login', 'Log/loginLogs');
        Route::get('operation', 'Log/operationLogs');
    });

    // 问题电站管理 (修改后)
    Route::group('station/problem', function () {
        // 基础 CRUD 和查询
        Route::get('list', 'StationProblem/list');
        Route::post('register', 'StationProblem/register');
        Route::post('update', 'StationProblem/update'); // 更新问题记录本身
        Route::post('delete', 'StationProblem/delete'); // 软删除问题记录

        // 辅助数据获取
        Route::get('getDepartmentUsers', 'StationProblem/getDepartmentUsers');
        Route::get('getAllDepartmentUsers', 'StationProblem/getAllDepartmentUsers');
        Route::get('getStationsByGuowang', 'StationProblem/getStationsByGuowang');
        Route::get('getDepartments', 'StationProblem/getDepartments');
        Route::get('index', 'StationProblem/index'); // 获取电站下拉列表
        Route::get('getHandlers', 'StationProblem/getHandlers'); // 获取处理人

        // 运维档案管理 (新添加)
        Route::get(':problemId/archives', 'StationProblem/archiveList');
        Route::post(':problemId/archives', 'StationProblem/archiveUpload');
    })->prefix('app\\controller\\'); // 确保前缀正确
    Route::delete('deleteProblemArchive/:archiveId', 'StationProblem/archiveDelete');
    // 档案管理路由
    Route::group('asset', function () {
        Route::get(':id/archives', 'StationArchiveController/index');
        Route::post(':id/archives', 'StationArchiveController/upload');
        Route::delete('archive/:stationId/:archiveId', 'StationArchiveController/delete');
    });

    // 档案初始化路由
    Route::group('archive', function () {
        Route::get('getDepartmentsByParent', 'Archive/getDepartmentsByParent');
        Route::get('getDirectories', 'Archive/getDirectories');
        Route::post('initArchive', 'Archive/initArchive');
        Route::get('getProgress', 'Archive/getProgress');
    });

    // 工单管理分类和标签路由
    Route::group('ticket', function () {
        // 分类和标签相关路由
        Route::get('category', 'TicketCategory/index');
        Route::post('category', 'TicketCategory/create');
        Route::put('category/:id', 'TicketCategory/update');
        Route::delete('category/:id', 'TicketCategory/delete');
        
        Route::get('tag', 'TicketTag/index');
        Route::post('tag/update/:id', 'TicketTag/update');
        Route::post('tag', 'TicketTag/create');
        Route::delete('tag/:id', 'TicketTag/delete'); 
        
    });
    // 财务管理路由组
    Route::group('finance', function() {
        // 资金归集
        Route::get('fund/list', 'finance.FundCollection/getList');
        Route::post('fund/save', 'finance.FundCollection/save');
        Route::post('fund/register', 'finance.FundCollection/register');
        Route::put('fund/update/:id', 'finance.FundCollection/update');
        Route::delete('fund/delete/:id', 'finance.FundCollection/delete');
        Route::post('fund/import', 'finance.FundCollection/import');
        Route::get('fund/export', 'finance.FundCollection/export');
        Route::get('fund/template', 'finance.FundCollection/template');
        Route::post('fund/import-custom', 'finance.FundCollection/importCustomFormat');
        
        // 租金发放
        Route::get('rent/list', 'finance.Rent/getList');
        Route::post('rent/save', 'finance.Rent/save');
        Route::post('rent/register', 'finance.Rent/register');
        Route::put('rent/update/:id', 'finance.Rent/update');
        Route::delete('rent/delete/:id', 'finance.Rent/delete');
        Route::post('rent/import', 'finance.Rent/import');
        Route::get('rent/export', 'finance.Rent/export');
        Route::get('rent/template', 'finance.Rent/template');

        // 国网打款
        Route::get('grid/list', 'finance.Grid/getList');
        Route::post('grid/save', 'finance.Grid/save');
        Route::post('grid/register', 'finance.Grid/register');
        Route::put('grid/update/:id', 'finance.Grid/update');
        Route::delete('grid/delete/:id', 'finance.Grid/delete');
        Route::post('grid/import', 'finance.Grid/import');
        Route::get('grid/export', 'finance.Grid/export');
        Route::get('grid/template', 'finance.Grid/template');
        
        // 村集体打款
        Route::get('village/list', 'finance.Village/getList');
        Route::post('village/save', 'finance.Village/save');
        Route::post('village/register', 'finance.Village/register');
        Route::put('village/update/:id', 'finance.Village/update');
        Route::delete('village/delete/:id', 'finance.Village/delete');
        Route::post('village/import', 'finance.Village/import');
        Route::get('village/export', 'finance.Village/export');
        Route::get('village/template', 'finance.Village/template');
        
        // 资金对账
        Route::get('reconciliation/list', 'finance.Reconciliation/getList');
        Route::get('reconciliation/detail/:id', 'finance.Reconciliation/getDetail');
        Route::post('reconciliation/adjust', 'finance.Reconciliation/adjustReconciliation');
        Route::get('reconciliation/recalculate', 'finance.Reconciliation/recalculateAll');
        Route::get('reconciliation/export', 'finance.Reconciliation/export');
    });
    
    Route::group('ticket_new', function () {
        Route::get('todo', 'TicketNew/todo');
        Route::get('done', 'TicketNew/done');
        Route::get('list', 'TicketNew/list');
        Route::get('handling', 'TicketNew/handling');
        Route::get('detail/:id', 'TicketNew/detail');
        Route::post('handle/:id', 'TicketNew/handle');
        Route::post('accept', 'TicketNew/accept');
        Route::post('complete', 'TicketNew/complete');
        Route::post('transfer/:id', 'TicketNew/transfer');
        Route::get('transfer_users', 'TicketNew/transferUsers');
        Route::get('transfer_departments', 'TicketNew/transferDepartments');
        Route::get('transfer/users', 'TicketNew/transferUsers');
        Route::get('transfer/departments', 'TicketNew/transferDepartments'); 
        Route::post('publish', 'TicketNew/publish');
        Route::post('upload', 'TicketNew/upload');
        Route::get('download/:id', 'TicketNew/download');
        Route::delete('delete_attachment/:id', 'TicketNew/deleteAttachment');
        Route::delete('delete/:id', 'TicketNew/delete');
        Route::post('export', 'TicketNew/export');
        Route::post('attachment/upload/:ticketId?', 'TicketNew/upload');
        Route::get('attachment/download/:id', 'TicketNew/download')
             ->pattern(['id' => '\d+']);  // 限制ID必须为数字
        Route::delete('attachment/:id', 'TicketNew/deleteAttachment');        
          

        // 以下是从Ticket控制器合并的路由
        Route::post('create', 'TicketNew/create');
        
        Route::get('statistics', 'TicketNew/statistics');
        Route::post('rate/:id', 'TicketNew/rate');
        Route::put('priority/:id', 'TicketNew/updatePriority');
        Route::get('check_timeout', 'TicketNew/checkTimeout');
        
        Route::post('update_handler_status', 'TicketNew/updateHandlerStatus');
        
        // 附件管理相关路由
        Route::get('fix_attachments', 'TicketNew/fixAttachments');
        Route::get('check_attachments', 'TicketNew/checkAttachments');
    });

    // 工商业电站路由组
    Route::group('business_station', function () {
        // 基础CRUD接口
        Route::get('', 'BusinessStationController/index');
        Route::post('', 'BusinessStationController/create');
        Route::put(':id', 'BusinessStationController/update');
        Route::delete(':id', 'BusinessStationController/delete');
        
        // 导入功能
        Route::post('import', 'BusinessStationImportController/import');
        
        // 档案管理 (保留列表和上传)
        Route::get(':id/archives', 'BusinessStationArchiveController/index');
        Route::post(':id/archives', 'BusinessStationArchiveController/upload');
    });

    // 将删除档案路由单独定义在分组外，但仍在 CheckAuth 保护内
    Route::delete('businessStation/archives/:archiveId', 'BusinessStationArchiveController/delete');

})->middleware(\app\middleware\CheckAuth::class)->allowCrossDomain();

// 404
Route::miss(function() {
    return json(['code' => 404, 'message' => '接口不存在']);
});