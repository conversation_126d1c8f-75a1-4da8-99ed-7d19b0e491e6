<?php

use think\migration\Migrator;

class CreateJobsTable extends Migrator
{
    /**
     * Change Method.
     *
     * @return void
     */
    public function change()
    {
        // 创建队列任务表
        $table = $this->table('jobs', ['comment' => '队列任务表', 'engine' => 'InnoDB', 'encoding' => 'utf8mb4', 'collation' => 'utf8mb4_unicode_ci']);
        $table
            ->addColumn('queue', 'string', ['limit' => 255, 'default' => 'default', 'comment' => '队列名称'])
            ->addColumn('payload', 'text', ['comment' => '任务载荷数据'])
            ->addColumn('attempts', 'integer', ['default' => 0, 'comment' => '尝试次数'])
            ->addColumn('reserved_at', 'integer', ['null' => true, 'comment' => '预留时间'])
            ->addColumn('available_at', 'integer', ['comment' => '可执行时间'])
            ->addColumn('created_at', 'integer', ['comment' => '创建时间'])
            ->addIndex(['queue', 'reserved_at'])
            ->create();
            
        // 创建失败任务表
        $table = $this->table('failed_jobs', ['comment' => '失败的队列任务表', 'engine' => 'InnoDB', 'encoding' => 'utf8mb4', 'collation' => 'utf8mb4_unicode_ci']);
        $table
            ->addColumn('connection', 'string', ['limit' => 255, 'comment' => '连接名称'])
            ->addColumn('queue', 'string', ['limit' => 255, 'comment' => '队列名称'])
            ->addColumn('payload', 'text', ['comment' => '任务载荷数据'])
            ->addColumn('exception', 'text', ['comment' => '异常信息'])
            ->addColumn('failed_at', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'comment' => '失败时间'])
            ->create();
    }
} 