<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Alarm;
use think\facade\Session;

class AlarmService extends BaseService
{
    /**
     * 获取告警列表
     */
    public function getList($params = [])
    {
        try {
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            $query = Alarm::with(['station', 'handleUser']);

            // 应用搜索条件
            if (!empty($params['keyword'])) {
                $query->where('content', 'like', "%{$params['keyword']}%");
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('status', intval($params['status']));
            }
            if (!empty($params['level'])) {
                $query->where('level', intval($params['level']));
            }
            if (!empty($params['type'])) {
                $query->where('type', $params['type']);
            }
            if (!empty($params['station_id'])) {
                $query->where('station_id', intval($params['station_id']));
            }
            if (!empty($params['start_time'])) {
                $query->whereTime('create_at', '>=', $params['start_time']);
            }
            if (!empty($params['end_time'])) {
                $query->whereTime('create_at', '<=', $params['end_time']);
            }

            // 获取总数
            $total = $query->count();
            
            // 获取分页数据
            $items = $query->page($page, $limit)
                ->order('create_at', 'desc')
                ->select();

            return $this->success([
                'items' => $items,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            return $this->error('获取告警列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理告警
     */
    public function handle($id, $data)
    {
        try {
            $alarm = Alarm::find($id);
            if (!$alarm) {
                return $this->error('告警不存在');
            }

            if ($alarm->status == 1) {
                return $this->error('该告警已处理');
            }

            $alarm->status = 1;
            $alarm->handle_user_id = Session::get('user_id');
            $alarm->handle_time = date('Y-m-d H:i:s');
            $alarm->handle_result = $data['handle_result'];
            
            if ($alarm->save() === false) {
                return $this->error('告警处理失败');
            }

            return $this->success($alarm, '告警处理成功');
        } catch (\Exception $e) {
            return $this->error('告警处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除告警
     */
    public function delete($id)
    {
        try {
            $alarm = Alarm::find($id);
            if (!$alarm) {
                return $this->error('告警不存在');
            }

            if ($alarm->delete() === false) {
                return $this->error('告警删除失败');
            }

            return $this->success(null, '告警删除成功');
        } catch (\Exception $e) {
            return $this->error('告警删除失败: ' . $e->getMessage());
        }
    }
} 