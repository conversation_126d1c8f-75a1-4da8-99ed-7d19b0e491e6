<?php
declare (strict_types = 1);
namespace app\controller;
use app\BaseController;
use app\model\Ticket as TicketModel;
use think\facade\Session;
use think\facade\Request;
use app\service\LogService;
use think\App;
use app\service\TicketService;
use app\model\TicketAttachment;
use think\facade\Db;
use think\facade\Filesystem;
use app\common\ExcelHelper;
class Ticket extends BaseController
{
    protected $ticketModel;
    protected $ticketService;
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->ticketModel = new TicketModel();
        $this->ticketService = new TicketService();
    }
    /**
     * 获取待办工单列表
     */
    public function todo()
    {
        try {
            trace('开始获取待办工单列表', 'debug');
            
            // 验证登录状态
            if (!session('user_id')) {
                trace('用户未登录', 'warning');
                return json([
                    'code' => 401,
                    'message' => '未登录或登录已过期'
                ]);
            }
            
            // 获取请求参数
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $keyword = input('keyword/s', '');
            $priority = input('priority/s', '');
            $categoryId = input('category_id/d', 0);
            $tagId = input('tag_id/d', 0); // 新增标签筛选
            $departmentId = input('department_id/d', 0); // 新增部门筛选
            $startDate = input('start_date/s', '');
            $endDate = input('end_date/s', '');
            $sortField = input('sort_field/s', 'create_at'); // 新增排序字段
            $sortOrder = input('sort_order/s', 'desc'); // 新增排序方向

            // 记录查询参数
            trace('工单列表查询参数: ' . json_encode([
                'page' => $page,
                'limit' => $limit,
                'keyword' => $keyword,
                'priority' => $priority,
                'category_id' => $categoryId,
                'tag_id' => $tagId,
                'department_id' => $departmentId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'sort_field' => $sortField,
                'sort_order' => $sortOrder
            ]), 'debug');
            
            // 获取当前用户信息和角色
            $userId = session('user_id');
            trace('当前查询用户ID: ' . $userId, 'debug');
            
            $user = \app\model\User::find($userId);
            
            // 检查是否为超级管理员
            $isAdmin = false;
            if ($user) {
                // 获取用户角色并检查是否为管理员
                $roles = $user->roles();
                foreach ($roles as $role) {
                    if ($role->is_admin == 1) {
                        $isAdmin = true;
                        break;
                    }
                }
            }
            
            trace('用户ID: ' . $userId . ', 是否管理员: ' . ($isAdmin ? '是' : '否'), 'debug');
            
            // 构建基础查询条件
            $where = [
                ['status', '=', 0]  // 待处理的工单
            ];
            
            // 如果不是管理员，只查询自己创建的或自己需要处理的工单
            if (!$isAdmin) {
                // 添加调试日志，帮助排查问题
                trace('正在查询用户ID为' . $userId . '创建的或负责处理的工单', 'debug');
                
                // 查询用户参与处理的所有工单ID
                $userTicketIds = Db::name('ticket_handler')
                    ->where('handler_id', $userId)
                    ->column('ticket_id');
                    
                trace('查询到用户参与处理的工单数量: ' . count($userTicketIds), 'debug');
                
                // 构建查询条件：自己创建的或者自己需要处理的
                $query = Db::name('ticket')->where('status', 0);
                $query->where(function($query) use ($userId, $userTicketIds) {
                    // 自己创建的工单
                    $query->where('creator_id', $userId);
                    // 自己需要处理的工单
                    if (!empty($userTicketIds)) {
                        $query->whereOr('id', 'in', $userTicketIds);
                    }
                });
                
                // 获取符合条件的工单ID
                $ticketIds = $query->column('id');
                
                if (!empty($ticketIds)) {
                    $where[] = ['id', 'in', $ticketIds];
                } else {
                    // 如果没有工单，设置一个不可能满足的条件
                    $where[] = ['id', '=', 0];
                }
            }
            
            // 关键词搜索（支持工单号、标题、内容）
            if ($keyword) {
                $where[] = function ($query) use ($keyword) {
                    $query->where('title', 'like', "%{$keyword}%")
                          ->whereOr('ticket_no', 'like', "%{$keyword}%")
                          ->whereOr('content', 'like', "%{$keyword}%");
                };
            }
            
            // 优先级筛选
            if ($priority) {
                $where[] = ['priority', '=', $priority];
            }
            
            // 分类筛选
            if ($categoryId) {
                $where[] = ['category_id', '=', $categoryId];
            }
            
            // 部门筛选
            if ($departmentId) {
                $where[] = ['department_id', '=', $departmentId];
            }
            
            // 日期范围筛选
            if ($startDate && $endDate) {
                $where[] = ['create_at', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
            } else if ($startDate) {
                $where[] = ['create_at', '>=', $startDate . ' 00:00:00'];
            } else if ($endDate) {
                $where[] = ['create_at', '<=', $endDate . ' 23:59:59'];
            }
            
            // 构建查询对象
            $query = Db::name('ticket');
            
            // 应用查询条件
            foreach ($where as $condition) {
                if (is_array($condition) && count($condition) === 3) {
                    $query->where($condition[0], $condition[1], $condition[2]);
                } else if (is_callable($condition)) {
                    $query->where($condition);
                }
            }
            
            // 标签筛选（需要单独处理，因为是多对多关系）
            if ($tagId) {
                $query->whereExists(function ($exist) use ($tagId) {
                    $exist->table('sp_ticket_tag_relation')
                        ->where('ticket_id', '=', Db::raw('sp_ticket.id'))
                        ->where('tag_id', '=', $tagId);
                });
            }
            
            // 获取总记录数
            $total = $query->count();
            trace('总记录数: ' . $total, 'debug');
            
            // 验证排序字段（防止SQL注入）
            $allowedSortFields = ['create_at', 'priority', 'expect_time', 'handlers_count', 'transfer_count'];
            if (!in_array($sortField, $allowedSortFields)) {
                $sortField = 'create_at';
            }
            
            // 验证排序方向
            $sortOrder = strtolower($sortOrder) === 'asc' ? 'asc' : 'desc';
            
            // 查询数据
            $list = $query->page($page, $limit)
                ->order($sortField, $sortOrder)
                ->select()
                ->each(function($item) {
                    // 获取标签数据
                    $item['tags'] = Db::name('ticket_tag_relation')
                        ->alias('r')
                        ->join('ticket_tag t', 'r.tag_id = t.id')
                        ->where('r.ticket_id', $item['id'])
                        ->field('t.id, t.name, t.color')
                        ->select();
                    
                    // 获取处理人数据
                    $item['handlers'] = Db::name('ticket_handler')
                        ->where('ticket_id', $item['id'])
                        ->field('handler_id, handler_name, status')
                        ->select();
                    
                    // 处理日期格式
                    if (!empty($item['create_at'])) {
                        $item['create_at_formatted'] = date('Y-m-d H:i', strtotime($item['create_at']));
                    }
                    
                    // 优先级格式化
                    switch ($item['priority']) {
                        case 'low':
                            $item['priority_text'] = '低';
                            $item['priority_color'] = 'success';
                            break;
                        case 'medium':
                            $item['priority_text'] = '中';
                            $item['priority_color'] = 'warning';
                            break;
                        case 'high':
                            $item['priority_text'] = '高';
                            $item['priority_color'] = 'danger';
                            break;
                        default:
                            $item['priority_text'] = '未知';
                            $item['priority_color'] = 'info';
                    }
                    
                    return $item;
                });
            
            trace('查询完成，返回' . count($list) . '条记录', 'debug');
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'items' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            trace('获取工单列表失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '获取工单列表失败: ' . $e->getMessage()
            ]);
        }
    }
    /**
     * 获取已办工单列表
     */
    public function done()
    {
        try {
            $query = request()->get();
            $page = isset($query['page']) ? intval($query['page']) : 1;
            $limit = isset($query['limit']) ? intval($query['limit']) : 10;
            $keyword = isset($query['keyword']) ? trim($query['keyword']) : '';
            $priority = isset($query['priority']) ? trim($query['priority']) : '';
            $category_id = isset($query['category_id']) ? intval($query['category_id']) : '';
            $date = isset($query['date']) ? $query['date'] : [];
            // 获取当前用户信息和角色
            $userId = session('user_id');
            // 修改查询方式，避免使用with导致的eagerlyResult错误
            $user = \app\model\User::find($userId);
            // 检查是否为超级管理员
            $isAdmin = false;
            if ($user) {
                // 获取用户角色并检查是否为管理员
                $roles = $user->roles();
                foreach ($roles as $role) {
                    if ($role->is_admin == 1) {
                        $isAdmin = true;
                        break;
                    }
                }
            }
            $where = [
                ['status', '=', 1],  // 已完成的工单
            ];
            // 如果不是管理员，只查询处理人包含当前用户的工单
            if (!$isAdmin) {
                // 使用JSON_CONTAINS函数查询handler_ids字段中包含当前用户ID的记录
                // 或者当前用户是处理人(handler_id字段)
                $userFilter = function ($query) use ($userId) {
                    $query->where('handler_id', '=', $userId)
                          ->whereOr(function ($q) use ($userId) {
                              $q->where('handler_ids', 'like', "%\"$userId\"%")
                                ->whereOr('handler_ids', 'like', "%,$userId,%")
                                ->whereOr('handler_ids', 'like', "[$userId,%")
                                ->whereOr('handler_ids', 'like', "%,$userId]");
                          });
                };
                $where[] = $userFilter;
            }
            // 添加调试日志
            trace('已办工单查询条件: ' . json_encode([
                'user_id' => $userId,
                'is_admin' => $isAdmin,
                'keyword' => $keyword,
                'priority' => $priority,
                'category_id' => $category_id,
                'date' => $date
            ]), 'debug');
            // 关键词搜索
            if ($keyword) {
                $where[] = ['title|ticket_no', 'like', "%{$keyword}%"];
            }
            // 优先级筛选
            if ($priority) {
                $where[] = ['priority', '=', $priority];
            }
            // 分类筛选
            if ($category_id) {
                $where[] = ['category_id', '=', $category_id];
            }
            // 日期范围筛选
            if (!empty($date)) {
                $where[] = ['create_at', 'between', [$date[0] . ' 00:00:00', $date[1] . ' 23:59:59']];
            }
            // 打印最终的查询条件
            trace('最终查询条件: ' . json_encode($where), 'debug');
            // 查询数据
            $query = Db::name('ticket');
            foreach ($where as $condition) {
                if (is_array($condition) && count($condition) === 3) {
                    $query->where($condition[0], $condition[1], $condition[2]);
                } else if (is_callable($condition)) {
                    $query->where($condition);
                }
            }
            // 获取总数
            $total = $query->count();
            // 查询列表数据
            $list = $query->order('last_completed_at desc, id desc')
                ->page($page, $limit)
                ->select()
                ->each(function($item) {
                    // 获取标签数据
                    $item['tags'] = Db::name('ticket_tag_relation')
                        ->alias('r')
                        ->join('ticket_tag t', 'r.tag_id = t.id')
                        ->where('r.ticket_id', $item['id'])
                        ->field('t.id, t.name, t.color')
                        ->select();
                    return $item;
                });
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'items' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            trace('获取已办工单列表失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取已办工单列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    /**
     * 创建工单
     */
    public function create()
    {
        try {
            $data = $this->request->post();
            // 数据验证
            $validate = validate([
                'title' => 'require',
                'content' => 'require',
                'priority' => 'require|in:low,medium,high',
                'category_id' => 'require|number',
                'attachments' => 'array'
            ]);
            if (!$validate->check($data)) {
                return $this->error($validate->getError());
            }
            // 添加创建人信息
            $data['creator_id'] = $this->user->id;
            $data['create_at'] = date('Y-m-d H:i:s');
            $data['status'] = 0;  // 0: 待处理
            // 创建工单
            $ticket = $this->ticketModel->create($data);
            // 处理标签
            if (!empty($data['tags'])) {
                $ticket->tags()->saveAll($data['tags']);
            }
            return $this->success([
                'id' => $ticket->id,
                'message' => '工单创建成功'
            ]);
        } catch (\Exception $e) {
            return $this->error('创建工单失败：' . $e->getMessage());
        }
    }
    /**
     * 处理工单
     */
    public function handle($id)
    {
        $result = Request::post('feedback');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            if ($ticket->status == 1) {
                return $this->error('工单已处理');
            }
            $ticket->status = 1; // 已处理状态
            $ticket->handler_id = Session::get('user_id');
            $ticket->feedback = $feedback;
            $ticket->completed_at = date('Y-m-d H:i:s');
            $ticket->save();
            // 记录操作日志
            LogService::recordOperation('ticket', 'handle', [
                'id' => $id,
                'feedback' => $feedback
            ]);
            return $this->success(null, '工处理成功');
        } catch (\Exception $e) {
            return $this->error('工单处理失败：' . $e->getMessage());
        }
    }
    /**
     * 生成工单编号
     */
    private function generateTicketNo()
    {
        $date = date('Ymd');
        $key = "ticket_no_{$date}";
        // 获取当日工单序号
        $sequence = cache($key) ?: 0;
        $sequence++;
        // 更新序号缓存，有效期到当天结束
        $expire = strtotime(date('Y-m-d 23:59:59')) - time();
        cache($key, $sequence, $expire);
        // 生成工单编号：WO + 日期 + 4位序号
        return 'WO' . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);
    }
    /**
     * 获取工单详情
     */
    public function detail($id)
    {
        try {
            trace('开始获取工单详情，ID='.$id, 'debug');
            
            // 验证登录状态
            if (!session('user_id')) {
                trace('用户未登录', 'warning');
                return json([
                    'code' => 401,
                    'message' => '未登录或登录已过期'
                ]);
            }
            
            // 获取当前用户ID和角色
            $userId = session('user_id');
            $user = \app\model\User::find($userId);
            
            // 检查是否为超级管理员
            $isAdmin = false;
            if ($user) {
                $roles = $user->roles();
                foreach ($roles as $role) {
                    if ($role->is_admin == 1) {
                        $isAdmin = true;
                        break;
                    }
                }
            }
            
            // 获取工单基本信息
            $ticket = Db::name('ticket')
                ->where('id', $id)
                ->find();
                
            if (!$ticket) {
                trace('工单不存在，ID='.$id, 'error');
                return json([
                    'code' => 404,
                    'message' => '工单不存在',
                    'data' => null
                ]);
            }
            
            // 检查访问权限
            if (!$isAdmin) {
                // 非管理员，检查是否为创建者或处理人
                $isCreator = ($ticket['creator_id'] == $userId);
                $isHandler = Db::name('ticket_handler')
                    ->where('ticket_id', $id)
                    ->where('handler_id', $userId)
                    ->count() > 0;
                    
                if (!$isCreator && !$isHandler) {
                    trace('用户无权查看该工单', 'warning');
                    return json([
                        'code' => 403,
                        'message' => '您无权查看此工单',
                        'data' => null
                    ]);
                }
            }
            
            trace('获取工单基本信息成功', 'debug');
            
            // 获取工单标签
            $tags = Db::name('ticket_tag_relation')
                ->alias('r')
                ->join('ticket_tag t', 'r.tag_id = t.id')
                ->where('r.ticket_id', $id)
                ->field('t.id, t.name, t.color')
                ->select()
                ->toArray();
                
            $ticket['tags'] = $tags;
            trace('获取工单标签成功，共'.count($tags).'个标签', 'debug');
            
            // 获取附件信息
            $attachments = Db::name('ticket_attachment')
                ->where('ticket_id', $id)
                ->select()
                ->toArray();
                
            $ticket['attachments'] = $attachments;
            trace('获取工单附件成功，共'.count($attachments).'个附件', 'debug');
            
            // 获取工单分类信息
            if (!empty($ticket['category_id'])) {
                $category = Db::name('ticket_category')
                    ->where('id', $ticket['category_id'])
                    ->field('id, name, code')
                    ->find();
                $ticket['category'] = $category;
            } else {
                $ticket['category'] = null;
            }
            
            // 获取创建者信息
            if (!empty($ticket['creator_id'])) {
                $creator = Db::name('user')
                    ->alias('u')
                    ->join('department d', 'u.department_id = d.id', 'LEFT')
                    ->where('u.id', $ticket['creator_id'])
                    ->field('u.id, u.username, u.realname, d.name as department_name')
                    ->find();
                $ticket['creator'] = $creator;
            } else {
                $ticket['creator'] = null;
            }
            
            // 获取部门信息
            if (!empty($ticket['department_id'])) {
                $department = Db::name('department')
                    ->where('id', $ticket['department_id'])
                    ->field('id, name, parent_id')
                    ->find();
                $ticket['department'] = $department;
            } else {
                $ticket['department'] = null;
            }
            
            // 获取处理人信息
            $handlers = Db::name('ticket_handler')
                ->alias('th')
                ->join('user u', 'th.handler_id = u.id', 'LEFT')
                ->join('department d', 'u.department_id = d.id', 'LEFT')
                ->where('th.ticket_id', $id)
                ->field([
                    'th.*',
                    'u.username', 'u.realname',
                    'd.name as department_name'
                ])
                ->select()
                ->toArray();
                
            // 为每个处理人增加状态文本和颜色
            foreach ($handlers as &$handler) {
                // 优化处理人姓名显示
                $handler['handler_name'] = $handler['realname'] ?: $handler['username'];
                
                switch ($handler['status']) {
                    case 0:
                        $handler['status_text'] = '未处理';
                        $handler['status_color'] = 'info';
                        break;
                    case 1:
                        $handler['status_text'] = '已接受';
                        $handler['status_color'] = 'primary';
                        break;
                    case 2:
                        $handler['status_text'] = '已拒绝';
                        $handler['status_color'] = 'danger';
                        break;
                    case 3:
                        $handler['status_text'] = '处理中';
                        $handler['status_color'] = 'warning';
                        break;
                    case 4:
                        $handler['status_text'] = '已完成';
                        $handler['status_color'] = 'success';
                        break;
                    default:
                        $handler['status_text'] = '未知';
                        $handler['status_color'] = 'default';
                }
                
                // 处理人历史处理时间记录
                $handlerTimeline = [];
                if (!empty($handler['create_at'])) {
                    $handlerInfo = $handler['handler_name'] . (!empty($handler['department_name']) ? "（{$handler['department_name']}）" : "");
                    $handlerTimeline[] = [
                        'time' => $handler['create_at'],
                        'title' => '指派处理人',
                        'content' => "工单被指派给 {$handlerInfo} 处理"
                    ];
                }
                if (!empty($handler['accepted_at'])) {
                    $handlerInfo = $handler['handler_name'] . (!empty($handler['department_name']) ? "（{$handler['department_name']}）" : "");
                    $handlerTimeline[] = [
                        'time' => $handler['accepted_at'],
                        'title' => '接受工单',
                        'content' => "{$handlerInfo} 接受了工单处理"
                    ];
                }
                if (!empty($handler['rejected_at'])) {
                    $handlerInfo = $handler['handler_name'] . (!empty($handler['department_name']) ? "（{$handler['department_name']}）" : "");
                    $handlerTimeline[] = [
                        'time' => $handler['rejected_at'],
                        'title' => '拒绝工单',
                        'content' => "{$handlerInfo} 拒绝处理工单" . 
                                    (!empty($handler['comment']) ? "，原因：{$handler['comment']}" : "")
                    ];
                }
                if (!empty($handler['handling_at'])) {
                    $handlerInfo = $handler['handler_name'] . (!empty($handler['department_name']) ? "（{$handler['department_name']}）" : "");
                    $handlerTimeline[] = [
                        'time' => $handler['handling_at'],
                        'title' => '开始处理',
                        'content' => "{$handlerInfo} 开始处理工单"
                    ];
                }
                if (!empty($handler['updated_at']) && $handler['status'] == 4) {
                    $handlerInfo = $handler['handler_name'] . (!empty($handler['department_name']) ? "（{$handler['department_name']}）" : "");
                    $handlerTimeline[] = [
                        'time' => $handler['updated_at'],
                        'title' => '完成处理',
                        'content' => "{$handlerInfo} 完成工单处理" .
                                    (!empty($handler['comment']) ? "，备注：{$handler['comment']}" : "") .
                                    (!empty($handler['handling_time']) ? "，处理用时：{$handler['handling_time']}分钟" : "")
                    ];
                }
                $handler['timeline'] = $handlerTimeline;
            }
            
            $ticket['handlers'] = $handlers;
            trace('获取工单处理人信息成功，共'.count($handlers).'个处理人', 'debug');
            
            // 格式化工单状态
            switch ($ticket['status']) {
                case 0:
                    $ticket['status_text'] = '待处理';
                    $ticket['status_color'] = 'primary';
                    break;
                case 1:
                    $ticket['status_text'] = '已完成';
                    $ticket['status_color'] = 'success';
                    break;
                default:
                    $ticket['status_text'] = '未知';
                    $ticket['status_color'] = 'default';
            }
            
            // 格式化优先级
            switch ($ticket['priority']) {
                case 'low':
                    $ticket['priority_text'] = '低';
                    $ticket['priority_color'] = 'success';
                    break;
                case 'medium':
                    $ticket['priority_text'] = '中';
                    $ticket['priority_color'] = 'warning';
                    break;
                case 'high':
                    $ticket['priority_text'] = '高';
                    $ticket['priority_color'] = 'danger';
                    break;
                default:
                    $ticket['priority_text'] = '未知';
                    $ticket['priority_color'] = 'info';
            }
            
            // 获取工单转派记录
            $transfers = Db::name('ticket_transfer')
                ->alias('t')
                ->join('user u1', 't.from_user_id = u1.id', 'LEFT')
                ->join('user u2', 't.to_user_id = u2.id', 'LEFT')
                ->where('t.ticket_id', $id)
                ->field([
                    't.*', 
                    'u1.username as from_username', 
                    'u1.realname as from_realname',
                    'u2.username as to_username', 
                    'u2.realname as to_realname'
                ])
                ->order('t.create_at', 'desc')
                ->select()
                ->toArray();
            
            $ticket['transfers'] = $transfers;
            trace('获取工单转派记录成功，共'.count($transfers).'条记录', 'debug');
            
            // 构建工单时间线
            $timeline = [];
            
            // 创建时间
            if (!empty($ticket['create_at'])) {
                $timeline[] = [
                    'time' => $ticket['create_at'],
                    'title' => '工单创建',
                    'content' => '工单由 ' . ($ticket['creator']['realname'] ?: $ticket['creator']['username']) . ' 创建'
                ];
            }
            
            // 转派记录
            foreach ($transfers as $transfer) {
                $fromName = $transfer['from_realname'] ?: $transfer['from_username'];
                $toName = $transfer['to_realname'] ?: $transfer['to_username'];
                
                $timeline[] = [
                    'time' => $transfer['create_at'],
                    'title' => '工单转派',
                    'content' => "工单由 {$fromName} 转派给 {$toName}，原因：{$transfer['remark']}"
                ];
            }
            
            // 处理记录
            foreach ($handlers as $handler) {
                foreach ($handler['timeline'] as $item) {
                    $timeline[] = $item;
                }
            }
            
            // 完成时间
            if (!empty($ticket['finish_time'])) {
                $timeline[] = [
                    'time' => $ticket['finish_time'],
                    'title' => '工单完成',
                    'content' => '工单处理完成'
                ];
            }
            
            // 按时间排序
            usort($timeline, function($a, $b) {
                return strtotime($a['time']) - strtotime($b['time']);
            });
            
            $ticket['timeline'] = $timeline;
            trace('构建工单时间线成功，共'.count($timeline).'条记录', 'debug');
            
            // 计算当前用户对此工单的权限
            $permissions = [
                'can_edit' => $isAdmin || $ticket['creator_id'] == $userId,
                'can_delete' => $isAdmin && $ticket['status'] == 0,
                'can_transfer' => $isAdmin && $ticket['status'] == 0,
                'can_handle' => false, // 默认不能处理
                'handler_status' => 0 // 当前用户处理状态
            ];
            
            // 查找当前用户是否为处理人
            $currentHandler = null;
            foreach ($handlers as $handler) {
                if ($handler['handler_id'] == $userId) {
                    $currentHandler = $handler;
                    $permissions['can_handle'] = true;
                    $permissions['handler_status'] = $handler['status'];
                    break;
                }
            }
            
            $ticket['permissions'] = $permissions;
            trace('计算用户权限完成', 'debug');
            
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $ticket
            ]);
        } catch (\Exception $e) {
            trace('获取工单详情失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '获取工单详情失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    /**
     * 删除工单
     */
    public function delete($id)
    {
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            // 只能删除未处理的工单
            if ($ticket->status == 1) {
                return $this->error('已处理的工单不能删除');
            }
            $ticket->delete();
            // 记录操作日志
            LogService::recordOperation('ticket', 'delete', ['id' => $id]);
            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    /**
     * 获取工单统计数据
     */
    public function statistics()
    {
        try {
            $today = date('Y-m-d');
            $data = [
                'total' => $this->ticketModel->count(),
                'pending' => $this->ticketModel->where('status', 0)->count(),
                'completed' => $this->ticketModel->where('status', 1)->count(),
                'today' => $this->ticketModel->whereDay('create_at', $today)->count()
            ];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }
    /**
     * 导出工单列表
     */
    public function export()
    {
        try {
            $params = Request::get();
            $list = $this->ticketModel->with(['station', 'creator', 'handler'])
                ->where('status', '>=', 0)
                ->select()
                ->toArray();
            $data = [];
            foreach ($list as $item) {
                $data[] = [
                    '工单编号' => $item['ticket_no'],
                    '工单标题' => $item['title'],
                    '所属电站' => $item['station']['name'],
                    '优先级' => $item['priority_text'],
                    '状态' => $item['status_text'],
                    '创建人' => $item['creator']['realname'],
                    '处理人' => $item['handler']['realname'] ?? '',
                    '创建时间' => $item['create_at'],
                    '完成时间' => $item['completed_at']
                ];
            }
            return download_excel($data, '工单列表');
        } catch (\Exception $e) {
            trace('导出工单失败：' . $e->getMessage(), 'error');
            return $this->error('导出失败');
        }
    }
    /**
     * 评价工单
     */
    public function rate($id)
    {
        $score = input('score/d');
        $comment = input('comment/s', '');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            if ($ticket->status != 1) {
                return $this->error('工单未完成,不能评价');
            }
            $ticket->score = $score;
            $ticket->comment = $comment;
            $ticket->save();
            LogService::recordOperation('ticket', 'rate', [
                'id' => $id,
                'score' => $score,
                'comment' => $comment
            ]);
            return $this->success(null, '评价成功');
        } catch (\Exception $e) {
            return $this->error('评价失败：' . $e->getMessage());
        }
    }
    /**
     * 转派工单
     */
    public function transfer($id)
    {
        try {
            $data = $this->request->post();
            $result = $this->ticketService->transfer($id, $data);
            return json($result);
        } catch (ValidateException $e) {
            return json(['code' => 400, 'msg' => $e->getError()]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }
    /**
     * 修改优先级
     */
    public function updatePriority($id)
    {
        $priority = input('priority/s');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            $ticket->priority = $priority;
            $ticket->save();
            LogService::recordOperation('ticket', 'update_priority', [
                'id' => $id,
                'priority' => $priority
            ]);
            return $this->success(null, '优先级修改成功');
        } catch (\Exception $e) {
            return $this->error('修改失败：' . $e->getMessage());
        }
    }
    /**
     * 检查超时工单
     */
    public function checkTimeout()
    {
        try {
            $timeout = 24; // 超时时间(小时)
            $tickets = $this->ticketModel
                ->where('status', 0)
                ->whereTime('create_at', '<=', date('Y-m-d H:i:s', strtotime("-{$timeout} hour")))
                ->select();
            foreach ($tickets as $ticket) {
                // 发送超时提醒通知
                $this->sendTimeoutNotification($ticket);
            }
            return $this->success(null, '检查完成');
        } catch (\Exception $e) {
            return $this->error('检查失败：' . $e->getMessage());
        }
    }
    /**
     * 发送超时提醒
     */
    private function sendTimeoutNotification($ticket)
    {
        // 实现发送提醒逻辑(邮件/短信/站内信等)
    }
    /**
     * 获取工单列表
     */
    public function index()
    {
        try {
            // 添加调试日志
            trace('开始获取工单列表', 'debug');
            $params = Request::get();
            trace('请求参数: ' . json_encode($params), 'debug');
            // 检查登录状态
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            $result = $this->ticketService->getList($params);
            trace('工单列表结果: ' . json_encode($result), 'debug');
            return json($result);
        } catch (\Exception $e) {
            trace('获取工单列表失败: ' . $e->getMessage(), 'error');
            return $this->error('获取工单列表失败: ' . $e->getMessage());
        }
    }
    /**
     * 发布工单
     */
    public function publish()
    {
        try {
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            $data = Request::post();
            // 添加创建者信息
            $data['creator_id'] = $this->user->id;
            
            // 验证必要字段
            if (empty($data['department_id'])) {
                return $this->error('请选择处理部门');
            }
            
            // 验证并确保预期完成时间字段存在
            if (empty($data['expect_time'])) {
                return $this->error('请设置预期完成时间');
            }
            
            // 处理顶级部门的特殊情况
            $isTopDepartment = isset($data['is_top_department']) && $data['is_top_department'] == 1;
            trace('是否顶级部门: ' . ($isTopDepartment ? '是' : '否'), 'debug');
            
            // 如果是顶级部门但没有选择处理人，自动获取该部门及其子部门下的所有用户
            if ($isTopDepartment && (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0)) {
                trace('顶级部门自动获取处理人', 'debug');
                // 获取部门ID
                $departmentId = intval($data['department_id']);
                // 查询该部门是否存在并确认是顶级部门
                $department = \think\facade\Db::name('department')->where('id', $departmentId)->find();
                if (!$department || $department['parent_id'] != 0) {
                    return $this->error('部门数据有误，请重新选择');
                }
                // 获取该部门下的所有子部门ID
                $childDepartments = \think\facade\Db::name('department')
                    ->where('parent_id', $departmentId)
                    ->column('id');
                // 将当前部门ID也加入到查询范围
                $allDepartmentIds = array_merge([$departmentId], $childDepartments);
                trace('查询部门IDs: ' . json_encode($allDepartmentIds), 'debug');
                // 查询这些部门下的所有有效用户
                $users = \think\facade\Db::name('user')
                    ->whereIn('department_id', $allDepartmentIds)
                    ->where('status', 1)
                    ->column('id');
                if (!empty($users)) {
                    trace('自动获取到的处理人: ' . json_encode($users), 'debug');
                    $data['handler_ids'] = $users;
                } else {
                    return $this->error('所选部门下没有可用的处理人员');
                }
            }
            // 常规验证处理人
            if (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0) {
                return $this->error('请选择处理人员');
            }
            // 记录请求数据
            trace('工单发布请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            $result = $this->ticketService->publish($data);
            if ($result['code'] === 200) {
                LogService::recordOperation('ticket', 'publish', array_merge(
                    ['id' => $result['data']['id']],
                    array_diff_key($data, ['content' => '', 'attachments' => []])
                ));
            }
            return json($result);
        } catch (\Exception $e) {
            trace('Publish ticket error: ' . $e->getMessage(), 'error');
            return $this->error('发布工单失败：' . $e->getMessage());
        }
    }
    /**
     * 处理文件上传
     */
    public function upload()
    {
        try {
            $file = request()->file('file');
            if (!$file) {
                return json(['code' => 400, 'message' => '请选择上传文件']);
            }

            // 验证文件类型和大小
            $validate = [
                'size' => 10 * 1024 * 1024, // 10MB
                'ext'  => 'jpg,jpeg,png,gif,pdf,doc,docx'
            ];
            $fileCheck = $file->check($validate);
            if (!$fileCheck) {
                return json(['code' => 400, 'message' => $file->getError()]);
            }

            // 保存路径
            $savePath = 'uploads/ticket/' . date('Ymd') . '/';
            $saveName = uniqid() . '.' . $file->getOriginalExtension();
            
            // 移动文件
            $file->move(public_path() . $savePath, $saveName);
            $filePath = $savePath . $saveName;

            return json([
                'code' => 200,
                'message' => '上传成功',
                'data' => [
                    'path' => $filePath,
                    'url' => request()->domain() . '/' . $filePath,
                    'original_name' => $file->getOriginalName()
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '上传失败：' . $e->getMessage()
            ]);
        }
    }
    /**
     * 删除附件
     */
    public function deleteAttachment($id)
    {
        try {
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            $attachment = TicketAttachment::find($id);
            if (!$attachment) {
                return $this->error('附件不存在');
            }
            // 删除文件
            if (file_exists($attachment->path)) {
                unlink($attachment->path);
            }
            $attachment->delete();
            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    /**
     * 获取工单标签
     */
    protected function getTicketTags($ticketId)
    {
        try {
            // 使用 facade Db
            $tags = Db::name('ticket_tag_relation')
                ->alias('tr')
                ->join('ticket_tag t', 'tr.tag_id = t.id')
                ->where('tr.ticket_id', $ticketId)
                ->where('t.status', 1)  // 只获取启用的标签
                ->column('t.name');
            if ($tags === false) {
                throw new \Exception('获取标签失败');
            }
            return $tags;
        } catch (\Exception $e) {
            trace('Get ticket tags error: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    /**
     * 获取处理人列表
     */
    public function handlers()
    {
        try {
            trace(__FILE__ . ':' . __LINE__ . ' [处理人] 开始获取处理人列表', 'debug');
            $checkLogin = $this->checkLogin();
            if ($checkLogin !== true) {
                trace(__FILE__ . ':' . __LINE__ . ' [处理人] 未登录', 'warning');
                return $checkLogin;
            }
            $result = $this->ticketService->getHandlerList();
            trace(__FILE__ . ':' . __LINE__ . ' [处理人] 获取处理人列表结果: ' . json_encode($result), 'debug');
            return json($result);
        } catch (\Exception $e) {
            trace(__FILE__ . ':' . __LINE__ . ' [处理人] 获取处理人列表失败: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取处理人列表失败',
                'data' => null
            ]);
        }
    }
    /**
     * 获取工单表单所需数据
     */
    public function form()
    {
        try {
            // 获取部门和处理人树形结构
            $handlersTree = $this->getDepartmentHandlerTree();
            // 获取分类数据
            $categories = [];
            $categoryRes = Db::name('ticket_category')
                ->field('id, parent_id, name, code, sort')
                ->order('sort asc, id asc')
                ->select();
            if ($categoryRes) {
                $categories = $this->buildCategoryTree($categoryRes->toArray());
            }
            // 获取标签数据
            $tags = Db::name('ticket_tag')
                ->field('id, name, color')
                ->select()
                ->toArray();
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'handlers' => $handlersTree,
                    'categories' => $categories,
                    'tags' => $tags
                ]
            ]);
        } catch (\Exception $e) {
            trace('获取工单表单数据失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取工单表单数据失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    /**
     * 获取部门和处理人树形结构
     */
    private function getDepartmentHandlerTree()
    {
        // 获取所有部门
        $departments = Db::name('department')
            ->field('id, name, parent_id, sort')
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select()
            ->toArray();
        // 获取所有用户
        $users = Db::name('user')
            ->field('id, username, realname, department_id, status')
            ->where('status', 1)
            ->select()
            ->toArray();
        // 构建树形结构
        $tree = $this->buildDepartmentHandlerTree($departments, $users);
        trace('构建的部门和处理人树：' . json_encode($tree, JSON_UNESCAPED_UNICODE), 'debug');
        return $tree;
    }
    /**
     * 构建部门和处理人树
     */
    private function buildDepartmentHandlerTree($departments, $users, $parentId = 0)
    {
        $result = [];
        // 找出当前级别的部门
        foreach ($departments as $dept) {
            if ($dept['parent_id'] == $parentId) {
                // 部门节点
                $node = [
                    'id' => $dept['id'],  // 移除前缀，使用原始部门ID
                    'label' => $dept['name'],
                    'type' => 'department'
                ];
                // 查找部门下的用户
                $deptUsers = [];
                foreach ($users as $user) {
                    if ($user['department_id'] == $dept['id']) {
                        $deptUsers[] = [
                            'id' => $user['id'],
                            'label' => $user['realname'] ?: $user['username'],
                            'type' => 'user'
                        ];
                    }
                }
                // 递归获取子部门
                $children = $this->buildDepartmentHandlerTree($departments, $users, $dept['id']);
                // 如果有子部门或用户，合并到子节点
                if (!empty($children) || !empty($deptUsers)) {
                    $node['children'] = array_merge($deptUsers, $children);
                }
                $result[] = $node;
            }
        }
        return $result;
    }
    /**
     * 构建分类树
     */
    private function buildCategoryTree($categories, $parentId = null)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = $this->buildCategoryTree($categories, $category['id']);
                if ($children) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        return $tree;
    }
    /**
     * 关联文件到工单
     */
    public function attach($id)
    {
        if (!$this->isLogin()) {
            return json(['code' => 401, 'message' => '未登录']);
        }
        $files = request()->post('files');
        if (empty($files)) {
            return json(['code' => 400, 'message' => '未提供文件信息']);
        }
        try {
            $ticket = \app\model\Ticket::find($id);
            if (!$ticket) {
                return json(['code' => 404, 'message' => '工单不存在']);
            }
            $insertData = [];
            $now = date('Y-m-d H:i:s');
            foreach ($files as $file) {
                $insertData[] = [
                    'ticket_id' => $id,
                    'name' => $file['name'],
                    'path' => $file['path'],
                    'type' => $file['type'],
                    'size' => $file['size'],
                    'create_at' => $now
                ];
            }
            if (!empty($insertData)) {
                Db::name('ticket_attachment')->insertAll($insertData);
            }
            return json(['code' => 200, 'message' => '附件关联成功']);
        } catch (\Exception $e) {
            trace('附件关联失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json(['code' => 500, 'message' => '附件关联失败: ' . $e->getMessage()]);
        }
    }
    /**
     * 同步工单处理人统计数据
     * @param int $ticketId 工单ID
     */
    public function syncTicketHandlerStats($ticketId)
    {
        try {
            // 获取处理人信息
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->select()
                ->toArray();
            $totalCount = count($handlers);
            $completedCount = 0;
            $firstHandlingAt = null;
            $lastCompletedAt = null;
            foreach ($handlers as $handler) {
                // 统计已完成的处理人
                if ($handler['status'] == 4) {
                    $completedCount++;
                    // 更新最后完成时间
                    if ($handler['updated_at'] && (!$lastCompletedAt || strtotime($handler['updated_at']) > strtotime($lastCompletedAt))) {
                        $lastCompletedAt = $handler['updated_at'];
                    }
                }
                // 更新首次处理时间
                if ($handler['handling_at'] && (!$firstHandlingAt || strtotime($handler['handling_at']) < strtotime($firstHandlingAt))) {
                    $firstHandlingAt = $handler['handling_at'];
                }
            }
            // 更新工单统计字段
            Db::name('ticket')
                ->where('id', $ticketId)
                ->update([
                    'handlers_count' => $totalCount,
                    'completed_handlers_count' => $completedCount,
                    'first_handling_at' => $firstHandlingAt,
                    'last_completed_at' => $lastCompletedAt
                ]);
            return true;
        } catch (\Exception $e) {
            trace('同步工单处理人统计数据失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    /**
     * 更新处理人状态
     */
    public function updateHandlerStatus($ticketId, $handlerId, $status, $comment = '')
    {
        try {
            // 现有代码保持不变...
            // 在处理人状态更新后，同步工单统计数据
            $this->syncTicketHandlerStats($ticketId);
            return ['code' => 200, 'message' => '更新成功'];
        } catch (\Exception $e) {
            trace('更新处理人状态失败: ' . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '更新失败：' . $e->getMessage()];
        }
    }
    /**
     * 接受工单
     */
    public function accept()
    {
        try {
            $id = input('post.id/d', 0);
            
            trace('用户尝试接受工单，ID='.$id, 'debug');
            
            // 验证登录状态
            if (!session('user_id')) {
                trace('用户未登录', 'warning');
                return json([
                    'code' => 401,
                    'message' => '未登录或登录已过期'
                ]);
            }
            
            // 参数验证
            if (empty($id)) {
                trace('缺少必要参数', 'warning');
                return json([
                    'code' => 400,
                    'message' => '缺少必要参数'
                ]);
            }
            
            $userId = session('user_id');
            
            // 调用服务
            $ticketService = new TicketService();
            $result = $ticketService->updateHandlerStatus($id, $userId, 1);
            
            return json($result);
        } catch (\Exception $e) {
            trace('接受工单失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '接受工单失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 完成工单
     */
    public function complete()
    {
        try {
            $id = input('post.id/d', 0);
            $comment = input('post.comment/s', '');
            
            trace('用户尝试完成工单，ID='.$id, 'debug');
            
            // 验证登录状态
            if (!session('user_id')) {
                trace('用户未登录', 'warning');
                return json([
                    'code' => 401,
                    'message' => '未登录或登录已过期'
                ]);
            }
            
            // 参数验证
            if (empty($id)) {
                trace('缺少必要参数', 'warning');
                return json([
                    'code' => 400,
                    'message' => '缺少必要参数'
                ]);
            }
            
            $userId = session('user_id');
            
            // 调用服务
            $ticketService = new TicketService();
            $result = $ticketService->completeTicket($id, $userId, $comment);
            
            return json($result);
        } catch (\Exception $e) {
            trace('完成工单失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '完成工单失败：' . $e->getMessage()
            ]);
        }
    }
}