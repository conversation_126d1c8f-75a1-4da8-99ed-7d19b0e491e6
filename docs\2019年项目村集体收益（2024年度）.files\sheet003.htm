<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
 <head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="ProgId" content="Excel.Sheet">
  <meta name="Generator" content="WPS Office ET">
  <link id="Main-File" rel="Main-File" href="../2019年项目村集体收益（2024年度）.html">
  <link rel="File-List" href="filelist.xml">
  <link rel="Stylesheet" href="stylesheet.css">
  <style>
<!-- @page
	{margin:1.00in 0.75in 1.00in 0.75in;
	mso-header-margin:0.50in;
	mso-footer-margin:0.50in;}
 -->  </style>
  <!--[if gte mso 9]>
   <xml>
    <x:WorksheetOptions>
     <x:DefaultRowHeight>270</x:DefaultRowHeight>
     <x:Panes>
      <x:Pane>
       <x:Number>3</x:Number>
       <x:ActiveCol>5</x:ActiveCol>
       <x:ActiveRow>4</x:ActiveRow>
       <x:RangeSelection>F2:F5</x:RangeSelection>
      </x:Pane>
     </x:Panes>
     <x:ProtectContents>False</x:ProtectContents>
     <x:ProtectObjects>False</x:ProtectObjects>
     <x:ProtectScenarios>False</x:ProtectScenarios>
     <x:PageBreakZoom>100</x:PageBreakZoom>
     <x:Print>
      <x:PaperSizeIndex>9</x:PaperSizeIndex>
     </x:Print>
    </x:WorksheetOptions>
   </xml>
  <![endif]-->
  <script language="JavaScript">
	if (window.name!="frSheet")
		window.location.replace("../2019年项目村集体收益（2024年度）.html");
	else
		parent.fnUpdateTabs(2);
</script>
 </head>
 <body link="blue" vlink="purple">
  <table width="639" border="0" cellpadding="0" cellspacing="0" style='width:479.25pt;border-collapse:collapse;table-layout:fixed;'>
   <col width="72" span="4" style='width:54.00pt;'/>
   <col width="279" style='mso-width-source:userset;mso-width-alt:8928;'/>
   <col width="72" style='width:54.00pt;'/>
   <tr height="27" style='height:20.25pt;mso-height-source:userset;mso-height-alt:405;'>
    <td class="xl78" height="27" width="567" colspan="5" style='height:20.25pt;width:425.25pt;border-right:1.0pt solid #000000;border-bottom:1.0pt solid #000000;' x:str>2019年项目河东区光伏项目村集体收益明细表（2024年度）</td>
    <td width="72" style='width:54.00pt;'></td>
   </tr>
   <tr height="19" style='height:14.25pt;'>
    <td class="xl66" height="19" style='height:14.25pt;' x:str>序号</td>
    <td class="xl66" x:str>乡镇</td>
    <td class="xl66" x:str>序号</td>
    <td class="xl66" x:str>村居</td>
    <td class="xl67" x:str>村集体收益（元）</td>
    <td class="xl68" x:str>发放状态</td>
   </tr>
   <tr height="20" style='height:15.00pt;'>
    <td class="xl75" height="20" style='height:15.00pt;' x:num>1</td>
    <td class="xl89" rowspan="2" style='border-right:1.0pt solid windowtext;border-bottom:1.0pt solid windowtext;' x:str>汤头镇</td>
    <td class="xl90" x:num>1</td>
    <td class="xl90" x:str>后篆注村</td>
    <td class="xl91" x:num>10748.16</td>
    <td class="xl74"></td>
   </tr>
   <tr height="20" style='height:15.00pt;'>
    <td class="xl75" height="20" style='height:15.00pt;'></td>
    <td class="xl90" x:num>2</td>
    <td class="xl90" x:str>逯长沟村</td>
    <td class="xl91" x:num>6282.03</td>
    <td class="xl74"></td>
   </tr>
   <tr height="21" style='height:15.75pt;mso-height-source:userset;mso-height-alt:315;'>
    <td class="xl75" height="21" colspan="4" style='height:15.75pt;border-right:1.0pt solid windowtext;border-bottom:1.0pt solid windowtext;' x:str>合计</td>
    <td class="xl91" x:num>17030.19</td>
    <td class="xl74"></td>
   </tr>
   <![if supportMisalignedColumns]>
    <tr width="0" style='display:none;'>
     <td width="279" style='width:209;'></td>
    </tr>
   <![endif]>
  </table>
 </body>
</html>
