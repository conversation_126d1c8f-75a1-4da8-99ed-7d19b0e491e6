<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\console\input\Option;
use think\facade\Db;

class InstallDatabase extends Command
{
    protected function configure()
    {
        $this->setName('install:database')
            ->setDescription('Install database tables')
            ->addOption('force', 'f', Option::VALUE_NONE, 'Force drop existing tables');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            $force = $input->getOption('force');
            
            // 如果指定了 force 选项，先删除已存在的表
            if ($force) {
                $this->dropTables($output);
            }
            
            // 读取 SQL 文件
            $sqlFile = file_get_contents(root_path() . 'database/install.sql');
            
            // 分割 SQL 语句
            $sqlArr = explode(';', $sqlFile);
            
            // 开始事务
            Db::startTrans();
            
            foreach ($sqlArr as $sql) {
                $sql = trim($sql);
                if (!empty($sql)) {
                    try {
                        Db::execute($sql);
                        $output->writeln("Executed SQL: " . substr($sql, 0, 100) . "...");
                    } catch (\Exception $e) {
                        if (!$force && strpos($e->getMessage(), 'already exists') !== false) {
                            $output->writeln("<comment>Table already exists, skipping... Use --force to drop existing tables.</comment>");
                            continue;
                        }
                        throw $e;
                    }
                }
            }
            
            // 提交事务
            Db::commit();
            $output->writeln('<info>Database tables created successfully!</info>');
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $output->writeln('<error>Error: ' . $e->getMessage() . '</error>');
        }
    }

    protected function dropTables($output)
    {
        try {
            // 先删除外键约束
            Db::execute('SET FOREIGN_KEY_CHECKS = 0');

            // 要删除的表名列表
            $tables = [
                'sp_role_permission',
                'sp_permission',
                'sp_user_role',
                'sp_user',
                'sp_role',
                'sp_department'
            ];

            foreach ($tables as $table) {
                try {
                    Db::execute("DROP TABLE IF EXISTS `{$table}`");
                    $output->writeln("<info>Dropped table: {$table}</info>");
                } catch (\Exception $e) {
                    $output->writeln("<comment>Failed to drop table {$table}: {$e->getMessage()}</comment>");
                }
            }

            // 恢复外键约束检查
            Db::execute('SET FOREIGN_KEY_CHECKS = 1');
            
            $output->writeln('<info>All existing tables dropped successfully!</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>Error while dropping tables: ' . $e->getMessage() . '</error>');
            throw $e;
        }
    }
} 