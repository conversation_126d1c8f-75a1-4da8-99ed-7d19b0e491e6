<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Permission;
use think\facade\Db;

class PermissionService extends BaseService
{
    /**
     * 获取权限列表
     */
    public function getList()
    {
        try {
            $permissions = Permission::order('sort', 'asc')->select();
            return $this->success($permissions);
        } catch (\Exception $e) {
            return $this->error('获取权限列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取权限树
     */
    public function getTree()
    {
        try {
            $tree = Permission::getTree();
            return $this->success($tree);
        } catch (\Exception $e) {
            return $this->error('获取权限树失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建权限
     */
    public function create($data)
    {
        try {
            // 验证权限类型
            if (!in_array($data['type'], [Permission::TYPE_MENU, Permission::TYPE_BUTTON, Permission::TYPE_API])) {
                return $this->error('无效的权限类型');
            }

            // 检查权限标识是否已存在
            if (Permission::where('code', $data['code'])->find()) {
                return $this->error('权限标识已存在');
            }

            $permission = new Permission;
            $permission->name = $data['name'];
            $permission->code = $data['code'];
            $permission->type = $data['type'];
            $permission->parent_id = $data['parent_id'] ?? 0;
            $permission->path = $data['path'] ?? '';
            $permission->component = $data['component'] ?? '';
            $permission->icon = $data['icon'] ?? '';
            $permission->sort = $data['sort'] ?? 0;
            $permission->save();

            return $this->success($permission, '权限创建成功');
        } catch (\Exception $e) {
            return $this->error('权限创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新权限
     */
    public function update($id, $data)
    {
        try {
            $permission = Permission::find($id);
            if (!$permission) {
                return $this->error('权限不存在');
            }

            // 检查权限标识是否已被其他权限使用
            if (isset($data['code']) && $data['code'] !== $permission->code) {
                if (Permission::where('code', $data['code'])->where('id', '<>', $id)->find()) {
                    return $this->error('权限标识已存在');
                }
            }

            // 检查是否将权限移动到其子权限下
            if (isset($data['parent_id']) && $data['parent_id'] != 0) {
                $children = Permission::getTree($id);
                $childrenIds = array_column($this->flattenTree($children), 'id');
                if (in_array($data['parent_id'], $childrenIds)) {
                    return $this->error('不能将权限移动到其子权限下');
                }
            }

            $permission->name = $data['name'] ?? $permission->name;
            $permission->code = $data['code'] ?? $permission->code;
            $permission->type = $data['type'] ?? $permission->type;
            $permission->parent_id = $data['parent_id'] ?? $permission->parent_id;
            $permission->path = $data['path'] ?? $permission->path;
            $permission->component = $data['component'] ?? $permission->component;
            $permission->icon = $data['icon'] ?? $permission->icon;
            $permission->sort = $data['sort'] ?? $permission->sort;
            $permission->save();

            return $this->success($permission, '权限更新成功');
        } catch (\Exception $e) {
            return $this->error('权限更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除权限
     */
    public function delete($id)
    {
        try {
            $permission = Permission::find($id);
            if (!$permission) {
                return $this->error('权限不存在');
            }

            // 检查是否有子权限
            if (Permission::where('parent_id', $id)->count() > 0) {
                return $this->error('请先删除子权限');
            }

            // 删除角色关联
            $permission->roles()->detach();
            // 删除权限
            $permission->delete();

            return $this->success(null, '权限删除成功');
        } catch (\Exception $e) {
            return $this->error('权限删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 扁平化权限树
     */
    private function flattenTree($tree)
    {
        $result = [];
        foreach ($tree as $node) {
            $item = $node;
            unset($item['children']);
            $result[] = $item;
            if (isset($node['children'])) {
                $result = array_merge($result, $this->flattenTree($node['children']));
            }
        }
        return $result;
    }

    /**
     * 初始化权限数据
     */
    public function initializePermissions()
    {
        try {
            // 读取 SQL 文件
            $sqlFile = file_get_contents(root_path() . 'database/init/permissions.sql');
            
            // 执行 SQL
            Db::execute($sqlFile);
            
            return $this->success(null, '权限数据初始化成功');
        } catch (\Exception $e) {
            return $this->error('权限数据初始化失败: ' . $e->getMessage());
        }
    }
} 