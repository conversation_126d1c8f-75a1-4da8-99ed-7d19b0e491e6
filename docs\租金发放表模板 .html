<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
 <head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="ProgId" content="Excel.Sheet">
  <meta name="Generator" content="WPS Office ET">
   <xml>
    <x:ExcelWorkbook>
     <x:ExcelWorksheets>
      <x:ExcelWorksheet>
       <x:Name>租金表更新（总表）</x:Name>
       <x:WorksheetOptions>
        <x:DefaultRowHeight>285</x:DefaultRowHeight>
        <x:Selected/>
        <x:FreezePanes/>
        <x:FrozenNoSplit/>
        <x:SplitHorizontal>2</x:SplitHorizontal>
        <x:TopRowBottomPane>2</x:TopRowBottomPane>
        <x:ActivePane>2</x:ActivePane>
        <x:Panes>
         <x:Pane>
          <x:Number>2</x:Number>
          <x:ActiveCol>12</x:ActiveCol>
          <x:ActiveRow>7</x:ActiveRow>
          <x:RangeSelection>M8</x:RangeSelection>
         </x:Pane>
         <x:Pane>
          <x:Number>3</x:Number>
         </x:Pane>
        </x:Panes>
        <x:ProtectContents>False</x:ProtectContents>
        <x:ProtectObjects>False</x:ProtectObjects>
        <x:ProtectScenarios>False</x:ProtectScenarios>
        <x:PageBreakZoom>100</x:PageBreakZoom>
        <x:Zoom>85</x:Zoom>
        <x:Print>
         <x:ValidPrinterInfo/>
         <x:PaperSizeIndex>9</x:PaperSizeIndex>
         <x:HorizontalResolution>600</x:HorizontalResolution>
        </x:Print>
       </x:WorksheetOptions>
      </x:ExcelWorksheet>
     </x:ExcelWorksheets>
     <x:ProtectStructure>False</x:ProtectStructure>
     <x:ProtectWindows>False</x:ProtectWindows>
     <x:SelectedSheets>0</x:SelectedSheets>
     <x:WindowHeight>17775</x:WindowHeight>
     <x:WindowWidth>-27991</x:WindowWidth>
    </x:ExcelWorkbook>
    <x:ExcelName>
     <x:Name>_FilterDatabase</x:Name>
     <x:Hidden/>
     <x:SheetIndex>1</x:SheetIndex>
     <x:Formula>='租金表更新（总表）'!$A$2:$N$3</x:Formula>
    </x:ExcelName>
    <x:ExcelName>
     <x:Name>Print_Titles</x:Name>
     <x:SheetIndex>1</x:SheetIndex>
     <x:Formula>='租金表更新（总表）'!$2:$2</x:Formula>
    </x:ExcelName>
    <x:ExcelName>
     <x:Name>Print_Titles</x:Name>
     <x:SheetIndex>1</x:SheetIndex>
     <x:Formula>='租金表更新（总表）'!$2:2</x:Formula>
    </x:ExcelName>
    <x:SupBook>
     <x:Path>E:\wwwroot\cjgf\server\docs\</x:Path>
    </x:SupBook>
   </xml>
  <![endif]-->
 </head>
 <body link="blue" vlink="purple" class="xl68">
  <table width="1707.13" border="0" cellpadding="0" cellspacing="0" style='width:1280.35pt;border-collapse:collapse;table-layout:fixed;'>
   <tr height="52" class="xl68" style='height:39.00pt;mso-height-source:userset;mso-height-alt:780;'>
    <td class="xl72" height="52" width="1632.13" colspan="13" style='height:39.00pt;width:1224.10pt;border-right:none;border-bottom:none;' x:str>业务三部2019年项目租金支付明细表（2023年度）</td>
    <td class="xl71" width="75" style='width:56.25pt;'></td>
   </tr>
   <tr height="53.33" class="xl69" style='height:40.00pt;mso-height-source:userset;mso-height-alt:800;'>
    <td class="xl73" height="53.33" style='height:40.00pt;' x:str>序号</td>
    <td class="xl73" x:str>姓名</td>
    <td class="xl74" x:str>户号</td>
    <td class="xl75" x:str>电话</td>
    <td class="xl75" x:str>县区</td>
    <td class="xl75" x:str>乡镇</td>
    <td class="xl75" x:str>村名</td>
    <td class="xl73" x:str>银行卡号</td>
    <td class="xl73" x:str>开户银行</td>
    <td class="xl73" x:str>应发租金</td>
    <td class="xl73" x:str>实发租金</td>
    <td class="xl73" x:str>发放状态</td>
    <td class="xl73" x:str>备注</td>
    <td class="xl69"></td>
   </tr>
   <tr height="34.67" class="xl68" style='height:26.00pt;mso-height-source:userset;mso-height-alt:520;'>
    <td class="xl76" height="34.67" style='height:26.00pt;' x:num>1</td>
    <td class="xl77" x:str>王兆飞</td>
    <td class="xl78" x:num="3700971520880.">3700971520880<span style='mso-spacerun:yes;'>&nbsp;</span></td>
    <td class="xl76" x:num>15269993618</td>
    <td class="xl77" x:str>平邑</td>
    <td class="xl75" x:str>保太</td>
    <td class="xl75" x:str>羊城村</td>
    <td class="xl75" x:str>6222021610007273605</td>
    <td class="xl77" x:str>工商银行</td>
    <td class="xl80" x:num="1188.">1188.00<span style='mso-spacerun:yes;'>&nbsp;</span></td>
    <td class="xl80" x:num="1188.">1188.00<span style='mso-spacerun:yes;'>&nbsp;</span></td>
    <td class="xl81"></td>
    <td class="xl82"></td>
    <td class="xl71"></td>
   </tr>
  </table>
 </body>
</html>
