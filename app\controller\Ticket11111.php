<?php
declare (strict_types = 1);
namespace app\controller;
use app\BaseController;
use app\model\Ticket as TicketModel;
use think\facade\Session;
use think\facade\Request;
use app\service\LogService;
use think\App;
use app\service\TicketService;
use app\model\TicketAttachment;
use think\facade\Db;
use think\facade\Filesystem;
use app\common\ExcelHelper;
class Ticket extends BaseController
{
    protected $ticketModel;
    protected $ticketService;
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->ticketModel = new TicketModel();
        $this->ticketService = new TicketService();
    }

    /**
     * 创建工单
     */
    public function create()
    {
        try {
            $data = $this->request->post();
            // 数据验证
            $validate = validate([
                'title' => 'require',
                'content' => 'require',
                'priority' => 'require|in:low,medium,high',
                'category_id' => 'require|number',
                'attachments' => 'array'
            ]);
            if (!$validate->check($data)) {
                return $this->error($validate->getError());
            }
            // 添加创建人信息
            $data['creator_id'] = $this->user->id;
            $data['create_at'] = date('Y-m-d H:i:s');
            $data['status'] = 0;  // 0: 待处理
            // 创建工单
            $ticket = $this->ticketModel->create($data);
            // 处理标签
            if (!empty($data['tags'])) {
                $ticket->tags()->saveAll($data['tags']);
            }
            return $this->success([
                'id' => $ticket->id,
                'message' => '工单创建成功'
            ]);
        } catch (\Exception $e) {
            return $this->error('创建工单失败：' . $e->getMessage());
        }
    }
    /**
     * 生成工单编号
     */
    private function generateTicketNo()
    {
        $date = date('Ymd');
        $key = "ticket_no_{$date}";
        // 获取当日工单序号
        $sequence = cache($key) ?: 0;
        $sequence++;
        // 更新序号缓存，有效期到当天结束
        $expire = strtotime(date('Y-m-d 23:59:59')) - time();
        cache($key, $sequence, $expire);
        // 生成工单编号：WO + 日期 + 4位序号
        return 'WO' . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 删除工单
     */
    public function delete($id)
    {
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            // 只能删除未处理的工单
            if ($ticket->status == 1) {
                return $this->error('已处理的工单不能删除');
            }
            $ticket->delete();
            // 记录操作日志
            LogService::recordOperation('ticket', 'delete', ['id' => $id]);
            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    /**
     * 获取工单统计数据
     */
    public function statistics()
    {
        try {
            $today = date('Y-m-d');
            $data = [
                'total' => $this->ticketModel->count(),
                'pending' => $this->ticketModel->where('status', 0)->count(),
                'completed' => $this->ticketModel->where('status', 1)->count(),
                'today' => $this->ticketModel->whereDay('create_at', $today)->count()
            ];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }
     /**
     * 评价工单
     */
    public function rate($id)
    {
        $score = input('score/d');
        $comment = input('comment/s', '');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            if ($ticket->status != 1) {
                return $this->error('工单未完成,不能评价');
            }
            $ticket->score = $score;
            $ticket->comment = $comment;
            $ticket->save();
            LogService::recordOperation('ticket', 'rate', [
                'id' => $id,
                'score' => $score,
                'comment' => $comment
            ]);
            return $this->success(null, '评价成功');
        } catch (\Exception $e) {
            return $this->error('评价失败：' . $e->getMessage());
        }
    }
    /**
     * 修改优先级
     */
    public function updatePriority($id)
    {
        $priority = input('priority/s');
        try {
            $ticket = $this->ticketModel->find($id);
            if (!$ticket) {
                return $this->error('工单不存在');
            }
            $ticket->priority = $priority;
            $ticket->save();
            LogService::recordOperation('ticket', 'update_priority', [
                'id' => $id,
                'priority' => $priority
            ]);
            return $this->success(null, '优先级修改成功');
        } catch (\Exception $e) {
            return $this->error('修改失败：' . $e->getMessage());
        }
    }
    /**
     * 检查超时工单
     */
    public function checkTimeout()
    {
        try {
            $timeout = 24; // 超时时间(小时)
            $tickets = $this->ticketModel
                ->where('status', 0)
                ->whereTime('create_at', '<=', date('Y-m-d H:i:s', strtotime("-{$timeout} hour")))
                ->select();
            foreach ($tickets as $ticket) {
                // 发送超时提醒通知
                $this->sendTimeoutNotification($ticket);
            }
            return $this->success(null, '检查完成');
        } catch (\Exception $e) {
            return $this->error('检查失败：' . $e->getMessage());
        }
    }
    /**
     * 发送超时提醒
     */
    private function sendTimeoutNotification($ticket)
    {
        // 实现发送提醒逻辑(邮件/短信/站内信等)
    }
    /**
     * 发布工单
     */
    public function publish()
    {
        try {
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            $data = Request::post();
            // 添加创建者信息
            $data['creator_id'] = $this->user->id;
            
            // 验证必要字段
            if (empty($data['department_id'])) {
                return $this->error('请选择处理部门');
            }
            
            // 验证并确保预期完成时间字段存在
            if (empty($data['expect_time'])) {
                return $this->error('请设置预期完成时间');
            }
            
            // 处理顶级部门的特殊情况
            $isTopDepartment = isset($data['is_top_department']) && $data['is_top_department'] == 1;
            trace('是否顶级部门: ' . ($isTopDepartment ? '是' : '否'), 'debug');
            
            // 如果是顶级部门但没有选择处理人，自动获取该部门及其子部门下的所有用户
            if ($isTopDepartment && (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0)) {
                trace('顶级部门自动获取处理人', 'debug');
                // 获取部门ID
                $departmentId = intval($data['department_id']);
                // 查询该部门是否存在并确认是顶级部门
                $department = \think\facade\Db::name('department')->where('id', $departmentId)->find();
                if (!$department || $department['parent_id'] != 0) {
                    return $this->error('部门数据有误，请重新选择');
                }
                // 获取该部门下的所有子部门ID
                $childDepartments = \think\facade\Db::name('department')
                    ->where('parent_id', $departmentId)
                    ->column('id');
                // 将当前部门ID也加入到查询范围
                $allDepartmentIds = array_merge([$departmentId], $childDepartments);
                trace('查询部门IDs: ' . json_encode($allDepartmentIds), 'debug');
                // 查询这些部门下的所有有效用户
                $users = \think\facade\Db::name('user')
                    ->whereIn('department_id', $allDepartmentIds)
                    ->where('status', 1)
                    ->column('id');
                if (!empty($users)) {
                    trace('自动获取到的处理人: ' . json_encode($users), 'debug');
                    $data['handler_ids'] = $users;
                } else {
                    return $this->error('所选部门下没有可用的处理人员');
                }
            }
            // 常规验证处理人
            if (empty($data['handler_ids']) || !is_array($data['handler_ids']) || count($data['handler_ids']) === 0) {
                return $this->error('请选择处理人员');
            }
            // 记录请求数据
            trace('工单发布请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            $result = $this->ticketService->publish($data);
            if ($result['code'] === 200) {
                LogService::recordOperation('ticket', 'publish', array_merge(
                    ['id' => $result['data']['id']],
                    array_diff_key($data, ['content' => '', 'attachments' => []])
                ));
            }
            return json($result);
        } catch (\Exception $e) {
            trace('Publish ticket error: ' . $e->getMessage(), 'error');
            return $this->error('发布工单失败：' . $e->getMessage());
        }
    }

  
    /**
     * 同步工单处理人统计数据
     * @param int $ticketId 工单ID
     */
    public function syncTicketHandlerStats($ticketId)
    {
        try {
            // 获取处理人信息
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->select()
                ->toArray();
            $totalCount = count($handlers);
            $completedCount = 0;
            $firstHandlingAt = null;
            $lastCompletedAt = null;
            foreach ($handlers as $handler) {
                // 统计已完成的处理人
                if ($handler['status'] == 4) {
                    $completedCount++;
                    // 更新最后完成时间
                    if ($handler['updated_at'] && (!$lastCompletedAt || strtotime($handler['updated_at']) > strtotime($lastCompletedAt))) {
                        $lastCompletedAt = $handler['updated_at'];
                    }
                }
                // 更新首次处理时间
                if ($handler['handling_at'] && (!$firstHandlingAt || strtotime($handler['handling_at']) < strtotime($firstHandlingAt))) {
                    $firstHandlingAt = $handler['handling_at'];
                }
            }
            // 更新工单统计字段
            Db::name('ticket')
                ->where('id', $ticketId)
                ->update([
                    'handlers_count' => $totalCount,
                    'completed_handlers_count' => $completedCount,
                    'first_handling_at' => $firstHandlingAt,
                    'last_completed_at' => $lastCompletedAt
                ]);
            return true;
        } catch (\Exception $e) {
            trace('同步工单处理人统计数据失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    /**
     * 更新处理人状态
     */
    public function updateHandlerStatus($ticketId, $handlerId, $status, $comment = '')
    {
        try {
            // 现有代码保持不变...
            // 在处理人状态更新后，同步工单统计数据
            $this->syncTicketHandlerStats($ticketId);
            return ['code' => 200, 'message' => '更新成功'];
        } catch (\Exception $e) {
            trace('更新处理人状态失败: ' . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '更新失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 完成工单
     */
    public function complete()
    {
        try {
            $id = input('post.id/d', 0);
            $comment = input('post.comment/s', '');
            
            trace('用户尝试完成工单，ID='.$id, 'debug');
            
            // 验证登录状态
            if (!session('user_id')) {
                trace('用户未登录', 'warning');
                return json([
                    'code' => 401,
                    'message' => '未登录或登录已过期'
                ]);
            }
            
            // 参数验证
            if (empty($id)) {
                trace('缺少必要参数', 'warning');
                return json([
                    'code' => 400,
                    'message' => '缺少必要参数'
                ]);
            }
            
            $userId = session('user_id');
            
            // 调用服务
            $ticketService = new TicketService();
            $result = $ticketService->completeTicket($id, $userId, $comment);
            
            return json($result);
        } catch (\Exception $e) {
            trace('完成工单失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '完成工单失败：' . $e->getMessage()
            ]);
        }
    }
}