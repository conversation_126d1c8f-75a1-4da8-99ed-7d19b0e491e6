// API 接口路径配置
window.API = {
    // 认证相关接口
    auth: {
        login: '/auth/login',
        logout: '/auth/logout',
        getUserInfo: '/auth/get_user_info',
        changePassword: '/auth/change_password'
    },
    // 用户管理接口
    user: {
        list: '/user/list',
        detail: id => `/user/detail/${id}`,
        create: '/user/create',
        update: id => `/user/update/${id}`,
        delete: id => `/user/delete/${id}`,
        assignRoles: id => `/user/assign_roles/${id}`,
        resetPassword: id => `/user/reset_password/${id}`,
        updateStatus: '/api/user/update_status'
    },
    // 角色管理接口
    role: {
        list: '/api/role/list',
        create: '/api/role/create',
        update: id => `/api/role/update/${id}`,
        delete: id => `/api/role/delete/${id}`,
        detail: id => `/api/role/detail/${id}`
    },
    // 权限管理接口
    permission: {
        list: '/api/permission/list'
    },
    // 站点管理接口
    station: {
        list: '/station/index',
        detail: id => `/station/${id}`,
        problem: {
            list: '/station/problem/list',
            register: '/station/problem/register',
            updateStatus: '/station/problem/updateStatus',
            delete: '/station/problem/delete',
            uploadImage: '/station/problem/uploadImage',
            getDepartmentUsers: '/station/problem/getDepartmentUsers',
            getAllDepartmentUsers: '/station/problem/getAllDepartmentUsers',
            getStationsByGuowang: '/station/problem/getStationsByGuowang',
            getDepartments: '/station/problem/getDepartments',
            index: '/station/problem/index',
            getHandlers: '/station/problem/getHandlers',
            update: '/station/problem/update'
        }
    },
    // 设备管理接口
    device: {
        list: '/api/device/list',
        create: '/api/device/create',
        update: id => `/api/device/update/${id}`,
        delete: id => `/api/device/delete/${id}`,
        detail: id => `/api/device/detail/${id}`
    },
    // 工单模块接口
    ticket: {
        todo: '/ticket_new/todo',
        done: '/ticket_new/done',
        list: '/ticket_new/list',
        handling: '/ticket_new/handling',
        publish: '/ticket_new/publish',
        upload: '/ticket_new/upload',
        detail: id => `/ticket_new/detail/${id}`,
        handle: id => `/ticket_new/handle/${id}`,
        transfer: '/ticket_new/transfer',
        delete: id => `/ticket_new/delete/${id}`,
    },
    // 添加上传相关 API
    upload: {
        image: '/station/problem/uploadImage'
    },
    // 添加部门相关 API
    department: {
        list: '/station/problem/getDepartments',
        users: '/station/problem/getDepartmentUsers'
    },
    // 财务管理模块API
    finance: {
        // 资金归集
        fund: {
            list: '/finance/fund/list',
            save: '/finance/fund/save',
            update: '/finance/fund/update',
            delete: '/finance/fund/delete',
            import: '/finance/fund/import',
            export: '/finance/fund/export',
            template: '/finance/fund/template'
        },
        // 租金发放
        rent: {
            list: '/finance/rent/list',
            save: '/finance/rent/save',
            register: '/finance/rent/register',
            update: '/finance/rent/update',
            delete: '/finance/rent/delete',
            import: '/finance/rent/import',
            export: '/finance/rent/export',
            template: '/finance/rent/template'
        },
        // 国网打款
        grid: {
            list: '/finance/grid/list',
            save: '/finance/grid/save',
            register: '/finance/grid/register',
            update: '/finance/grid/update',
            delete: '/finance/grid/delete',
            import: '/finance/grid/import',
            export: '/finance/grid/export',
            template: '/finance/grid/template'
        },
        // 村集体打款
        village: {
            list: '/finance/village/list',
            save: '/finance/village/save',
            register: '/finance/village/register',
            update: '/finance/village/update',
            delete: '/finance/village/delete',
            import: '/finance/village/import',
            export: '/finance/village/export',
            template: '/finance/village/template'
        },
        // 资金对账
        reconciliation: {
            list: '/finance/reconciliation/list',
            detail: '/finance/reconciliation/detail',
            adjust: '/finance/reconciliation/adjust',
            recalculate: '/finance/reconciliation/recalculate',
            export: '/finance/reconciliation/export'
        }
    }
};

// 防止被覆盖
if (typeof window !== 'undefined') {
    window.API = window.API;
} 