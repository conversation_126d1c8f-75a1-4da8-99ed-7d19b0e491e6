<?php
use think\migration\Migrations;

class CreateCommercialSolarProjectsTable extends Migrations
{
    public function up()
    {
        $table = $this->table('commercial_solar_projects', ['comment' => '工商业光伏项目表']);
        $table->addColumn('project_name', 'string', ['limit' => 100, 'comment' => '项目名称'])
              ->addColumn('installed_capacity', 'decimal', ['precision' => 10, 'scale' => 2, 'comment' => '装机容量(kW)'])
              ->addColumn('investment_amount', 'decimal', ['precision' => 15, 'scale' => 2, 'comment' => '投资额(元)'])
              ->addColumn('location', 'string', ['limit' => 200, 'comment' => '项目地点'])
              ->addColumn('start_date', 'date', ['comment' => '开始日期'])
              ->addColumn('end_date', 'date', ['comment' => '结束日期'])
              ->addColumn('status', 'integer', ['limit' => 1, 'default' => 1, 'comment' => '状态:1-运行中,2-已结束'])
              ->addColumn('created_at', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'comment' => '创建时间'])
              ->addColumn('updated_at', 'timestamp', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP', 'comment' => '更新时间'])
              ->create();
    }

    public function down()
    {
        $this->table('commercial_solar_projects')->drop();
    }
}