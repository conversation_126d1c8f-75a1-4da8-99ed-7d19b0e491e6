<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class TicketAttachment extends Model
{
    protected $name = 'ticket_attachment';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = false;
    
    // 关联工单
    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }
    
    // 关联上传用户
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    // 获取完整文件路径
    public function getFullPathAttr($value, $data)
    {
        return request()->domain() . '/' . $data['path'];
    }
} 