-- 软删除功能验证脚本
-- 用于验证系统中的软删除功能是否正常工作

-- 1. 检查各表的 is_deleted 字段是否存在
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'solar_power' 
    AND COLUMN_NAME = 'is_deleted'
ORDER BY TABLE_NAME;

-- 2. 检查 sp_station 表的软删除数据
SELECT 
    COUNT(*) as total_stations,
    SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_stations,
    SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_stations
FROM sp_station;

-- 3. 检查 sp_station_problem 表的软删除数据
SELECT 
    COUNT(*) as total_problems,
    SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_problems,
    SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_problems
FROM sp_station_problem;

-- 4. 检查 sp_business_station 表的软删除数据（如果字段已添加）
SELECT 
    COUNT(*) as total_business_stations,
    SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_business_stations,
    SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_business_stations
FROM sp_business_station;

-- 5. 查看最近被软删除的电站记录
SELECT 
    id,
    station_name,
    contact_name,
    is_deleted,
    update_at
FROM sp_station 
WHERE is_deleted = 1 
ORDER BY update_at DESC 
LIMIT 10;

-- 6. 查看最近被软删除的问题记录
SELECT 
    id,
    station_id,
    problem_type,
    is_deleted,
    update_time
FROM sp_station_problem 
WHERE is_deleted = 1 
ORDER BY update_time DESC 
LIMIT 10;

-- 7. 验证软删除的数据不会在正常查询中出现
-- 这个查询应该只返回 is_deleted = 0 的记录
SELECT 
    id,
    station_name,
    contact_name,
    is_deleted
FROM sp_station 
WHERE is_deleted = 0
LIMIT 5;

-- 8. 检查是否有遗漏的硬删除操作
-- 查找可能的数据不一致问题
SELECT 
    'sp_station' as table_name,
    COUNT(*) as records_without_is_deleted
FROM sp_station 
WHERE is_deleted IS NULL

UNION ALL

SELECT 
    'sp_station_problem' as table_name,
    COUNT(*) as records_without_is_deleted
FROM sp_station_problem 
WHERE is_deleted IS NULL;

-- 9. 性能检查：确保 is_deleted 字段有适当的索引
SHOW INDEX FROM sp_station WHERE Column_name = 'is_deleted';
SHOW INDEX FROM sp_station_problem WHERE Column_name = 'is_deleted';

-- 10. 建议添加索引以提高软删除查询性能
-- ALTER TABLE sp_station ADD INDEX idx_is_deleted (is_deleted);
-- ALTER TABLE sp_station_problem ADD INDEX idx_is_deleted (is_deleted);
-- ALTER TABLE sp_business_station ADD INDEX idx_is_deleted (is_deleted);
