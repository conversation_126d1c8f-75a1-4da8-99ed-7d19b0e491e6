# 档案初始化队列系统使用说明

## 1. 简介

档案初始化功能现已升级为使用队列系统处理，相比原来的命令行方式，队列系统具有以下优势：

- 更可靠的任务处理
- 失败自动重试机制
- 更好的系统资源利用
- 服务器重启后任务不会丢失
- 更友好的进度显示

## 2. 初始化设置

在首次使用前，需要初始化队列数据表：

```bash
php think queue:init
```

此命令将创建两个表：
- `jobs` - 存储队列任务
- `failed_jobs` - 存储失败的任务

## 3. 启动队列处理进程

### Windows环境

双击根目录下的 `start_queue.bat` 批处理文件，或在命令行中执行：

```bash
start_queue.bat
```

### Linux/Unix环境

执行根目录下的 `start_queue.sh` 脚本：

```bash
chmod +x start_queue.sh
./start_queue.sh
```

如需设置为系统服务，可以使用 Supervisor 或 Systemd 配置。

## 4. 使用方法

1. 登录系统
2. 进入档案初始化页面
3. 选择部门和档案目录
4. 点击"开始初始化档案"按钮
5. 系统会将任务发送到队列并开始处理
6. 页面会实时显示处理进度和日志

## 5. 进度显示说明

- **进度条** - 显示当前处理进度百分比
- **已处理文件数** - 显示已处理/总文件数
- **当前处理文件** - 显示当前正在处理的文件名
- **处理日志** - 显示处理过程中的详细日志
- **最后更新时间** - 显示进度信息的最后更新时间

不同类型的日志会以不同颜色显示：
- 错误信息 - 红色
- 警告信息 - 黄色
- 成功信息 - 绿色
- 普通信息 - 默认颜色

## 6. 故障排查

如果遇到问题，可以查看以下日志文件：

- `runtime/log/queue_worker.log` - 队列处理进程日志
- `runtime/log/[年月]/[日].log` - 系统日志

常见问题：

1. **队列处理进程未启动**
   - 检查是否执行了启动脚本
   - 检查日志中是否有错误信息

2. **任务处理失败**
   - 检查系统日志
   - 检查 `failed_jobs` 表中的失败记录

3. **进度显示不更新**
   - 检查队列处理进程是否在运行
   - 检查 `Cache` 缓存是否正常

## 7. 高级配置

可以通过修改 `config/queue.php` 文件调整队列配置，主要参数：

- `default` - 默认使用的队列连接
- `connections` - 队列连接配置
  - `database` - 数据库队列配置
  - `redis` - Redis队列配置（需安装Redis扩展）

启动队列进程时可用的参数：
- `--queue` - 队列名称，默认为 default
- `--sleep` - 队列为空时休眠秒数，默认为 3
- `--memory` - 内存限制，单位MB，默认为 128
- `--tries` - 最大尝试次数，默认为 0（无限） 