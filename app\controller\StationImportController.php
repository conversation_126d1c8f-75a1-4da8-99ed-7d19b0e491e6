<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;
use think\Request;
use think\facade\Filesystem;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\facade\Config;

class StationImportController
{
    public function import(Request $request)
    {
        // 获取站点类型
        $type = $request->param('type', 1);  // 默认为农户电站类型
        
        // 获取上传的文件
        $file = $request->file('file');
        if (!$file) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 移动文件到临时目录
        $info = $file->move(runtime_path() . 'upload');
        if (!$info) {
            return json(['code' => 400, 'message' => '文件上传失败']);
        }

        // 获取文件路径
        $filePath = $info->getPathname();

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $rows = $sheet->toArray();

        // 检查文件格式
        if (count($rows) < 4) {
            unlink($filePath);
            return json(['code' => 400, 'message' => '文件格式不正确']);
        }

        // 获取列名
        $columns = $rows[2];
        $data = [];

        // 获取 base.json 中的映射关系
        $columnMapping = Config::get('import.station_column_mapping');

        // 遍历数据行
        for ($i = 3; $i < count($rows); $i++) {
            $row = $rows[$i];
            $item = [];
            foreach ($columns as $index => $column) {
                if (isset($columnMapping[$column])) {
                    $item[$columnMapping[$column]] = $row[$index];
                }
            }
            // 设置电站类型
            $item['type'] = $type;
            $data[] = $item;
        }

        // 删除临时文件
        unlink($filePath);

        // 插入数据库
        try {
            Db::name('station')->insertAll($data);
            return json(['code' => 200, 'message' => '导入成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '导入失败: ' . $e->getMessage()]);
        }
    }
}