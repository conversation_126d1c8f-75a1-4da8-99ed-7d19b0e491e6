<?php
declare (strict_types = 1);

namespace app\middleware;

use think\facade\Log;

class LogMiddleware
{
    public function handle($request, \Closure $next)
    {
        // 使用 Log 门面记录日志
        Log::debug('=== Request Started ===');
        Log::debug('Request URI: ' . $request->url());
        Log::debug('Request Method: ' . $request->method());
        Log::debug('Request Data: ' . json_encode($request->param(), JSON_UNESCAPED_UNICODE));

        // 执行请求
        $response = $next($request);

        // 记录响应
        Log::debug('Response Data: ' . $response->getContent());
        Log::debug('=== Request Ended ===');

        return $response;
    }
} 