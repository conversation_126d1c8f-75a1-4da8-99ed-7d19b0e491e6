<?php
declare (strict_types = 1);

namespace app\middleware;

use think\facade\Cache;
use think\facade\Session;
use think\facade\Log;

/**
 * 用户认证中间件
 * 
 * 该中间件负责检查请求的认证状态，处理以下功能：
 * 1. 验证请求头中的 Authorization 令牌
 * 2. 处理重复的 Bearer 前缀问题
 * 3. 尝试多种缓存键格式查找用户信息
 * 4. 设置用户会话信息
 * 5. 在请求对象中附加用户信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
class CheckAuth
{
    /**
     * 处理请求
     *
     * @param \think\Request $request 请求对象
     * @param \Closure       $next 下一个中间件
     * @return Response 响应对象
     */
    public function handle($request, \Closure $next)
    {
        try {
            // 检查请求头中是否包含 Authorization 字段
            $token = $request->header('Authorization');
            
            if (!$token) {
                return json(['code' => 401, 'message' => '未授权，请登录后再试']);
            }
            
            // 处理重复的Bearer前缀问题
            if (strpos($token, 'Bearer Bearer') === 0) {
                $token = str_replace('Bearer Bearer', 'Bearer', $token);
            }
            
            // 提取token的实际值（去掉Bearer前缀）
            $actualToken = $token;
            if (strpos($token, 'Bearer ') === 0) {
                $actualToken = substr($token, 7);
            }
            
            // 尝试多种缓存键格式查找用户信息
            $user = null;
            $cacheKeys = [
                'token_' . $actualToken,  // 不带Bearer前缀
                'token_' . $token,        // 带Bearer前缀
                $actualToken,             // 直接使用token值
                'user_' . $actualToken    // 使用user_前缀
            ];
            
            foreach ($cacheKeys as $key) {
                $tempUserInfo = Cache::get($key);
                if ($tempUserInfo) {
                    $user = $tempUserInfo;
                    break;
                }
            }
            
            if (!$user) {
                return json(['code' => 401, 'message' => '登录已过期，请重新登录']);
            }

            if (is_array($user)) {
                // 将数组转换为标准对象
                $userObj = new \stdClass();
                foreach ($user as $key => $value) {
                    $userObj->$key = $value;
                }
                $user = $userObj;
            }

            $userId = $user->id;       
            if (is_numeric($userId)) {
                // 如果只存储了用户ID，则从数据库获取完整信息
                $userInfo = \app\model\User::find($userId);
                if (!$userInfo) {
                    return json(['code' => 401, 'message' => '用户信息无效', 'data' => null]);
                }
            }else{
                return json(['code' => 401, 'message' => '用户信息无效', 'data' => null]);
            }
            
            // 设置用户会话
            session('user_id', $userId);
            
            // 在请求对象中附加用户信息，方便后续处理
            $request->user = $user;
            $request->userId = $userId;
            
            // 继续下一步处理
            return $next($request);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '系统错误，请稍后重试']);
        }
    }
} 