<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Permission extends Model
{
    // 定义权限类型常量
    const TYPE_MENU = 'menu';
    const TYPE_BUTTON = 'button';
    const TYPE_API = 'api';

    protected $name = 'permission';
    protected $prefix = 'sp_';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    // 类型获取器
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            self::TYPE_MENU => '菜单',
            self::TYPE_BUTTON => '按钮',
            self::TYPE_API => '接口'
        ];
        return $types[$data['type']] ?? $data['type'];
    }

    // 关联角色
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_permission', 'permission_id', 'role_id');
    }

    // 关联父级权限
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    // 关联子权限
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id')->order('sort', 'asc');
    }

    // 获取权限树
    public static function getTree($parentId = 0)
    {
        $permissions = self::where('parent_id', $parentId)
            ->order('sort', 'asc')
            ->select();
        
        $tree = [];
        foreach ($permissions as $perm) {
            $node = $perm->toArray();
            $node['type_text'] = $perm->type_text;
            $children = self::getTree($perm->id);
            if (!empty($children)) {
                $node['children'] = $children;
            }
            $tree[] = $node;
        }
        
        return $tree;
    }
} 