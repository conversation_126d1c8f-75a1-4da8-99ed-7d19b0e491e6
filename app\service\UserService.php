<?php
declare (strict_types = 1);

namespace app\service;

use app\model\User;
use app\model\Role;
use app\model\Department;
use think\facade\Db;
use think\facade\Session;
use think\facade\Log;

class UserService extends BaseService
{
    /**
     * 获取用户列表
     */
    public function getList($params = [])
    {
        try {
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            $keyword = isset($params['keyword']) ? trim($params['keyword']) : '';
            $departmentId = isset($params['department_id']) ? intval($params['department_id']) : 0;

            // 记录查询参数
            trace('获取用户列表参数: ' . json_encode([
                'page' => $page,
                'limit' => $limit,
                'keyword' => $keyword,
                'department_id' => $departmentId,
                'query_from' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'] ?? 'unknown'
            ]), 'debug');

            // 先检查部门是否存在（如果指定了部门ID）
            if ($departmentId > 0) {
                $department = Db::name('department')->where('id', $departmentId)->find();
                if (!$department) {
                    trace('指定的部门不存在: department_id=' . $departmentId, 'debug');
                    // 返回成功但数据为空的结果，不返回嵌套错误码
                    return [
                        'code' => 200,
                        'message' => 'success',
                        'data' => [
                            'total' => 0,
                            'items' => []
                        ]
                    ];
                }
                trace('找到部门: ' . $department['name'] . ' (ID=' . $departmentId . ')', 'debug');
            }

            $query = User::alias('u')
                ->leftJoin('department d', 'u.department_id = d.id')
                ->field([
                    'u.*',
                    'd.name as department_name'
                ]);

            // 按部门ID筛选
            if ($departmentId > 0) {
                $query->where('u.department_id', $departmentId);
                trace('按部门ID筛选用户: department_id=' . $departmentId, 'debug');
            }

            if (!empty($keyword)) {
                $query->where('u.username|u.realname|u.mobile|u.email', 'like', "%{$keyword}%");
            }

            $total = $query->count();
            trace('查询到的用户总数: ' . $total, 'debug');
            
            // 如果没有数据，直接返回空结果
            if ($total === 0) {
                trace('没有找到符合条件的用户，返回空列表。查询条件: department_id=' . $departmentId, 'debug');
                return [
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'total' => 0,
                        'items' => []
                    ]
                ];
            }
            
            try {
                $list = $query->page($page, $limit)
                    ->order('u.id', 'desc')
                    ->select();
                
                trace('查询结果数量: ' . count($list), 'debug');
                if (count($list) === 0) {
                    // 如果分页后没有数据，返回空列表
                    return [
                        'code' => 200,
                        'message' => 'success',
                        'data' => [
                            'total' => $total,
                            'items' => []
                        ]
                    ];
                }
                
                $mappedList = $list->map(function($item) {
                        $data = $item->toArray();
                        
                        // 处理部门信息
                        $data['department'] = [
                            'id' => $data['department_id'],
                            'name' => $data['department_name']
                        ];
                        
                        // 记录原始角色ID字符串，用于调试
                        trace('用户ID: ' . $data['id'] . ', 角色IDs原始值: ' . ($data['role_ids'] ?? '空'), 'debug');
                        
                        // 处理角色信息 - 优化获取角色的方式
                        $data['roles'] = [];
                        
                        if (!empty($data['role_ids'])) {
                            // 确保角色ID格式正确
                            $roleIds = array_filter(array_map('trim', explode(',', $data['role_ids'])));
                            
                            if (!empty($roleIds)) {
                                trace('处理用户角色, 用户ID: ' . $data['id'] . ', 角色IDs: ' . json_encode($roleIds), 'debug');
                                
                                // 查询有效的角色
                                $roles = \think\facade\Db::name('role')
                                    ->whereIn('id', $roleIds)
                                    ->select()
                                    ->toArray();
                                
                                foreach ($roles as $role) {
                                    $data['roles'][] = [
                                        'id' => intval($role['id']),
                                        'name' => $role['name']
                                    ];
                                }
                                
                                trace('获取到的角色: ' . json_encode($data['roles']), 'debug');
                            }
                        }
                        
                        // 移除临时字段
                        unset($data['department_name']);
                        
                        return $data;
                    });
                
                return [
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'total' => $total,
                        'items' => $mappedList
                    ]
                ];
            } catch (\Exception $e) {
                trace('处理用户数据时出错: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
                // 发生错误时也返回一个有效的空列表，而不是错误
                return [
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'total' => 0,
                        'items' => []
                    ]
                ];
            }
        } catch (\Exception $e) {
            trace('获取用户列表失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            // 返回标准格式的错误，不使用嵌套错误码
            return [
                'code' => 500,
                'message' => '获取用户列表失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 创建用户
     */
    public function create($data)
    {
        try {
            Db::startTrans();
            
            // 设置默认密码（通常是123456）
            $defaultPassword = '123456';
            
            // 确保使用统一的密码加密方式
            $data['password'] = password_hash($defaultPassword, PASSWORD_DEFAULT);
            
            // 角色处理
            $roleIds = isset($data['role_ids']) ? $data['role_ids'] : [];
            if (!empty($roleIds) && is_array($roleIds)) {
                $roleIds = array_map('intval', $roleIds);
                $data['role_ids'] = implode(',', $roleIds);
            } else {
                $data['role_ids'] = '';
            }
            
            // 添加用户
            $userId = Db::name('user')->insertGetId([
                'username' => $data['username'],
                'password' => $data['password'],
                'realname' => $data['realname'] ?? '',
                'email' => $data['email'] ?? '',
                'mobile' => $data['mobile'] ?? '',
                'department_id' => $data['department_id'] ?? 0,
                'role_ids' => $data['role_ids'],
                'status' => $data['status'] ?? 1,
                'create_at' => date('Y-m-d H:i:s')
            ]);

            // 获取新创建用户的完整信息
            $user = User::alias('u')
                ->leftJoin('department d', 'u.department_id = d.id')
                ->field([
                    'u.*',
                    'd.name as department_name'
                ])
                ->where('u.id', $userId)
                ->find();

            // 格式化返回数据
            $userData = [
                'id' => $user['id'],
                'username' => $user['username'],
                'realname' => $user['realname'],
                'email' => $user['email'] ?? '',
                'mobile' => $user['mobile'] ?? '',
                'department_id' => $user['department_id'],
                'department' => [
                    'id' => $user['department_id'],
                    'name' => $user['department_name']
                ],
                'status' => $user['status'],
                'create_at' => $user['create_at'],
                'roles' => []
            ];
            
            // 获取角色信息
            if (!empty($user['role_ids'])) {
                $roleIds = explode(',', $user['role_ids']);
                $roles = Db::name('role')->whereIn('id', $roleIds)->select()->toArray();
                $userData['roles'] = array_map(function($role) {
                    return [
                        'id' => $role['id'],
                        'name' => $role['name']
                    ];
                }, $roles);
            }

            Db::commit();
            return $this->success($userData, '创建成功，默认密码为：123456');
        } catch (\Exception $e) {
            Db::rollback();
            trace('创建用户失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return ['code' => 500, 'message' => '用户创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新用户
     */
    public function update($id, $params)
    {
        return $this->transaction(function () use ($id, $params) {
            $user = User::find($id);
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 验证用户名
            if (isset($params['username']) && $params['username'] !== $user->username) {
                if (User::where('username', $params['username'])->where('id', '<>', $id)->find()) {
                    throw new \Exception('用户名已存在');
                }
                $user->username = $params['username'];
            }

            // 验证手机号
            if (isset($params['mobile']) && $params['mobile'] !== $user->mobile) {
                if (User::where('mobile', $params['mobile'])->where('id', '<>', $id)->find()) {
                    throw new \Exception('手机号已存在');
                }
                $user->mobile = $params['mobile'];
            }

            // 验证邮箱
            if (isset($params['email']) && $params['email'] !== $user->email) {
                if (User::where('email', $params['email'])->where('id', '<>', $id)->find()) {
                    throw new \Exception('邮箱已存在');
                }
                $user->email = $params['email'];
            }

            // 更新基本信息
            if (isset($params['realname'])) $user->realname = $params['realname'];
            if (isset($params['department_id'])) $user->department_id = $params['department_id'];
            
            // 更新角色
            if (isset($params['role_ids'])) {
                if (is_array($params['role_ids'])) {
                    $roleIds = array_map('intval', $params['role_ids']);
                    $user->role_ids = implode(',', $roleIds);
                } else {
                    $user->role_ids = '';
                }
            }
            
            $user->save();
            return '更新成功';
        });
    }

    /**
     * 删除用户
     */
    public function delete($id)
    {
        try {
            Db::startTrans();

            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 不能删除自己
            if ($user->id === Session::get('user_id')) {
                return $this->error('不能删除当前登录用户');
            }

            // 由于roles()方法返回的是Collection而不是关联关系，
            // 我们直接清空role_ids字段即可，无需使用detach()
            // $user->role_ids = '';
            // $user->save();
            
            // 删除用户
            $user->delete();

            Db::commit();
            return $this->success('删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 更改用户状态
     */
    public function changeStatus($id, $status)
    {
        try {
            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 不能禁用自己
            if ($user->id === Session::get('user_id')) {
                return $this->error('不能修改当前登录用户的状态');
            }

            $user->status = $status;
            $user->save();

            return $this->success('状态更新成功');
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配角色
     */
    public function assignRoles($id, $roleIds)
    {
        try {
            Log::debug('开始分配角色，用户ID: ' . $id . ', 角色IDs: ' . json_encode($roleIds));
            
            $user = User::find($id);
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 验证角色是否存在
            if (!empty($roleIds)) {
                // 确保roleIds是数组
                if (!is_array($roleIds)) {
                    $roleIds = [$roleIds];
                }
                
                // 转换所有ID为整数
                $roleIds = array_map('intval', $roleIds);
                
                Log::debug('查询有效角色，角色IDs: ' . json_encode($roleIds));
                
                $existingRoles = Db::name('role')
                    ->where('id', 'in', $roleIds)
                    ->where('status', 1)
                    ->select()
                    ->toArray();
                
                Log::debug('找到的有效角色: ' . json_encode($existingRoles));
                
                if (count($existingRoles) !== count($roleIds)) {
                    $invalidRoles = array_diff($roleIds, array_column($existingRoles, 'id'));
                    throw new \Exception('包含无效的角色ID: ' . implode(',', $invalidRoles));
                }
                
                // 设置用户角色
                $validRoleIds = array_column($existingRoles, 'id');
                $user->setAttr('role_ids', implode(',', $validRoleIds));
                $user->save();
                
                Log::debug('角色分配完成，更新的角色IDs: ' . $user->role_ids);
            } else {
                // 清空用户角色
                $user->setAttr('role_ids', '');
                $user->save();
                
                Log::debug('已清空用户角色');
                $existingRoles = [];
            }
            
            // 返回更新后的角色信息
            return $this->success([
                'roles' => array_map(function($role) {
                    return [
                        'id' => intval($role['id']),
                        'name' => $role['name']
                    ];
                }, $existingRoles)
            ], '角色分配成功');
        } catch (\Exception $e) {
            Log::error('角色分配失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('角色分配失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置密码
     */
    public function resetPassword($id)
    {
        try {
            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 重置为默认密码 123456
            $user->password = password_hash('123456', PASSWORD_DEFAULT);
            $user->save();

            return $this->success('密码重置成功');
        } catch (\Exception $e) {
            return $this->error('密码重置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可处理工单的用户列表
     */
    public function getHandlers()
    {
        try {
            // 这里可以根据实际需求添加筛选条件，如特定角色、部门等
            $users = User::where('status', 1)
                ->field('id, username, realname, department_id')
                ->with(['department' => function($query) {
                    $query->field('id, name');
                }])
                ->select();
                
            return ['code' => 200, 'message' => '获取成功', 'data' => $users];
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '获取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取用户详情
     */
    public function detail($id)
    {
        try {
            if (!is_numeric($id)) {
                return ['code' => 400, 'message' => '无效的用户ID'];
            }

            trace('准备查询用户，接收到的ID值: ' . var_export($id, true), 'debug');

            $user = User::with(['department', 'roles'])
                ->field('id, username, realname, department_id, status, create_at')
                ->find($id);
                
            if (!$user) {
                return ['code' => 404, 'message' => '用户不存在'];
            }
            
            return ['code' => 200, 'message' => '获取成功', 'data' => $user];
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '获取失败：' . $e->getMessage()];
        }
    }

    public function changePassword($userId, $oldPassword, $newPassword)
    {
        try {
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return ['code' => 404, 'message' => '用户不存在'];
            }

            if (!password_verify($oldPassword, $user['password'])) {
                return ['code' => 400, 'message' => '原密码错误'];
            }

            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            Db::name('user')->where('id', $userId)->update([
                'password' => $hashedPassword,
                'update_at' => date('Y-m-d H:i:s')
            ]);

            return ['code' => 200, 'message' => '密码修改成功'];
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '密码修改失败：' . $e->getMessage()];
        }
    }

    public function index()
    {
        trace('User控制器index方法被调用，参数：' . json_encode(input()), 'debug');
        // 原有代码...
    }
} 