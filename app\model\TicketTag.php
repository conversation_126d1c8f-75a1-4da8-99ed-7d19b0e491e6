<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class TicketTag extends Model
{
    protected $name = 'ticket_tag';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';
    
    // 多对多关联
    public function tickets()
    {
        return $this->belongsToMany(Ticket::class, 'ticket_tag_relation');
    }
} 