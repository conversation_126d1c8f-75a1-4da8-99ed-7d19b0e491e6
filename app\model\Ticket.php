<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Ticket extends Model
{
    protected $name = 'ticket';
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';
    
    // 设置必填字段
    protected $field = [
        'ticket_no',
        'title',
        'content',
        'category_id',
        'priority',
        'creator_id',
        'creator_name',
        'expect_time',
        'status'
    ];
    
    protected $type = [
        'create_at' => 'datetime',
        'update_at' => 'datetime',
        'completed_at' => 'datetime',
        'attachments' => 'array',
        'handler_ids' => 'json'
    ];

    protected $schema = [
        'id' => 'int',
        'ticket_no' => 'string',
        'title' => 'string',
        'content' => 'string',
        'category_id' => 'int',
        'priority' => 'string',
        'status' => 'int',
        'creator_id' => 'int',
        'creator_name' => 'string',
        'handler_id' => 'int',
        'handler_name' => 'string',
        'department_id' => 'int',
        'handler_ids' => 'string',
        'expect_time' => 'datetime',
        'create_at' => 'datetime',
        'update_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    // 关联创建人
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    // 关联处理人
    public function handler()
    {
        return $this->belongsTo(User::class, 'handler_id')
            ->field(['id', 'username', 'realname', 'department_id'])
            ->bind([
                'handler_name' => 'realname',
                'handler_username' => 'username'
            ]);
    }

    // 获取优先级文本
    public function getPriorityTextAttr($value, $data)
    {
        $priorities = [
            'high' => '高',
            'medium' => '中',
            'low' => '低'
        ];
        return $priorities[$data['priority']] ?? '';
    }

    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            -1 => '已删除',
            0 => '待处理',
            1 => '已处理'
        ];
        return $status[$data['status']] ?? '';
    }
} 