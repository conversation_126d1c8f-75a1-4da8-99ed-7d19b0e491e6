<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\BaseController;
use think\facade\Db;
// use think\facade\Filesystem;
use think\facade\Request;
use think\facade\Log;
use think\file\UploadedFile;

class AttachmentController extends BaseController
{
    /**
     * 上传附件
     * @param int $ticketId 工单ID
     * @param string $uid 临时唯一标识
     */
    public function upload($uid = '', $ticketId = 0)
    {
        // 移到方法开始处进行验证
        if (empty($uid)) {
            Log::error('UID参数缺失，完整请求数据：'.json_encode([
                'GET' => $_GET,
                'POST' => $_POST,
                'FILES' => $_FILES,
                'SERVER' => $_SERVER
            ]));
            return json(['code' => 400, 'message' => 'UID参数必须提供']);
        }
        
        Log::info('开始处理附件上传', [
            'received_ticketId' => $ticketId,
            'received_uid' => $uid,
            'input_uid' => input('uid')
        ]);
        
        try {
            
            // 获取当前用户信息
            $userId = session('user_id');
            $realname = session('realname');
            
            // 如果没有传递uid参数，尝试从请求中获取
            if (empty($uid)) {
                $uid = input('uid', '');
            }
            
            Log::info('上传附件 - 用户ID: ' . $userId . ', 工单ID: ' . $ticketId . ', UID: ' . $uid);
            
            // 检查工单是否存在
            if ($ticketId > 0) {
                $ticket = Db::name('ticket')->where('id', $ticketId)->find();
                if (!$ticket) {
                    return json(['code' => 400, 'message' => '工单不存在']);
                }
                
                // 检查权限（只有创建者或处理人可以上传附件）
                $isAdmin = $this->isAdmin();
                if (!$isAdmin && $ticket['creator_id'] != $userId) {
                    $isHandler = Db::name('ticket_handler')
                        ->where('ticket_id', $ticketId)
                        ->where('handler_id', $userId)
                        ->count() > 0;
                        
                    if (!$isHandler) {
                        return json(['code' => 403, 'message' => '无权上传附件']);
                    }
                }
            }
            
            // 获取上传文件
            $file = request()->file('file');
            if (!$file) {
                return json(['code' => 400, 'message' => '未检测到上传文件']);
            }
            
            Log::info('上传附件 - 文件信息: ' . json_encode([
                'originalName' => $file->getOriginalName(),
                'size' => $file->getSize(),
                'extension' => $file->getOriginalExtension(),
                'type' => $file->getType(),
                'mimeType' => $file->getMime(),
                'tmpName' => $file->getPathname()
            ]));
            
            // 验证文件
            validate(['file' => [
                'fileSize' => 20*1024*1024, // 20MB
                'fileExt' => 'jpg,jpeg,png,gif,doc,docx,xls,xlsx,pdf,zip,rar,txt'
            ]])->check(['file' => $file]);
            
            // 先获取文件信息，避免移动后无法获取
            $originalName = $file->getOriginalName();
            $fileSize = $file->getSize();
            $fileExt = $file->getOriginalExtension();
            $tmpName = $file->getPathname();
            
            // 定义保存路径和文件名
            $savePath = 'ticket/' . date('Y') . '/' . date('m') . '/' . date('d');
            $fileName = date('YmdHis') . '_' . mt_rand(1000, 9999) . '.' . $fileExt;
            $fullPath = 'uploads/' . $savePath;
            $targetDir = public_path() . $fullPath;
            $targetFile = $targetDir . '/' . $fileName;
            
            Log::info('上传附件 - 保存路径: ' . $targetDir . ', 文件名: ' . $fileName);
            
            // 确保目录存在
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
                Log::info('上传附件 - 创建目录: ' . $targetDir);
            }
            
            // 移动文件到目标位置
            if (!move_uploaded_file($tmpName, $targetFile)) {
                Log::error('上传附件 - 移动文件失败: ' . $tmpName . ' -> ' . $targetFile);
                return json(['code' => 500, 'message' => '文件上传失败']);
            }
            
            Log::info('上传附件 - 文件移动成功: ' . $targetFile);
            
            // 构建相对路径
            $relativePath = $fullPath . '/' . $fileName;
            
            $fileInfo = [
                'ticket_id' => $ticketId,
                'uid' => $uid,
                'name' => $originalName,
                'path' => $relativePath,
                'size' => $fileSize,
                'type' => $fileExt,
                'create_at' => date('Y-m-d H:i:s')
            ];
            
            Log::info('上传附件 - 文件信息: ' . json_encode($fileInfo));
            
            // 保存到数据库
            $attachmentId = Db::name('ticket_attachment')->insertGetId([
                'uid' => $uid ?: 'invalid_uid', // 测试用占位符
                'ticket_id' => $ticketId,
                'handler_id' => $userId,
                'handler_name' => $realname,
                'path' => $relativePath,
                'name' => $fileName,
                'original_name' => $originalName,
                'type' => $fileExt,
                'size' => $fileSize,
                'create_at' => date('Y-m-d H:i:s')
            ]);
            
            // 返回文件信息
            $fileInfo['id'] = $attachmentId;
            $fileInfo['url'] = url('ticket.attachment/download', ['id' => $attachmentId])->build();
            $fileInfo['original_name'] = $originalName;
            
            Log::info('上传附件 - 成功: ID=' . $attachmentId);
            
            // 添加调试日志
            Log::info('最终存储的UID：' . $uid . '，类型：' . gettype($uid));
            
            return json([
                'code' => 200,
                'message' => '上传成功',
                'data' => $fileInfo
            ]);
        } catch (\Exception $e) {
            Log::error('上传附件异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'message' => '上传失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 下载附件
     * @param int $id 附件ID
     */
    public function download($id)
    {
        try {
            // 加强ID验证
            if (!is_numeric($id) || $id <= 0) {
                return json(['code' => 400, 'message' => '无效的附件ID']);
            }
            
            // 获取附件信息
            $attachment = Db::name('ticket_attachment')
                ->where('id', intval($id))
                ->find();
            if (!$attachment) {
                return json(['code' => 404, 'message' => '附件不存在']);
            }
            
            // 检查文件是否存在
            $filePath = public_path() . $attachment['path'];
            if (!file_exists($filePath)) {
                return json(['code' => 404, 'message' => '文件不存在']);
            }
            
            // 下载文件
            return download($filePath, $attachment['name'] ?: ('附件_'.$id));
        } catch (\Exception $e) {
            Log::error('下载附件异常: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '下载失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除附件
     * @param int $id 附件ID
     */
    public function delete($id)
    {
        try {
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            
            // 获取附件信息
            $attachment = Db::name('ticket_attachment')->where('id', $id)->find();
            if (!$attachment) {
                return json(['code' => 404, 'message' => '附件不存在']);
            }
            
            // 检查权限（只有创建者或管理员可以删除附件）
            if (!$isAdmin && $attachment['uploader_id'] != $userId) {
                return json(['code' => 403, 'message' => '无权删除附件']);
            }
            
            // 删除文件
            $filePath = public_path() . 'storage/' . $attachment['filename'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // 删除数据库记录
            Db::name('ticket_attachment')->where('id', $id)->delete();
            
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            trace('删除附件异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '删除失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 检查登录状态
     * @return bool|json
     */
    protected function checkLogin()
    {
        $userId = session('user_id');
        if (!$userId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        return true;
    }
} 