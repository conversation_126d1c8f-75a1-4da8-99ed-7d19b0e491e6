-- 为缺少 is_deleted 字段的表添加软删除字段
-- 执行日期：2025年

-- 1. 为 sp_business_station 表添加 is_deleted 字段
ALTER TABLE `sp_business_station` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `grid_connection_time`;

-- 2. 为 sp_user 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_user` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `role_ids`;

-- 3. 为 sp_department 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_department` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `status`;

-- 4. 为 sp_role 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_role` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `status`;

-- 5. 为 sp_permission 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_permission` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `status`;

-- 6. 为 sp_ticket 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_ticket` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `transfer_count`;

-- 7. 为 sp_ticket_category 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_ticket_category` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `description`;

-- 8. 为 sp_alarm 表添加 is_deleted 字段（如果需要）
ALTER TABLE `sp_alarm` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `handle_result`;

-- 9. 为档案表添加 is_deleted 字段
-- 为 sp_station_archive 表添加 is_deleted 字段
ALTER TABLE `sp_station_archive`
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
AFTER `reserve_time`;

-- 为 sp_business_station_archive 表添加 is_deleted 字段
ALTER TABLE `sp_business_station_archive`
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
AFTER `reserve_time`;

-- 为 sp_station_problem_archive 表添加 is_deleted 字段
ALTER TABLE `sp_station_problem_archive`
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
AFTER `update_at`;

-- 注意：sp_station 和 sp_station_problem 表已经有 is_deleted 字段，无需添加
