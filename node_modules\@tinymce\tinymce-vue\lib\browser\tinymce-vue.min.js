var Editor=function(a){"use strict";function n(n){return-1!==t.map(function(n){return n.toLowerCase()}).indexOf(n.toLowerCase())}function e(){return{listeners:[],scriptId:h("tiny-script"),scriptLoaded:!1}}function g(){var n="undefined"!=typeof window?window:global;return n&&n.tinymce?n.tinymce:null}var o,y=function(){return(y=Object.assign||function(n){for(var e,t=1,o=arguments.length;t<o;t++)for(var i in e=arguments[t])Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}).apply(this,arguments)},t=["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCommentChange","onCompositionEnd","onCompositionStart","onCompositionUpdate","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onInput","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"],C=function(t,o,i){Object.keys(o).filter(n).forEach(function(n){var e=o[n];"function"==typeof e&&("onInit"===n?e(t,i):i.on(n.substring(2),function(n){return e(n,i)}))})},S=function(t,n,o,e){var i=t.modelEvents||null,i=Array.isArray(i)?i.join(" "):i;a.watch(e,function(n,e){o&&"string"==typeof n&&n!==e&&n!==o.getContent({format:t.outputFormat})&&o.setContent(n)}),o.on(i||"change input undo redo",function(){n.emit("update:modelValue",o.getContent({format:t.outputFormat}))})},i=0,h=function(n){var e=Date.now();return n+"_"+Math.floor(1e9*Math.random())+ ++i+String(e)},b=function(n){return void 0===n||""===n?[]:Array.isArray(n)?n:n.split(" ")},w=(o=e(),{load:function(n,e,t){o.scriptLoaded?t():(o.listeners.push(t),n.getElementById(o.scriptId)||function(n,e,t,o){var i=e.createElement("script");i.referrerPolicy="origin",i.type="application/javascript",i.id=n,i.src=t;function r(){i.removeEventListener("load",r),o()}i.addEventListener("load",r),e.head&&e.head.appendChild(i)}(o.scriptId,n,e,function(){o.listeners.forEach(function(n){return n()}),o.scriptLoaded=!0}))},reinitialize:function(){o=e()}}),r={apiKey:String,licenseKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],modelValue:String,disabled:Boolean,tinymceScriptSrc:String,outputFormat:{type:String,validator:function(n){return"html"===n||"text"===n}}},D={selector:void 0,target:void 0};return a.defineComponent({props:r,setup:function(u,l){function t(){var n,e,a=(n=p,f?function(){return null!=c&&c.value?c.value:""}:function(){return n?v:m}),t=y(y({},o),{readonly:u.disabled,target:i.value,plugins:(t=o.plugins,e=u.plugins,b(t).concat(b(e))),toolbar:u.toolbar||o.toolbar,inline:s,license_key:u.licenseKey,setup:function(r){(d=r).on("init",function(n){return e=n,t=u,o=l,i=c,(n=r).setContent(a()),o.attrs["onUpdate:modelValue"]&&S(t,o,n,i),void C(e,o.attrs,n);var e,t,o,i}),"function"==typeof o.setup&&o.setup(r)}});null!==(e=i.value)&&"textarea"===e.tagName.toLowerCase()&&(i.value.style.visibility=""),g().init(t),p=!1}var o=u.init?y(y({},u.init),D):y({},D),n=a.toRefs(u),e=n.disabled,c=n.modelValue,n=n.tagName,i=a.ref(null),d=null,r=u.id||h("tiny-vue"),s=u.init&&u.init.inline||u.inline,f=!!l.attrs["onUpdate:modelValue"],p=!0,v=u.initialValue||"",m="";a.watch(e,function(n){var e;null!==d&&("function"==typeof(null===(e=d.mode)||void 0===e?void 0:e.set)?d.mode.set(n?"readonly":"design"):d.setMode(n?"readonly":"design"))}),a.watch(n,function(n){var e;f||(m=d.getContent()),null!==(e=g())&&void 0!==e&&e.remove(d),a.nextTick(t)}),a.onMounted(function(){var n,e;null!==g()?t():i.value&&i.value.ownerDocument&&(e=u.cloudChannel||"7",n=u.apiKey||"no-api-key",e=null==u.tinymceScriptSrc?"https://cdn.tiny.cloud/1/".concat(n,"/tinymce/").concat(e,"/tinymce.min.js"):u.tinymceScriptSrc,w.load(i.value.ownerDocument,e,t))}),a.onBeforeUnmount(function(){null!==g()&&g().remove(d)}),s||(a.onActivated(function(){p||t()}),a.onDeactivated(function(){var n;f||(m=d.getContent()),null!==(n=g())&&void 0!==n&&n.remove(d)}));return l.expose({rerender:function(n){var e;m=d.getContent(),null!==(e=g())&&void 0!==e&&e.remove(d),o=y(y(y({},o),n),D),a.nextTick(t)},getEditor:function(){return d}}),function(){return s?(n=a.h,e=i,t=u.tagName,n(t||"div",{id:r,ref:e})):(0,a.h)("textarea",{id:r,visibility:"hidden",ref:i});var n,e,t}}})}(Vue);
