<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;

class TicketCategory
{
    /**
     * 获取分类列表
     */
    public function index()
    {
        try {
            // 获取所有分类数据
            $categories = Db::name('ticket_category')
                ->field('id, parent_id, name, code, sort, description')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            // 构建树形结构
            $tree = $this->buildTree($categories);

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            trace('获取分类数据失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取分类数据失败',
                'data' => null
            ]);
        }
    }

    /**
     * 构建树形结构
     */
    private function buildTree($categories, $parentId = null)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = $this->buildTree($categories, $category['id']);
                if ($children) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        
        return $tree;
    }

    /**
     * 创建分类
     */
    public function create()
    {
        try {
            $data = request()->post();
            trace('原始请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 获取完整表名和字段
            $tableName = Db::name('ticket_category')->getTable();
            trace('完整表名: ' . $tableName, 'debug');
            
            // 检查表是否存在
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            trace('表是否存在: ' . json_encode($exists, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 获取表结构
            $columns = Db::query("SHOW COLUMNS FROM {$tableName}");
            trace('表结构: ' . json_encode($columns, JSON_UNESCAPED_UNICODE), 'debug');

            // 验证数据
            if (empty($data['name']) || empty($data['code'])) {
                return json([
                    'code' => 400,
                    'message' => '分类名称和编码不能为空',
                    'data' => null
                ]);
            }

            // 检查编码是否已存在
            $exists = Db::name('ticket_category')
                ->where('code', $data['code'])
                ->find();
            
            if ($exists) {
                return json([
                    'code' => 400,
                    'message' => '分类编码已存在',
                    'data' => null
                ]);
            }

            // 构建插入数据
            $insertData = [
                'parent_id' => isset($data['parent_id']) ? intval($data['parent_id']) : null,
                'name' => $data['name'],
                'code' => strtoupper($data['code']),
                'sort' => isset($data['sort']) ? intval($data['sort']) : 0,
                'description' => $data['description'] ?? '',
                'create_at' => date('Y-m-d H:i:s')
            ];
            
            trace('准备插入的数据: ' . json_encode($insertData, JSON_UNESCAPED_UNICODE), 'debug');

            // 使用原生SQL插入数据
            $fields = implode(',', array_keys($insertData));
            $values = implode(',', array_map(function($value) {
                if (is_null($value)) {
                    return 'NULL';
                }
                // 根据类型处理值
                if (is_int($value)) {
                    return $value;
                }
                return "'" . addslashes((string)$value) . "'";
            }, $insertData));
            
            $sql = "INSERT INTO {$tableName} ({$fields}) VALUES ({$values})";
            trace('执行的SQL: ' . $sql, 'debug');
            
            Db::execute($sql);
            $id = Db::name('ticket_category')->getLastInsID();

            trace('插入成功，ID: ' . $id, 'debug');

            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            trace('创建分类失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '创建分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新分类
     */
    public function update($id)
    {
        try {
            $data = request()->put();
            trace('更新请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 验证数据
            if (empty($data['name']) || empty($data['code'])) {
                return json([
                    'code' => 400,
                    'message' => '分类名称和编码不能为空',
                    'data' => null
                ]);
            }

            // 检查编码是否已被其他分类使用
            $exists = Db::name('ticket_category')
                ->where('code', $data['code'])
                ->where('id', '<>', $id)
                ->find();
            
            if ($exists) {
                return json([
                    'code' => 400,
                    'message' => '分类编码已存在',
                    'data' => null
                ]);
            }

            // 构建更新数据
            $updateData = [
                'parent_id' => isset($data['parent_id']) ? intval($data['parent_id']) : null,
                'name' => $data['name'],
                'code' => strtoupper($data['code']),
                'sort' => isset($data['sort']) ? intval($data['sort']) : 0,
                'description' => $data['description'] ?? '',
                'update_at' => date('Y-m-d H:i:s')
            ];
            
            trace('准备更新的数据: ' . json_encode($updateData, JSON_UNESCAPED_UNICODE), 'debug');

            // 更新分类
            $tableName = Db::name('ticket_category')->getTable();
            
            // 构建 SET 子句
            $sets = [];
            foreach ($updateData as $field => $value) {
                if (is_null($value)) {
                    $sets[] = "`{$field}` = NULL";
                } elseif (is_int($value)) {
                    $sets[] = "`{$field}` = {$value}";
                } else {
                    $sets[] = "`{$field}` = '" . addslashes($value) . "'";
                }
            }
            
            $sql = "UPDATE {$tableName} SET " . implode(', ', $sets) . " WHERE id = " . intval($id);
            trace('执行的SQL: ' . $sql, 'debug');
            
            Db::execute($sql);

            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            trace('更新分类失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '更新分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除分类
     */
    public function delete($id)
    {
        try {
            // 检查是否有子分类
            $hasChildren = Db::name('ticket_category')
                ->where('parent_id', $id)
                ->count();
            
            if ($hasChildren > 0) {
                return json([
                    'code' => 400,
                    'message' => '请先删除子分类',
                    'data' => null
                ]);
            }

            // 检查是否有关联的工单
            $hasTickets = Db::name('ticket')
                ->where('category_id', $id)
                ->count();
            
            if ($hasTickets > 0) {
                return json([
                    'code' => 400,
                    'message' => '该分类下有工单，无法删除',
                    'data' => null
                ]);
            }

            // 删除分类
            $result = Db::name('ticket_category')->where('id', $id)->delete();
            
            if (!$result) {
                throw new \Exception('删除失败');
            }

            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            trace('删除分类失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '删除分类失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
} 