<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use think\facade\Log;

class BusinessStationArchiveController extends BaseController
{
    /**
     * 获取工商业电站档案列表
     * @param int $id 工商业电站ID (sp_business_station.id)
     */
    public function index($id)
    {
        try {
            $stationId = intval($id);
            if ($stationId <= 0) {
                return json(['code' => 400, 'message' => '无效的电站ID']);
            }

            $archives = Db::name('business_station_archive') // 操作新表
                ->where('station_id', $stationId)
                ->order('create_at', 'desc')
                ->select()
                ->map(function ($item) {
                    // --- 修改：生成完整 URL ---
                    $baseUrl = request()->domain(); // 获取类似 http://127.0.0.1:66
                    // $item['url'] = '/uploads/' . $item['file_path']; // 旧的相对路径
                    $item['url'] = $baseUrl . '/uploads/' . str_replace('\\', '/', $item['file_path']); // 拼接完整路径，确保正斜杠
                    // --- 修改结束 ---

                    // 根据文件扩展名判断类型 (更可靠的方式是存储 mime 类型)
                    $extension = strtolower(pathinfo($item['file_path'], PATHINFO_EXTENSION));
                    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                    if (in_array($extension, $imageExtensions)) {
                        $item['type'] = 'image';
                    } elseif ($extension === 'pdf') {
                        $item['type'] = 'pdf';
                    } else {
                        $item['type'] = 'other'; // 其他类型
                    }
                    return $item;
                });
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $archives]);
        } catch (\Exception $e) {
            Log::error('获取工商业档案列表失败 (Station ID: ' . $id . '): ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取档案列表失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 上传工商业电站档案文件
     * @param Request $request
     * @param int $id 工商业电站ID (sp_business_station.id)
     */
    public function upload(Request $request, $id)
    {
        $stationId = intval($id);
        if ($stationId <= 0) {
            return json(['code' => 400, 'message' => '无效的电站ID']);
        }
        
        $file = $request->file('file');
        $customName = $request->param('name'); 

        if (!$file || !$file->isValid()) {
            Log::error('工商业档案上传：无效文件 (Station ID: ' . $stationId . ')');
            return json(['code' => 400, 'message' => '文件上传失败或文件无效']);
        }
        
        if (empty($customName)) {
            $customName = $file->getOriginalName();
            Log::warning('工商业档案上传：未收到自定义文件名，使用原始文件名: ' . $customName . ' (Station ID: ' . $stationId . ')');
        } else {
            Log::info('工商业档案上传：收到自定义文件名: ' . $customName . ' (Station ID: ' . $stationId . ')');
        }

        $saveName = null; // 初始化为 null
        $absoluteSavePath = null; // 用于可能的删除操作

        try {
            // 验证文件类型和大小
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'application/pdf'];
            $maxSize = 10 * 1024 * 1024; // 10MB

             validate([
                 'file' => [
                     'fileSize' => $maxSize,
                     'fileMime' => $allowedTypes
                 ]
             ], [
                 'file.fileSize' => '文件大小不能超过10MB',
                 'file.fileMime' => '不支持的文件类型，仅允许 JPG, PNG, GIF, BMP, WebP, PDF'
             ])->check(['file' => $file]);
            
            // 获取文件信息
            $fileSize = $file->getSize();
            $fileMime = $file->getMime();
            $extension = $file->getOriginalExtension();
            
            // --- 修改：参照 StationArchiveController 生成文件名并移动 --- 
            $subDir = 'business_archive' . DIRECTORY_SEPARATOR . date('Ymd');
            $savePath = root_path() . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . $subDir;

            // 1. 生成包含原始扩展名的唯一文件名
            $generatedFileName = md5(uniqid((string)mt_rand(), true)) . '.' . $extension;
            Log::info('工商业档案上传：生成文件名: ' . $generatedFileName);
            
            // 2. 移动文件，并指定使用生成的文件名
            $fileInfo = $file->move($savePath, $generatedFileName); 
            
            if (!$fileInfo) {
                 // 文件移动失败
                throw new \Exception('文件保存失败: ' . $file->getError());
            }
            
            // 获取相对于 public/uploads 的保存路径和文件名 (使用生成的文件名)
            // $saveName = $subDir . DIRECTORY_SEPARATOR . $fileInfo->getFilename(); // 不再需要 getFilename
            $saveName = $subDir . DIRECTORY_SEPARATOR . $generatedFileName; 
            $absoluteSavePath = $fileInfo->getPathname(); // getPathname 仍然获取移动后的完整绝对路径
            Log::info('工商业档案上传：文件已移动至 ' . $absoluteSavePath);
            // --- 修改结束 ---
             
            // 整理数据库记录
            $data = [
                'station_id' => $stationId,
                'name' => $customName,
                'file_path' => str_replace('\\', '/', $saveName), // 确保存储路径分隔符统一为 /
                'file_size' => $fileSize,
                'file_type' => $fileMime,
                'create_at' => date('Y-m-d H:i:s')
            ];
            
            // 插入数据库
            $result = Db::name('business_station_archive')->insert($data);
            if (!$result) {
                 // 如果数据库插入失败，尝试删除已上传的文件
                 if ($absoluteSavePath && file_exists($absoluteSavePath)) {
                     try {
                         unlink($absoluteSavePath);
                         Log::warning('工商业档案上传：数据库插入失败，已删除物理文件: ' . $absoluteSavePath);
                     } catch (\Exception $deleteEx) {
                         Log::error('工商业档案上传：数据库插入失败且删除物理文件失败: ' . $absoluteSavePath . ' Error: ' . $deleteEx->getMessage());
                     }
                 }
                 throw new \Exception('档案信息保存失败');
             }
            
             Log::info('工商业档案上传成功: ' . $data['file_path'] . ' (Station ID: ' . $stationId . ')');
             return json(['code' => 200, 'message' => '上传成功', 'data' => ['path' => $data['file_path']]]);

        } catch (\think\exception\ValidateException $ve) {
            Log::warning('工商业档案上传验证失败 (Station ID: ' . $stationId . '): ' . $ve->getMessage());
            return json(['code' => 400, 'message' => $ve->getMessage()]);
        } catch (\Exception $e) {
            Log::error('工商业档案上传失败 (Station ID: ' . $stationId . '): ' . $e->getMessage());
            // 尝试删除可能已上传的文件
            if ($absoluteSavePath && file_exists($absoluteSavePath)) {
                 try {
                    unlink($absoluteSavePath);
                    Log::info('工商业档案上传失败，已清理文件: ' . $absoluteSavePath);
                } catch (\Exception $deleteEx) {
                    Log::error('工商业档案上传失败，清理文件失败: ' . $absoluteSavePath . ' Error: ' . $deleteEx->getMessage());
                }
            }
            return json(['code' => 500, 'message' => '上传失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除工商业电站档案文件
     * @param int $archiveId 档案记录ID
     */
    public function delete($archiveId)
    {
        Log::debug('!!! Entered BusinessStationArchiveController delete method !!!'); // 添加日志：确认方法是否被调用
        Log::debug("Received archiveId: {$archiveId}"); // 添加日志：记录接收到的 ID

        $archiveId = intval($archiveId);
        if ($archiveId <= 0) {
            return json(['code' => 400, 'message' => '无效的ID']);
        }

        Db::startTrans(); // 开始事务
        try {
            Log::debug("尝试查找工商业档案，ID: {$archiveId}"); // 添加日志：记录查询的 ID
            $archive = Db::name('business_station_archive') // 操作新表
                ->where('id', $archiveId)
                ->find();
            Log::debug("查找结果: ", is_null($archive) ? ['result' => 'null'] : ['result' => 'found', 'data' => $archive]); // 添加日志：记录查找结果

            if (!$archive) {
                // Db::rollback(); // 记录不存在，无需回滚
                return json(['code' => 404, 'message' => '档案文件不存在']);
            }

            // 1. 删除数据库记录
            $deleted = Db::name('business_station_archive')->delete($archiveId);
            if (!$deleted) {
                throw new \Exception('删除数据库记录失败');
            }

            // 2. 删除物理文件
            // --- 修改：使用 unlink 和绝对路径 --- 
            $relativePath = $archive['file_path']; // 路径已包含子目录，如 business_archive/2023/10/27/file.jpg
            $absolutePath = root_path() . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $relativePath);
            
            if (!empty($relativePath) && file_exists($absolutePath)) {
                 if (!unlink($absolutePath)) {
                    Log::warning('工商业档案删除：数据库记录已删，但物理文件删除失败: ' . $absolutePath);
                    // 选择不抛出异常，让操作成功，但记录日志
                 } else {
                     Log::info('工商业档案删除：成功删除物理文件: ' . $absolutePath);
                 }
            } else {
                 Log::warning('工商业档案删除：数据库记录已删，但物理文件不存在或路径为空: ' . $absolutePath);
            }
            // --- 修改结束 ---
            
            Db::commit(); // 提交事务
            Log::info('工商业档案删除成功 (Archive ID: ' . $archiveId . ', Station ID: ' . ($archive['station_id'] ?? 'N/A') . ')');
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback(); // 回滚事务
            Log::error('工商业档案删除失败 (Archive ID: ' . $archiveId . '): ' . $e->getMessage());
            return json(['code' => 500, 'message' => '删除失败: ' . $e->getMessage()]);
        }
    }
} 