<?php
declare (strict_types = 1);
namespace app\service;
use app\model\Ticket;
use app\model\TicketCategory;
use app\model\TicketTag;
use app\model\TicketAttachment;
use app\model\TicketTransfer;
use think\facade\Db;
use think\facade\Event;
class TicketService
{
    /**
     * 获取工单列表
     */
    public function getList($params)
    {
        $query = Ticket::with(['station', 'creator', 'handler', 'category', 'tags']);
        // 筛选条件
            if (!empty($params['keyword'])) {
                $query->where('title|ticket_no', 'like', "%{$params['keyword']}%");
            }
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
            if (!empty($params['priority'])) {
                $query->where('priority', $params['priority']);
            }
        if (!empty($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
            }
            if (!empty($params['start_date'])) {
                $query->whereTime('create_at', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->whereTime('create_at', '<=', $params['end_date']);
            }
        // 分页
        $page = !empty($params['page']) ? intval($params['page']) : 1;
        $limit = !empty($params['limit']) ? intval($params['limit']) : 10;
            $total = $query->count();
        $items = $query->page($page, $limit)
                ->order('create_at', 'desc')
                ->select();
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'items' => $items,
                    'total' => $total
                ]
            ];
    }
    /**
     * 发布工单
     */
    public function publish($data)
    {
        Db::startTrans();
        try {
            // 获取当前用户信息
            $user = request()->user;
            if (!$user) {
                trace('未获取到用户信息', 'error');
                return ['code' => 401, 'message' => '未登录或登录已过期'];
            }
            
            // 确保用户信息包含必要的字段
            $userId = null;
            $username = null;
            
            if (is_object($user)) {
                $userId = $user->id ?? null;
                $username = $user->username ?? null;
            } else if (is_array($user)) {
                $userId = $user['id'] ?? null;
                $username = $user['username'] ?? null;
            }
            
            if (!$userId || !$username) {
                trace('用户信息不完整: ' . json_encode($user), 'error');
                return ['code' => 500, 'message' => '用户信息不完整'];
            }
            
            // 记录原始请求数据
            trace('工单发布原始数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            // 确保 creator_id 有值
            $creatorId = intval($userId);
            if ($creatorId <= 0) {
                trace('无效的创建者ID: ' . $creatorId, 'error');
                return ['code' => 500, 'message' => '无效的创建者信息'];
            }
            // 确保数据类型正确
            $insertData = [
                'ticket_no' => $this->generateTicketNo(),
                'uid' => $data['uid'] ?? null,
                'title' => $data['title'],
                'content' => $data['content'],
                'category_id' => intval($data['category_id']),
                'priority' => $data['priority'],
                'creator_id' => intval($userId),
                'creator_name' => strval($username),
                'expect_time' => $data['expect_time'],
                'status' => 0
            ];
            // 添加部门信息
            if (!empty($data['department_id'])) {
                $insertData['department_id'] = intval($data['department_id']);
                // 记录是否为顶级部门
                if (!empty($data['is_top_department'])) {
                    $insertData['is_top_department'] = intval($data['is_top_department']);
                }
            }
            // 数据验证
            $validate = validate([
                'title' => 'require',
                'content' => 'require',
                'priority' => 'require|in:low,medium,high',
                'category_id' => 'require|number',
                'creator_id' => 'require|number|>:0'
            ]);
            if (!$validate->check($insertData)) {
                trace('数据验证失败: ' . $validate->getError(), 'error');
                return ['code' => 400, 'message' => $validate->getError()];
            }
            // 处理标签数据
            $tagIds = $data['tag_ids'] ?? [];
            trace('标签数据: ' . json_encode($tagIds, JSON_UNESCAPED_UNICODE), 'debug');
            // 处理附件数据
            $attachments = $data['attachments'] ?? [];
            trace('附件数据: ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), 'debug');
            // 记录处理后的工单数据
            trace('准备创建工单数据: ' . json_encode($insertData, JSON_UNESCAPED_UNICODE), 'debug');
            // 创建工单
            $ticket = Ticket::create($insertData);
            trace('工单创建成功，ID: ' . $ticket->id, 'debug');
            
            // 如果有uid，更新附件关联
            if (!empty($insertData['uid'])) {
                try {
                    // 查询是否有相关附件
                    $attachmentCount = Db::name('ticket_attachment')
                        ->where('uid', $insertData['uid'])
                        ->where('ticket_id', 0)
                        ->count();
                    
                    trace('查询到相关附件数量: ' . $attachmentCount . ', UID: ' . $insertData['uid'], 'debug');
                    
                    if ($attachmentCount > 0) {
                        // 更新附件的ticket_id
                        $updateResult = Db::name('ticket_attachment')
                            ->where('uid', $insertData['uid'])
                            ->where('ticket_id', 0)
                            ->update(['ticket_id' => $ticket->id]);
                        
                        trace('更新附件关联结果: ' . $updateResult . '条记录, UID: ' . $insertData['uid'], 'debug');
                    } else {
                        trace('没有找到需要更新的附件记录, UID: ' . $insertData['uid'], 'debug');
                    }
                } catch (\Exception $e) {
                    trace('更新附件关联失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
                    // 不抛出异常，继续处理
                }
            }
            
            // 处理标签关联
            if (!empty($tagIds)) {
                foreach ($tagIds as $tagId) {
                    try {
                        $tagId = intval($tagId);
                        Db::name('ticket_tag_relation')->insert([
                            'ticket_id' => $ticket->id,
                            'tag_id' => $tagId,
                            'create_at' => date('Y-m-d H:i:s')
                        ]);
                        // 更新标签使用次数 - 使用原生SQL更新
                        Db::execute("
                            UPDATE sp_ticket_tag t 
                            SET t.used_count = (
                                SELECT COUNT(*) 
                                FROM sp_ticket_tag_relation r 
                                WHERE r.tag_id = t.id
                            )
                            WHERE t.id = ?
                        ", [$tagId]);
                        trace('标签关联创建成功，标签ID: ' . $tagId, 'debug');
                    } catch (\Exception $e) {
                        trace('标签关联创建失败: ' . $e->getMessage(), 'error');
                        throw $e;
                    }
                }
            }
            // 处理附件
            if (!empty($attachments)) {
                try {
                    $this->handleAttachments($ticket, $attachments);
                    trace('附件处理成功', 'debug');
                } catch (\Exception $e) {
                    trace('附件处理失败: ' . $e->getMessage(), 'error');
                    throw $e;
                }
            }
            // 修改处理人处理逻辑，确保所有处理人都成功添加
            if (!empty($data['handler_ids']) && is_array($data['handler_ids'])) {
                // 获取处理人信息
                $handlerIds = array_map('intval', $data['handler_ids']);
                $handlers = Db::name('user')
                    ->alias('u')
                    ->join('department d', 'u.department_id = d.id', 'LEFT')
                    ->whereIn('u.id', $handlerIds)
                    ->field([
                        'u.id', 
                        'u.username', 
                        'u.realname', 
                        'u.department_id',
                        'd.name as department_name'
                    ])
                    ->select()
                    ->toArray();
                trace('查询到的处理人信息: ' . json_encode($handlers, JSON_UNESCAPED_UNICODE), 'debug');
                // 确保找到所有处理人
                if (count($handlers) != count($handlerIds)) {
                    trace('部分处理人未找到: ' . implode(',', array_diff($handlerIds, array_column($handlers, 'id'))), 'warning');
                }
                // 添加所有处理人，任何一个失败都回滚
                foreach ($handlers as $handler) {
                    // 检查部门名称是否存在，不存在则设为空字符串
                    $departmentName = isset($handler['department_name']) ? $handler['department_name'] : '';
                    Db::name('ticket_handler')->insert([
                        'ticket_id' => $ticket->id,
                        'handler_id' => $handler['id'],
                        'handler_name' => $handler['realname'] ?: $handler['username'],
                        'department_id' => $handler['department_id'] ?: 0,
                        'department_name' => $departmentName,
                        'status' => 0,
                        'create_at' => date('Y-m-d H:i:s')
                    ]);
                    trace('处理人关联创建成功，处理人ID: ' . $handler['id'], 'debug');
                }
            }
            Db::commit();
            trace('工单发布完成', 'debug');
            return ['code' => 200, 'message' => '工单发布成功', 'data' => $ticket];
        } catch (\Exception $e) {
            Db::rollback();
            trace('工单发布失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return ['code' => 500, 'message' => '工单发布失败：' . $e->getMessage()];
        }
    }
    /**
     * 创建工单
     */
    public function create($data)
    {
        Db::startTrans();
        try {
            // 生成工单编号
            $data['ticket_no'] = $this->generateTicketNo();
            // 创建工单
            $ticket = Ticket::create($data);
            // 处理标签
            if (!empty($data['tag_ids'])) {
                $ticket->tags()->saveAll($data['tag_ids']);
            }
            // 触发工单创建事件
            Event::trigger('ticket_created', $ticket);
            // 处理附件
            if (!empty($data['attachments'])) {
                $attachments = [];
                foreach ($data['attachments'] as $attachment) {
                    // 确保每个附件都有必要字段
                    if (!isset($attachment['name']) || !isset($attachment['path'])) {
                        throw new \Exception('附件信息不完整');
                    }
                    $attachments[] = [
                        'ticket_id' => $ticket->id,
                        'name'      => $attachment['name'],
                        'path'      => $attachment['path'],
                        'type'      => $attachment['type'] ?? 'other',
                        'create_at' => date('Y-m-d H:i:s')
                    ];
                }
                Db::name('ticket_attachment')->insertAll($attachments);
            }
            Db::commit();
            return ['code' => 200, 'message' => '创建成功', 'data' => $ticket];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 500, 'message' => '创建失败：' . $e->getMessage()];
        }
    }
    /**
     * 工单分类相关方法
     */
    public function getCategoryList()
    {
        try {
            $categories = TicketCategory::order('sort', 'asc')->select();
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $categories
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage()
            ];
        }
    }
    public function createCategory($data)
    {
        try {
            $category = TicketCategory::create($data);
            return ['code' => 200, 'message' => '创建成功', 'data' => $category];
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '创建失败：' . $e->getMessage()];
        }
    }
    /**
     * 标签管理相关方法
     */
    public function getTagList()
    {
        try {
            $tags = TicketTag::order('id', 'desc')->select();
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $tags
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage()
            ];
        }
    }
    public function createTag($data)
    {
        try {
            $tag = TicketTag::create($data);
            return ['code' => 200, 'message' => '创建成功', 'data' => $tag];
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '创建失败：' . $e->getMessage()];
        }
    }
    /**
     * 转派工单
     */
    public function transfer($id, $data)
    {
        Db::startTrans();
        try {
            trace('开始转派工单，ID='.$id.', 目标处理人='.$data['handler_id'], 'debug');
            // 验证转派原因
            if (empty($data['reason'])) {
                trace('转派原因不能为空', 'warning');
                return ['code' => 400, 'message' => '请填写转派原因'];
            }
            $ticket = Ticket::find($id);
            if (!$ticket) {
                trace('工单不存在，ID='.$id, 'error');
                return ['code' => 404, 'message' => '工单不存在'];
            }
            if ($ticket->status != 0) {
                trace('已处理的工单不能转派，工单状态='.$ticket->status, 'warning');
                return ['code' => 400, 'message' => '已处理的工单不能转派'];
            }
            // 验证目标处理人是否存在
            $targetHandler = Db::name('user')->where('id', $data['handler_id'])->find();
            if (!$targetHandler) {
                trace('目标处理人不存在，ID='.$data['handler_id'], 'error');
                return ['code' => 404, 'message' => '目标处理人不存在'];
            }
            // 从关联表中查找当前处理人
            $currentHandlers = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->select()
                ->toArray();
            // 记录转派历史前的状态
            $handlerStatusLog = [];
            foreach ($currentHandlers as $handler) {
                $handlerStatusLog[] = [
                    'handler_id' => $handler['handler_id'],
                    'handler_name' => $handler['handler_name'],
                    'status' => $handler['status'],
                    'accepted_at' => $handler['accepted_at'] ?? null,
                    'handling_at' => $handler['handling_at'] ?? null,
                    'handling_time' => $handler['handling_time'] ?? 0
                ];
            }
            // 记录当前第一个处理人ID，用于转派记录
            $fromUserId = !empty($currentHandlers) ? $currentHandlers[0]['handler_id'] : 0;
            trace('记录当前处理人状态：'.json_encode($handlerStatusLog), 'debug');
            // 先清除原有处理人关联
            Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->delete();
            trace('已清除原有处理人关联', 'debug');
            // 添加新处理人信息
            $handler = Db::name('user')
                ->alias('u')
                ->join('department d', 'u.department_id = d.id', 'LEFT')
                ->where('u.id', $data['handler_id'])
                ->field([
                    'u.id', 
                    'u.username', 
                    'u.realname', 
                    'u.department_id',
                    'd.name as department_name'
                ])
                ->find();
            if ($handler) {
                // 创建新的处理人关联
                Db::name('ticket_handler')->insert([
                    'ticket_id' => $id,
                    'handler_id' => $handler['id'],
                    'handler_name' => $handler['realname'] ?: $handler['username'],
                    'department_id' => $handler['department_id'] ?: 0,
                    'department_name' => $handler['department_name'] ?: '',
                    'status' => 0,
                    'create_at' => date('Y-m-d H:i:s')
                ]);
                trace('已添加新处理人，ID='.$handler['id'], 'debug');
            } else {
                throw new \Exception('未找到处理人信息');
            }
            // 更新工单转派信息
            $ticket->transfer_reason = $data['reason'] ?? '';
            $ticket->transfer_time = date('Y-m-d H:i:s');
            $ticket->transfer_count = Db::raw('transfer_count + 1'); // 增加转派次数计数
            $ticket->last_transfer_user_id = request()->user->id ?? 0; // 记录最后转派人
            $ticket->save();
            trace('已更新工单转派信息', 'debug');
            // 记录转派历史
            TicketTransfer::create([
                'ticket_id' => $id,
                'from_user_id' => $fromUserId,
                'to_user_id' => $data['handler_id'],
                'reason' => $data['reason'] ?? '',
                'created_by' => request()->user->id ?? 0,
                'handler_status_log' => json_encode($handlerStatusLog),
                'create_at' => date('Y-m-d H:i:s')
            ]);
            trace('已记录转派历史', 'debug');
            // 更新工单处理人计数
            Db::name('ticket')
                ->where('id', $id)
                ->update([
                    'handlers_count' => 1,
                    'completed_handlers_count' => 0
                ]);
            // 发送通知
            $this->sendTransferNotification($ticket, $handler, $data['reason'] ?? '');
            trace('工单转派完成', 'debug');
            Db::commit();
            return ['code' => 200, 'message' => '转派成功'];
        } catch (\Exception $e) {
            Db::rollback();
            trace('转派失败：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return ['code' => 500, 'message' => '转派失败：' . $e->getMessage()];
        }
    }
    /**
     * 发送工单转派通知
     * 
     * @param Ticket $ticket 工单对象
     * @param array $handler 新处理人信息
     * @param string $reason 转派原因
     */
    protected function sendTransferNotification($ticket, $handler, $reason)
    {
        try {
            // 触发工单转派事件，由事件监听器处理实际通知发送
            Event::trigger('ticket_transferred', [
                'ticket' => $ticket,
                'handler' => $handler,
                'reason' => $reason,
                'transfer_time' => date('Y-m-d H:i:s'),
                'operator' => request()->user
            ]);
            trace('已触发工单转派通知事件', 'debug');
            return true;
        } catch (\Exception $e) {
            trace('发送转派通知失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 处理工单附件
     */
    private function handleAttachments($ticket, $attachments)
    {
        $user = request()->user;
        if (!$user) {
            throw new \Exception('未获取到用户信息');
        }
        trace('开始处理附件: ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), 'debug');
        
        // 获取工单的uid
        $uid = $ticket->uid ?? '';
        
        // 如果有uid，检查是否已经处理过附件
        if (!empty($uid)) {
            $existingAttachments = Db::name('ticket_attachment')
                ->where('uid', $uid)
                ->where('ticket_id', $ticket->id)
                ->count();
            
            trace('检查是否已处理附件: 已存在' . $existingAttachments . '条记录, UID: ' . $uid, 'debug');
            
            // 如果已经有关联到当前工单的附件，说明已经处理过，直接返回
            if ($existingAttachments > 0) {
                trace('附件已经处理过，跳过处理', 'debug');
                return;
            }
        }
        
        foreach ($attachments as $attachment) {
            try {
                // 确保有必要的字段
                if (empty($attachment['name']) || empty($attachment['path'])) {
                    trace('附件数据不完整: ' . json_encode($attachment), 'warning');
                    continue;
                }
                
                // 检查是否已存在相同路径的附件
                $existingAttachment = Db::name('ticket_attachment')
                    ->where('path', $attachment['path'])
                    ->where('uid', $uid)
                    ->find();
                
                if ($existingAttachment) {
                    // 如果已存在且ticket_id为0，则更新ticket_id
                    if ($existingAttachment['ticket_id'] == 0) {
                        Db::name('ticket_attachment')
                            ->where('id', $existingAttachment['id'])
                            ->update(['ticket_id' => $ticket->id]);
                        
                        trace('更新已存在附件的ticket_id: ' . $existingAttachment['id'], 'debug');
                    }
                    continue;
                }
                
                // 创建附件记录
                $attachData = [
                    'ticket_id' => $ticket->id,
                    'uid' => $uid,
                    'name' => $attachment['name'],
                    'original_name' => isset($attachment['original_name']) ? $attachment['original_name'] : $attachment['name'],
                    'path' => $attachment['path'],
                    'size' => isset($attachment['size']) ? $attachment['size'] : 0,
                    'type' => isset($attachment['type']) ? $attachment['type'] : pathinfo($attachment['path'], PATHINFO_EXTENSION),
                    'create_at' => date('Y-m-d H:i:s')
                ];
                
                // 记录附件数据
                trace('正在创建附件: ' . json_encode($attachData, JSON_UNESCAPED_UNICODE), 'debug');
                
                // 插入数据库
                Db::name('ticket_attachment')->insert($attachData);
                trace('附件创建成功: ' . $attachment['name'], 'debug');
            } catch (\Exception $e) {
                trace('附件创建失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
                throw $e;
            }
        }
    }
    /**
     * 上传附件
     */
    public function uploadAttachment($file)
    {
        try {
            $info = $file->validate([
                'size' => 10 * 1024 * 1024,  // 限制大小10M
                'ext' => 'jpg,jpeg,png,gif,doc,docx,xls,xlsx,pdf,zip,rar'
            ])->move('storage/tickets');
            if ($info) {
                $data = [
                    'name' => $info->getOriginalName(),
                    'path' => 'storage/tickets/' . $info->getSaveName(),
                    'size' => $info->getSize(),
                    'type' => $info->getExtension()
                ];
                return ['code' => 200, 'message' => '上传成功', 'data' => $data];
            } else {
                return ['code' => 400, 'message' => $file->getError()];
            }
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '上传失败：' . $e->getMessage()];
        }
    }
    // 其他辅助方法...
    private function generateTicketNo()
    {
        $date = date('Ymd');
        $key = "ticket_no_{$date}";
        $sequence = cache($key) ?: 0;
        $sequence++;
        cache($key, $sequence, strtotime(date('Y-m-d 23:59:59')) - time());
        return 'WO' . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);
    }
    protected function buildQuery($params)
    {
        $query = Db::name('ticket');
        // 记录原始参数
        trace('Query params: ' . json_encode($params), 'debug');
        // 逐个添加查询条件
        if (!empty($params['keyword'])) {
            $query->where('title|ticket_no', 'like', "%{$params['keyword']}%");
        }
        if (isset($params['status'])) {
            $query->where('status', '=', intval($params['status']));
        }
        if (!empty($params['priority'])) {
            $query->where('priority', '=', $params['priority']);
        }
        // 记录生成的SQL
        trace('Generated SQL: ' . $query->fetchSql(true)->select(), 'debug');
        return $query;
    }
    /**
     * 获取可选的处理人列表（按部门组织）
     */
    public function getHandlerList()
    {
        try {
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 开始获取处理人列表', 'debug');
            // 1. 获取所有部门
            $departments = Db::name('department')
                ->where('status', 1)
                ->field(['id', 'name', 'parent_id'])
                ->select()
                ->toArray();
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 查询到部门数量: ' . count($departments), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 部门数据: ' . json_encode($departments), 'debug');
            // 2. 获取所有启用的用户
            $users = Db::name('user')
                ->alias('u')
                ->join('user_role ur', 'u.id = ur.user_id')
                ->join('role r', 'ur.role_id = r.id')
                ->where('u.status', 1)
                ->where('r.status', 1)
                ->field([
                    'u.id',
                    'u.username',
                    'u.realname',
                    'u.department_id',
                    'GROUP_CONCAT(r.name) as roles'
                ])
                ->group('u.id')
                ->select()
                ->toArray();
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 查询到用户数量: ' . count($users), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 用户数据: ' . json_encode($users), 'debug');
            // 3. 构建树形结构
            $tree = [];
            foreach ($departments as $dept) {
                if (!$dept['parent_id']) {
                    trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 处理顶级部门: ' . $dept['name'], 'debug');
                    $node = [
                        'id' => 'dept_' . $dept['id'],
                        'label' => $dept['name'],
                        'type' => 'department',
                        'disabled' => true,
                        'children' => $this->buildDepartmentTree($departments, $users, $dept['id'])
                    ];
                    if (!empty($node['children'])) {
                        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 添加部门节点: ' . $dept['name'] . 
                              ', 子节点数量: ' . count($node['children']), 'debug');
                        $tree[] = $node;
                    } else {
                        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 跳过空部门: ' . $dept['name'], 'debug');
                    }
                }
            }
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 最终树形结构节点数量: ' . count($tree), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 返回的树形结构: ' . json_encode($tree), 'debug');
            return $this->success($tree);
        } catch (\Exception $e) {
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 获取处理人列表失败: ' . $e->getMessage() . 
                  "\n" . $e->getTraceAsString(), 'error');
            return $this->error('获取处理人列表失败：' . $e->getMessage());
        }
    }
    /**
     * 递归构建部门树
     */
    protected function buildDepartmentTree($departments, $users, $parentId)
    {
        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 开始构建部门ID ' . $parentId . ' 的子树', 'debug');
        $tree = [];
        // 1. 添加该部门下的用户
        $departmentUsers = array_filter($users, function($user) use ($parentId) {
            return $user['department_id'] == $parentId;
        });
        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 部门 ' . $parentId . ' 下的用户数量: ' . 
              count($departmentUsers), 'debug');
        foreach ($departmentUsers as $user) {
            $userNode = [
                'id' => $user['id'],
                'label' => $user['realname'] ?: $user['username'],
                'type' => 'user',
                'disabled' => false,
                'username' => $user['username'],
                'realname' => $user['realname'],
                'department_id' => $user['department_id'],
                'roles' => explode(',', $user['roles'])
            ];
            $tree[] = $userNode;
            trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 添加用户节点: ' . json_encode($userNode), 'debug');
        }
        // 2. 添加子部门
        $childDepartments = array_filter($departments, function($dept) use ($parentId) {
            return $dept['parent_id'] == $parentId;
        });
        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 部门 ' . $parentId . ' 的子部门数量: ' . 
              count($childDepartments), 'debug');
        foreach ($childDepartments as $dept) {
            $children = $this->buildDepartmentTree($departments, $users, $dept['id']);
            if (!empty($children)) {
                $deptNode = [
                    'id' => 'dept_' . $dept['id'],
                    'label' => $dept['name'],
                    'type' => 'department',
                    'disabled' => true,
                    'children' => $children
                ];
                $tree[] = $deptNode;
                trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 添加子部门节点: ' . $dept['name'] . 
                      ', 子节点数量: ' . count($children), 'debug');
            } else {
                trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 跳过空子部门: ' . $dept['name'], 'debug');
            }
        }
        trace(__FILE__ . ':' . __LINE__ . ' [处理人树] 部门 ' . $parentId . ' 的子树构建完成，节点数量: ' . 
              count($tree), 'debug');
        return $tree;
    }
    /**
     * 获取工单分类列表
     */
    public function getCategories()
    {
        try {
            trace('开始获取工单分类列表', 'debug');
            $categories = Db::name('ticket_category')
                ->where('status', 1)
                ->field(['id', 'name'])
                ->select()
                ->map(function ($item) {
                    return [
                        'id' => $item['id'],
                        'label' => $item['name'],
                        'value' => $item['id']
                    ];
                })
                ->toArray();
            trace('获取到的分类列表: ' . json_encode($categories), 'debug');
            return $this->success($categories);
        } catch (\Exception $e) {
            trace('获取分类列表失败: ' . $e->getMessage(), 'error');
            return $this->error('获取分类列表失败：' . $e->getMessage());
        }
    }
    /**
     * 获取工单标签列表
     */
    public function getTags()
    {
        try {
            trace('开始获取工单标签列表', 'debug');
            $tags = Db::name('ticket_tag')
                ->where('status', 1)
                ->field(['id', 'name', 'color'])
                ->select()
                ->map(function ($item) {
                    return [
                        'id' => $item['id'],
                        'label' => $item['name'],
                        'value' => $item['id'],
                        'color' => $item['color']
                    ];
                })
                ->toArray();
            trace('获取到的标签列表: ' . json_encode($tags), 'debug');
            return $this->success($tags);
        } catch (\Exception $e) {
            trace('获取标签列表失败: ' . $e->getMessage(), 'error');
            return $this->error('获取标签列表失败：' . $e->getMessage());
        }
    }
    /**
     * 获取工单表单数据
     */
    public function getFormData()
    {
        try {
            trace('开始获取工单表单数据', 'debug');
            // 获取分类列表
            $categories = $this->getCategories();
            trace('分类数据: ' . json_encode($categories), 'debug');
            // 获取处理人列表
            $handlers = $this->getHandlerList();
            trace('处理人数据: ' . json_encode($handlers), 'debug');
            // 获取标签列表
            $tags = $this->getTags();
            trace('标签数据: ' . json_encode($tags), 'debug');
            return [
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'categories' => $categories['data'] ?? [],
                    'handlers' => $handlers['data'] ?? [],
                    'tags' => $tags['data'] ?? []
                ]
            ];
        } catch (\Exception $e) {
            trace('获取表单数据失败: ' . $e->getMessage(), 'error');
            return [
                'code' => 500,
                'message' => '获取表单数据失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    protected function success($data = null, string $message = 'success')
    {
        return [
            'code' => 200,
            'message' => $message,
            'data' => $data
        ];
    }
    protected function error($message = 'error', int $code = 500)
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => null
        ];
    }
    /**
     * 更新工单处理人状态
     * @param int $ticketId 工单ID
     * @param int $handlerId 处理人ID
     * @param int $status 状态(1:接受,2:拒绝,3:处理中,4:完成)
     * @param string $feedback 备注
     */
    public function updateHandlerStatus($ticketId, $handlerId, $status, $feedback = '')
    {
        // 添加状态验证
        $validStatuses = ['accepted', 'processing', 'completed'];
        if (!in_array($status, $validStatuses)) {
            throw new \Exception('无效的状态变更');
        }

        // 记录状态变更日志
        trace("更新工单{$ticketId}处理状态为{$status}", 'info');
        
        try {
            trace('开始更新工单处理人状态: 工单ID='.$ticketId.', 处理人ID='.$handlerId.', 状态='.$status, 'debug');
            // 查找处理人记录
            $handler = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->where('handler_id', $handlerId)
                ->find();
            if (!$handler) {
                trace('未找到处理人记录', 'error');
                return ['code' => 404, 'message' => '未找到处理人记录'];
            }
            // 检查状态变更的有效性
            if ($handler['status'] == 2 && $status != 2) {
                // 如果已经拒绝，不能再变更为其他状态
                trace('该处理人已拒绝处理此工单，不能变更状态', 'warning');
                return ['code' => 400, 'message' => '该处理人已拒绝处理此工单，不能变更状态'];
            }
            if ($handler['status'] == 4 && $status != 4) {
                // 如果已经完成，不能再变更为其他状态
                trace('该处理人已完成处理此工单，不能变更状态', 'warning');
                return ['code' => 400, 'message' => '该处理人已完成处理此工单，不能变更状态'];
            }
            $updateData = [
                'status' => $status,
                'feedback' => $feedback,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            // 根据状态设置不同的时间字段
            switch ($status) {
                case 1: // 接受
                    $updateData['accepted_at'] = date('Y-m-d H:i:s');
                    trace('处理人接受工单', 'debug');
                    break;
                case 2: // 拒绝
                    $updateData['rejected_at'] = date('Y-m-d H:i:s');
                    trace('处理人拒绝工单', 'debug');
                    break;
                case 3: // 处理中
                    // 只有首次进入处理中状态时才设置handling_at
                    if (empty($handler['handling_at'])) {
                        $updateData['handling_at'] = date('Y-m-d H:i:s');
                        trace('处理人开始处理工单，记录开始时间', 'debug');
                    }
                    break;
                case 4: // 完成
                    $updateData['completed_at'] = date('Y-m-d H:i:s');
                    // 只有存在处理开始时间时才计算处理时长
                    if (!empty($handler['handling_at'])) {
                        // 计算处理时长(分钟)
                        $start = strtotime($handler['handling_at']);
                        $end = time();
                        $updateData['handling_time'] = ceil(($end - $start) / 60);
                        trace('处理人完成工单，计算处理时长: '.$updateData['handling_time'].'分钟', 'debug');
                    } else {
                        trace('处理人完成工单，但无法计算处理时长(未记录开始时间)', 'warning');
                    }
                    break;
            }
            // 更新处理人状态
            Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->where('handler_id', $handlerId)
                ->update($updateData);
            trace('处理人状态更新完成', 'debug');
            // 如果所有处理人都已完成/拒绝，自动更新工单状态
            if ($status == 4 || $status == 2) {
                $this->checkTicketCompletion($ticketId);
            }
            return ['code' => 200, 'message' => '更新成功'];
        } catch (\Exception $e) {
            trace('更新处理人状态失败: ' . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '更新失败：' . $e->getMessage()];
        }
    }
    /**
     * 检查工单是否可以完成
     * @param int $ticketId 工单ID
     */
    private function checkTicketCompletion($ticketId)
    {
        try {
            // 获取所有处理人状态
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->column('status');
            trace('检查工单完成状态，共'.count($handlers).'个处理人', 'debug');
            // 检查是否所有处理人都已经处理完毕(完成或拒绝)
            $pendingHandlers = 0;
            foreach ($handlers as $status) {
                // 状态0(未处理)、1(已接受)、3(处理中)都表示未完成
                if (in_array($status, [0, 1, 3])) {
                    $pendingHandlers++;
                }
            }
            // 如果没有待处理的处理人，更新工单状态为已完成
            if ($pendingHandlers == 0 && !empty($handlers)) {
                trace('所有处理人都已完成处理，更新工单状态为已完成', 'debug');
                // 更新工单状态为已完成
                Db::name('ticket')
                    ->where('id', $ticketId)
                    ->update([
                        'status' => 1,
                        'finish_time' => date('Y-m-d H:i:s'),
                        'last_completed_at' => date('Y-m-d H:i:s'),
                        'completed_handlers_count' => Db::raw('(SELECT COUNT(*) FROM sp_ticket_handler WHERE ticket_id = '.$ticketId.' AND status = 4)')
                    ]);
                // 触发工单完成事件
                Event::trigger('ticket_completed', $ticketId);
            } else {
                trace('还有'.$pendingHandlers.'个处理人未完成处理，工单状态保持不变', 'debug');
            }
            return true;
        } catch (\Exception $e) {
            trace('检查工单完成状态失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    /**
     * 获取工单的处理人列表
     * @param int $ticketId 工单ID
     */
    public function getTicketHandlers($ticketId)
    {
        try {
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->order('create_at', 'asc')
                ->select()
                ->toArray();
            return ['code' => 200, 'message' => '获取成功', 'data' => $handlers];
        } catch (\Exception $e) {
            trace('获取工单处理人失败: ' . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '获取失败：' . $e->getMessage()];
        }
    }
    /**
     * 完成工单处理
     * @param int $ticketId 工单ID
     * @param int $handlerId 处理人ID
     * @param string $feedback 处理备注
     * @return bool|array
     */
    public function completeTicket($ticketId, $handlerId, $feedback = '')
    {
        try {
            trace("尝试完成工单，ticketId={$ticketId}, handlerId={$handlerId}", 'debug');
            
            // 验证工单是否存在
            $ticket = Db::name('ticket')->where('id', $ticketId)->find();
            if (!$ticket) {
                trace("工单不存在，ticketId={$ticketId}", 'error');
                return [
                    'code' => 404,
                    'message' => '工单不存在'
                ];
            }
            
            // 验证工单是否已完成
            if ($ticket['status'] == 1) {
                trace("工单已完成，不能重复完成，ticketId={$ticketId}", 'warning');
                return [
                    'code' => 400,
                    'message' => '工单已完成，不能重复完成'
                ];
            }
            
            // 验证处理人是否存在且与工单关联
            $handler = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->where('handler_id', $handlerId)
                ->find();
                
            if (!$handler) {
                trace("处理人与工单不匹配，ticketId={$ticketId}, handlerId={$handlerId}", 'error');
                return [
                    'code' => 403,
                    'message' => '您不是此工单的处理人'
                ];
            }
            
            // 验证处理人是否已接受并开始处理
            if ($handler['status'] < 1 || $handler['status'] == 2) {
                trace("处理人尚未接受工单或已拒绝，无法完成，handlerStatus={$handler['status']}", 'warning');
                return [
                    'code' => 400,
                    'message' => '您尚未接受工单或已拒绝，无法完成处理'
                ];
            }
            
            // 验证处理人是否已完成
            if ($handler['status'] == 4) {
                trace("处理人已完成工单，不能重复完成，ticketId={$ticketId}, handlerId={$handlerId}", 'warning');
                return [
                    'code' => 400,
                    'message' => '您已完成此工单，不能重复操作'
                ];
            }
            
            Db::startTrans();
            try {
                // 计算处理时间（分钟）
                $handlingTime = 0;
                if (!empty($handler['handling_at'])) {
                    $startTime = strtotime($handler['handling_at']);
                    $endTime = time();
                    $handlingTime = ceil(($endTime - $startTime) / 60); // 向上取整，至少1分钟
                    if ($handlingTime < 1) {
                        $handlingTime = 1;
                    }
                }
                
                // 更新处理人状态
                $updateData = [
                    'status' => 4, // 已完成
                    'handling_time' => $handlingTime,
                    'feedback' => $feedback,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                Db::name('ticket_handler')
                    ->where('ticket_id', $ticketId)
                    ->where('handler_id', $handlerId)
                    ->update($updateData);
                    
                trace("处理人状态已更新为完成，处理时间：{$handlingTime}分钟", 'debug');
                
                // 检查是否所有处理人都已完成
                $unfinishedHandlers = Db::name('ticket_handler')
                    ->where('ticket_id', $ticketId)
                    ->where('status', '<>', 4) // 未完成状态
                    ->where('status', '<>', 2) // 排除已拒绝的处理人
                    ->count();
                    
                // 如果所有处理人都已完成，更新工单状态为已完成
                if ($unfinishedHandlers == 0) {
                    trace("所有处理人已完成工单，将工单状态更新为已完成", 'debug');
                    
                    // 更新工单状态
                    Db::name('ticket')
                        ->where('id', $ticketId)
                        ->update([
                            'status' => 1, // 已完成
                            'finish_time' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                        
                    // 发送工单完成通知
                    $this->sendTicketCompletedNotification($ticketId);
                } else {
                    trace("还有{$unfinishedHandlers}个处理人未完成工单", 'debug');
                }
                
                Db::commit();
                
                // 返回工单完成状态
                return [
                    'code' => 200,
                    'message' => '工单处理完成',
                    'data' => [
                        'ticket_completed' => ($unfinishedHandlers == 0),
                        'handling_time' => $handlingTime
                    ]
                ];
            } catch (\Exception $e) {
                Db::rollback();
                trace("完成工单处理时发生错误：" . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
                throw $e;
            }
        } catch (\Exception $e) {
            trace("完成工单处理时发生错误：" . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return [
                'code' => 500,
                'message' => '完成工单处理失败：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 发送工单完成通知
     * @param int $ticketId 工单ID
     * @return bool
     */
    protected function sendTicketCompletedNotification($ticketId)
    {
        try {
            trace("开始发送工单完成通知，ticketId={$ticketId}", 'debug');
            
            // 获取工单信息
            $ticket = Db::name('ticket')->where('id', $ticketId)->find();
            if (!$ticket) {
                trace("工单不存在，无法发送通知", 'error');
                return false;
            }
            
            // 获取工单创建者
            $creator = Db::name('user')->where('id', $ticket['creator_id'])->find();
            if (!$creator) {
                trace("工单创建者不存在，无法发送通知", 'warning');
                return false;
            }
            
            // 获取处理人信息
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $ticketId)
                ->where('status', 4) // 只获取已完成的处理人
                ->select()
                ->toArray();
                
            // 生成通知内容
            $handlerNames = array_column($handlers, 'handler_name');
            $handlerNamesStr = implode('、', $handlerNames);
            
            $notificationTitle = "工单已完成处理：{$ticket['title']}";
            $notificationContent = "您创建的工单【{$ticket['title']}】已由 {$handlerNamesStr} 处理完成。";
            
            // 发送站内通知
            $notification = [
                'user_id' => $ticket['creator_id'],
                'title' => $notificationTitle,
                'content' => $notificationContent,
                'type' => 'ticket_completed',
                'relation_id' => $ticketId,
                'read' => 0,
                'create_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('notification')->insert($notification);
            trace("工单完成通知已发送给创建者(ID:{$ticket['creator_id']})", 'debug');
            
            // TODO: 可以添加邮件通知或其他通知方式
            
            return true;
        } catch (\Exception $e) {
            trace("发送工单完成通知失败：" . $e->getMessage(), 'error');
            return false;
        }
    }
} 