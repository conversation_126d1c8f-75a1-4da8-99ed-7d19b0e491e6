<?php
declare (strict_types = 1);
namespace app\service\ticket;

use think\facade\Db;
use app\service\BaseService;

class HandleService extends BaseService
{
    /**
     * 接受工单处理
     * @param int $id 工单ID
     * @param int $userId 当前用户ID
     * @return array
     */
    public function accept($id, $userId)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 查询工单信息
            $ticket = Db::name('ticket')
                ->where('id', $id)
                ->find();
                
            if (!$ticket) {
                return ['code' => 404, 'message' => '工单不存在'];
            }
            
            // 检查工单状态
            if ($ticket['status'] != 0) {
                return ['code' => 400, 'message' => '工单已处理完成，无法接受'];
            }
            
            // 检查是否为处理人
            $handler = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->where('handler_id', $userId)
                ->find();
                
            if (!$handler) {
                return ['code' => 403, 'message' => '您不是该工单的处理人'];
            }
            
            // 检查处理人状态
            if ($handler['status'] != 0) {
                $statusText = $handler['status'] == 1 ? '已接受' : ($handler['status'] == 2 ? '已完成' : '已拒绝');
                return ['code' => 400, 'message' => "您已{$statusText}该工单，不能重复接受"];
            }
            
            // 更新处理人状态
            Db::name('ticket_handler')
                ->where('id', $handler['id'])
                ->update([
                    'status' => 1, // 处理中
                    'update_at' => date('Y-m-d H:i:s')
                ]);
            
            // 记录日志
            $this->logAction($id, $userId, 'accept', '接受处理工单');
            
            Db::commit();
            return ['code' => 200, 'message' => '成功接受工单'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 500, 'message' => '操作失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 拒绝工单处理
     * @param int $id 工单ID
     * @param int $userId 当前用户ID
     * @param string $reason 拒绝原因
     * @return array
     */
    public function reject($id, $userId, $reason)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 查询工单信息
            $ticket = Db::name('ticket')
                ->where('id', $id)
                ->find();
                
            if (!$ticket) {
                return ['code' => 404, 'message' => '工单不存在'];
            }
            
            // 检查工单状态
            if ($ticket['status'] != 0) {
                return ['code' => 400, 'message' => '工单已处理完成，无法拒绝'];
            }
            
            // 检查是否为处理人
            $handler = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->where('handler_id', $userId)
                ->find();
                
            if (!$handler) {
                return ['code' => 403, 'message' => '您不是该工单的处理人'];
            }
            
            // 检查处理人状态
            if ($handler['status'] != 0) {
                $statusText = $handler['status'] == 1 ? '已接受' : ($handler['status'] == 2 ? '已完成' : '已拒绝');
                return ['code' => 400, 'message' => "您已{$statusText}该工单，不能拒绝"];
            }
            
            // 更新处理人状态
            Db::name('ticket_handler')
                ->where('id', $handler['id'])
                ->update([
                    'status' => -1, // 已拒绝
                    'reject_reason' => $reason,
                    'update_at' => date('Y-m-d H:i:s')
                ]);
            
            // 记录日志
            $this->logAction($id, $userId, 'reject', '拒绝处理工单，原因：' . $reason);
            
            Db::commit();
            return ['code' => 200, 'message' => '成功拒绝工单'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 500, 'message' => '操作失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 完成工单处理
     * @param int $id 工单ID
     * @param int $userId 当前用户ID
     * @param string $remark 处理备注
     * @return array
     */
    public function complete($id, $userId, $remark = '')
    {
        // 开启事务
        Db::startTrans();
        try {
            // 查询工单信息
            $ticket = Db::name('ticket')
                ->where('id', $id)
                ->find();
                
            if (!$ticket) {
                return ['code' => 404, 'message' => '工单不存在'];
            }
            
            // 检查工单状态
            if ($ticket['status'] != 0) {
                return ['code' => 400, 'message' => '工单已处理完成，无法再次完成'];
            }
            
            // 检查是否为处理人
            $handler = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->where('handler_id', $userId)
                ->find();
                
            if (!$handler) {
                return ['code' => 403, 'message' => '您不是该工单的处理人'];
            }
            
            // 检查处理人状态
            if ($handler['status'] != 1) {
                if ($handler['status'] == -1) {
                    return ['code' => 400, 'message' => '您已拒绝该工单，无法完成处理'];
                } else if ($handler['status'] == 0) {
                    return ['code' => 400, 'message' => '请先接受工单再完成处理'];
                } else if ($handler['status'] == 2) {
                    return ['code' => 400, 'message' => '您已完成该工单处理'];
                }
            }
            
            // 当前时间
            $now = date('Y-m-d H:i:s');
            
            // 更新处理人状态
            Db::name('ticket_handler')
                ->where('id', $handler['id'])
                ->update([
                    'status' => 2, // 已完成
                    'complete_at' => $now,
                    'remark' => $remark,
                    'update_at' => $now
                ]);
            
            // 检查是否所有处理人都已完成
            $allCompleted = true;
            $handlers = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->where('status', '<>', -1) // 排除已拒绝的
                ->select();
                
            foreach ($handlers as $h) {
                if ($h['status'] != 2) {
                    $allCompleted = false;
                    break;
                }
            }
            
            // 如果所有处理人都已完成，则更新工单状态为已完成
            if ($allCompleted) {
                Db::name('ticket')
                    ->where('id', $id)
                    ->update([
                        'status' => 1, // 已完成
                        'completed_at' => $now,
                        'update_at' => $now
                    ]);
            }
            
            // 记录日志
            $this->logAction($id, $userId, 'complete', '完成工单处理，备注：' . $remark);
            
            Db::commit();
            return [
                'code' => 200, 
                'message' => '成功完成工单处理',
                'data' => ['all_completed' => $allCompleted]
            ];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 500, 'message' => '操作失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 记录工单操作日志
     * @param int $ticketId 工单ID
     * @param int $userId 用户ID
     * @param string $action 操作类型
     * @param string $content 操作内容
     */
    private function logAction($ticketId, $userId, $action, $content)
    {
        Db::name('ticket_log')->insert([
            'ticket_id' => $ticketId,
            'user_id' => $userId,
            'action' => $action,
            'content' => $content,
            'ip' => request()->ip(),
            'create_at' => date('Y-m-d H:i:s')
        ]);
    }
} 