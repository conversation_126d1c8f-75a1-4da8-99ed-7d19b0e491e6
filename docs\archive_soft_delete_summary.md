# 档案文件软删除功能实现总结

## 概述
根据客户要求，将档案文件删除功能从物理删除改为软删除，确保删除的档案文件可以恢复。

## 涉及的档案表

### 1. sp_station_archive (农户电站档案表)
- **用途**: 存储农户电站的档案文件信息
- **关联**: 关联 sp_station 表
- **API路径**: `/asset/archive/{stationId}/{archiveId}`

### 2. sp_business_station_archive (工商业电站档案表)  
- **用途**: 存储工商业电站的档案文件信息
- **关联**: 关联 sp_business_station 表
- **API路径**: `/businessStation/archives/{archiveId}`

### 3. sp_station_problem_archive (问题电站档案表)
- **用途**: 存储运维问题的档案文件信息
- **关联**: 关联 sp_station_problem 表
- **API路径**: `/deleteProblemArchive/{archiveId}`

## 数据库修改

### 添加 is_deleted 字段
为所有档案表添加软删除标记字段：

```sql
-- 农户电站档案表
ALTER TABLE `sp_station_archive` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `reserve_time`;

-- 工商业电站档案表
ALTER TABLE `sp_business_station_archive` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `reserve_time`;

-- 问题电站档案表
ALTER TABLE `sp_station_problem_archive` 
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除' 
AFTER `update_at`;
```

## 后端代码修改

### 1. StationArchiveController.php (农户电站档案)

#### 修改删除方法
- **文件**: `app\controller\StationArchiveController.php`
- **方法**: `delete($stationId, $archiveId)`
- **修改**: 使用软删除，设置 `is_deleted = 1`，保留物理文件

#### 修改列表查询
- **方法**: `index($id)`
- **修改**: 添加 `where('is_deleted', 0)` 过滤条件

### 2. BusinessStationArchiveController.php (工商业电站档案)

#### 修改删除方法
- **文件**: `app\controller\BusinessStationArchiveController.php`
- **方法**: `delete($archiveId)`
- **修改**: 使用软删除，设置 `is_deleted = 1`，保留物理文件

#### 修改列表查询
- **方法**: `index($id)`
- **修改**: 添加 `where('is_deleted', 0)` 过滤条件

### 3. StationProblem.php (问题电站档案)

#### 修改删除方法
- **文件**: `app\controller\StationProblem.php`
- **方法**: `deleteArchive($archiveId)`
- **修改**: 使用软删除，设置 `is_deleted = 1`，保留物理文件

#### 修改列表查询
- **方法**: `archiveList($problemId)`
- **修改**: 添加 `where('is_deleted', 0)` 过滤条件

## 前端代码
前端代码无需修改，因为：
- 前端只负责调用后端删除API
- 软删除逻辑完全由后端处理
- 前端的删除流程保持不变

## 软删除逻辑

### 删除操作
1. 查找档案记录（只查找 `is_deleted = 0` 的记录）
2. 设置 `is_deleted = 1`
3. 更新删除时间（使用 `reserve_time` 或 `update_at` 字段）
4. **保留物理文件**，不执行 `unlink()` 操作

### 查询操作
1. 所有列表查询都添加 `where('is_deleted', 0)` 条件
2. 只显示未删除的档案文件
3. 已删除的档案在前端不可见

## 优势

### 1. 数据安全
- 误删除的档案可以恢复
- 物理文件得到保护
- 数据完整性得到保障

### 2. 审计追踪
- 可以查看删除历史
- 保留删除时间记录
- 便于问题排查

### 3. 存储管理
- 物理文件仍然存在
- 可以通过定时任务清理过期的软删除文件
- 灵活的存储策略

## 注意事项

### 1. 存储空间
- 软删除的文件仍占用磁盘空间
- 需要定期监控存储使用情况
- 建议实施定期清理策略

### 2. 性能影响
- 查询时需要过滤 `is_deleted = 0`
- 建议为 `is_deleted` 字段添加索引
- 大量软删除数据可能影响查询性能

### 3. 数据一致性
- 确保所有查询都正确过滤已删除记录
- 避免在业务逻辑中遗漏软删除条件

## 建议的后续优化

### 1. 添加索引
```sql
ALTER TABLE sp_station_archive ADD INDEX idx_is_deleted (is_deleted);
ALTER TABLE sp_business_station_archive ADD INDEX idx_is_deleted (is_deleted);
ALTER TABLE sp_station_problem_archive ADD INDEX idx_is_deleted (is_deleted);
```

### 2. 数据恢复功能
- 为管理员提供查看已删除档案的界面
- 实现档案恢复功能
- 添加批量恢复操作

### 3. 定期清理机制
- 实现定时任务清理超过一定时间的软删除档案
- 提供手动清理工具
- 清理前进行数据备份

### 4. 监控和报告
- 监控软删除档案的数量和占用空间
- 生成定期报告
- 设置存储空间告警

## 测试验证

### 测试步骤
1. 执行数据库更新脚本
2. 测试各类档案的删除功能
3. 验证删除后列表不显示该档案
4. 验证物理文件仍然存在
5. 验证数据库中 `is_deleted` 字段正确设置

### 验证要点
- 删除操作成功返回
- 前端列表正确更新
- 物理文件未被删除
- 数据库记录正确标记
- 查询性能无明显下降

---
*实施日期：2025年1月*
*状态：已完成*
