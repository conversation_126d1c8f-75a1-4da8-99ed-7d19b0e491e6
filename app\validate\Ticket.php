<?php
namespace app\validate;

use think\Validate;

class Ticket extends Validate
{
    protected $rule = [
        'title' => 'require|max:100',
        'content' => 'require',
        'category_id' => 'require|number',
        'priority' => 'require|in:high,medium,low',
        'expect_time' => 'require|date'
    ];
    
    protected $message = [
        'title.require' => '工单标题不能为空',
        'title.max' => '工单标题最多100个字符',
        'content.require' => '工单内容不能为空',
        'category_id.require' => '工单分类不能为空',
        'category_id.number' => '工单分类格式错误',
        'priority.require' => '优先级不能为空',
        'priority.in' => '优先级只能是high、medium、low',
        'expect_time.require' => '完成预期时间不能为空',
        'expect_time.date' => '完成预期时间格式错误'
    ];
}