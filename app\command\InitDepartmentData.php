<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\model\Department;
use app\model\Role;
use app\model\User;

class InitDepartmentData extends Command
{
    protected function configure()
    {
        $this->setName('init:department-data')
            ->setDescription('Initialize department data, roles and users');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            \think\facade\Db::startTrans();

            // 1. 创建部门
            $departments = [
                ['name' => '总部', 'parent_id' => 0, 'sort' => 1],
                ['name' => '综合部', 'parent_id' => 1, 'sort' => 1],
                ['name' => '投融资部', 'parent_id' => 1, 'sort' => 2],
                ['name' => '资产管理部', 'parent_id' => 1, 'sort' => 3],
                ['name' => '业务二部', 'parent_id' => 1, 'sort' => 4],
                ['name' => '业务三部', 'parent_id' => 1, 'sort' => 5],
                ['name' => '业务七部', 'parent_id' => 1, 'sort' => 6]
            ];

            foreach ($departments as $dept) {
                Department::create($dept);
            }
            $output->writeln('Departments created successfully.');

            // 2. 创建角色
            $roles = [
                ['name' => '超级管理员', 'description' => '系统超级管理员，拥有所有权限'],
                ['name' => '综合部经理', 'description' => '综合部门负责人，负责数据报表和算法管理'],
                ['name' => '投融资部经理', 'description' => '投融资部门负责人，负责收益统计和对账'],
                ['name' => '资产管理部经理', 'description' => '资产管理部门负责人，负责档案管理'],
                ['name' => '业务二部经理', 'description' => '业务二部负责人，负责电费核算和租金管理'],
                ['name' => '业务三部经理', 'description' => '业务三部负责人，负责部门监督和数据同步'],
                ['name' => '业务七部经理', 'description' => '业务七部负责人，负责数据管理和问题处理'],
                ['name' => '普通用户', 'description' => '普通员工角色，基本操作权限']
            ];

            foreach ($roles as $role) {
                Role::create($role);
            }
            $output->writeln('Roles created successfully.');

            // 3. 创建用户并分配角色
            $users = [
                ['username' => 'admin', 'realname' => '系统管理员', 'department' => '总部', 'role' => '超级管理员'],
                ['username' => 'zhb_manager', 'realname' => '综合部经理', 'department' => '综合部', 'role' => '综合部经理'],
                ['username' => 'tzr_manager', 'realname' => '投融资部经理', 'department' => '投融资部', 'role' => '投融资部经理'],
                ['username' => 'zcgl_manager', 'realname' => '资产管理部经理', 'department' => '资产管理部', 'role' => '资产管理部经理'],
                ['username' => 'yw2_manager', 'realname' => '业务二部经理', 'department' => '业务二部', 'role' => '业务二部经理'],
                ['username' => 'yw3_manager', 'realname' => '业务三部经理', 'department' => '业务三部', 'role' => '业务三部经理'],
                ['username' => 'yw7_manager', 'realname' => '业务七部经理', 'department' => '业务七部', 'role' => '业务七部经理']
            ];

            foreach ($users as $userData) {
                $department = Department::where('name', $userData['department'])->find();
                $role = Role::where('name', $userData['role'])->find();

                $user = User::create([
                    'username' => $userData['username'],
                    'password' => 'password', // 默认密码
                    'realname' => $userData['realname'],
                    'department_id' => $department->id,
                    'status' => 1
                ]);

                $user->roles()->attach($role->id);
            }
            $output->writeln('Users created and roles assigned successfully.');

            \think\facade\Db::commit();
            $output->writeln('All data initialized successfully!');

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            $output->writeln('Error: ' . $e->getMessage());
        }
    }
} 