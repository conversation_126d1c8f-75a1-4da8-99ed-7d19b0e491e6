# 分布式光伏运维平台开发者指南：五个关键问题

## 1. 系统架构与技术栈

**问题**：系统的整体架构是什么，使用了哪些主要技术？

**回答**：
- **架构模式**：前后端分离的B/S架构
- **后端技术栈**：
  - PHP 7.4+ 
  - ThinkPHP 6.x 框架（MVC模式）
  - MySQL 5.7+ 数据库
  - Redis 缓存
- **前端技术栈**：
  - Vue.js 2.x
  - Element UI 组件库
  - Axios HTTP客户端
  - ECharts 数据可视化
- **服务器环境**：
  - Nginx/Apache Web服务器
  - Linux CentOS 7+

系统采用了经典的三层架构：控制器层（Controller）、服务层（Service）和数据访问层（Model）。这种架构确保了业务逻辑与数据访问的分离，提高了代码的可维护性和可扩展性。

## 2. 核心功能模块与数据流

**问题**：系统的主要功能模块是什么，它们之间的数据流是怎样的？

**回答**：
系统包含以下核心功能模块：

1. **档案管理模块**
   - 农户电站基础档案管理
   - 工商业电站基础档案管理
   - 问题电站管理

2. **数据分析模块**
   - 发电量管理
   - 收益分析
   - 目标管理

3. **财务管理模块**
   - 收入管理
   - 支出管理
   - 对账管理

4. **权限管理模块**
   - 用户管理
   - 角色管理
   - 权限控制
   - 部门管理

5. **工单管理模块**
   - 待办工单
   - 已办工单
   - 工单发布与处理

数据流程：
- 用户通过前端界面提交请求
- 请求经过路由分发到对应的控制器
- 控制器调用服务层处理业务逻辑
- 服务层通过模型层访问数据库
- 结果返回给前端展示

## 3. 代码组织与重构需求

**问题**：代码是如何组织的，存在哪些需要重构的地方？

**回答**：
当前代码组织：
- 后端代码按照ThinkPHP框架的标准目录结构组织
- 前端代码主要在public目录下，按功能模块分组

主要重构需求：
1. **控制器拆分**：
   - 工单控制器（Ticket.php，857行）过于庞大，需要拆分为多个专用控制器
   - 问题电站控制器（StationProblem.php，481行）同样需要拆分

2. **服务层优化**：
   - 提取共用逻辑到独立服务类
   - 创建通用服务如FileService、NotificationService等

3. **前端组件化改造**：
   - 将大型JS文件拆分为小型组件
   - 创建可复用的表单、列表和详情组件

4. **代码复用提升**：
   - 工单和问题电站模块有类似逻辑，但没有共享代码
   - 需要提取共同逻辑到独立服务

## 4. 数据库设计与关系

**问题**：系统的数据库设计是怎样的，主要表之间有什么关系？

**回答**：
系统使用MySQL数据库，主要表结构包括：

1. **核心业务表**：
   - `sp_station`：电站基本信息
   - `sp_station_problem`：问题电站记录
   - `sp_station_problem_handler`：问题电站处理人关联
   - `sp_station_problem_image`：问题电站图片
   - `sp_ticket`：工单信息
   - `sp_ticket_handler`：工单处理人关联
   - `sp_ticket_attachment`：工单附件
   - `sp_ticket_tag`：工单标签
   - `sp_ticket_category`：工单分类
   - `sp_ticket_transfer`：工单转派记录

2. **系统管理表**：
   - `sp_user`：用户信息
   - `sp_role`：角色信息
   - `sp_permission`：权限信息
   - `sp_department`：部门信息
   - `sp_login_log`：登录日志
   - `sp_operation_log`：操作日志

主要关系：
- 一个电站可以有多个问题记录
- 一个问题可以有多个处理人和多张图片
- 一个工单可以有多个处理人、多个附件和多个标签
- 用户、角色、权限之间是多对多关系

## 5. API设计与前后端交互

**问题**：系统的API是如何设计的，前后端如何交互？

**回答**：
系统API设计采用RESTful风格，主要特点：

1. **API路径规范**：
   - 资源集合：`/resource`（如`/ticket`）
   - 特定资源：`/resource/{id}`（如`/ticket/1`）
   - 资源关联：`/resource/{id}/relation`（如`/ticket/1/attachments`）

2. **HTTP方法使用**：
   - GET：获取资源
   - POST：创建资源
   - PUT：更新资源
   - DELETE：删除资源

3. **响应格式统一**：
   ```json
   {
     "code": 200,
     "message": "操作成功",
     "data": { ... }
   }
   ```

4. **前后端交互流程**：
   - 前端通过Axios发送HTTP请求
   - 请求经过路由分发到对应控制器
   - 控制器处理请求并返回JSON响应
   - 前端解析响应并更新UI

5. **认证机制**：
   - 基于JWT（JSON Web Token）的认证
   - Token存储在localStorage中
   - 请求通过Authorization头部传递Token
