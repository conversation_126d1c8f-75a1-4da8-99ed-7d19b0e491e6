<?php

namespace app\controller\finance;

use app\BaseController;
use think\Request;
use think\Validate;
use think\facade\Db;
use app\model\Station;
use think\facade\Validate as FacadeValidate;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Village extends BaseController
{
    /**
     * 获取村集体打款列表
     */
    public function getList()
    {
        $page = input('page', 1, 'intval');
        $pageSize = input('pageSize', 10, 'intval');
        $county = input('county', '', 'trim');
        $township = input('township', '', 'trim');
        $village = input('village', '', 'trim');
        $projectYear = input('project_year', '', 'intval');
        $paymentYear = input('payment_year', '', 'intval');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $paymentStatus = input('payment_status', '', 'trim');

        $where = [];
        
        // 构建查询条件
        if (!empty($county)) {
            $where[] = ['a.county', 'like', "%{$county}%"];
        }
        
        if (!empty($township)) {
            $where[] = ['a.township', 'like', "%{$township}%"];
        }
        
        if (!empty($village)) {
            $where[] = ['a.village', 'like', "%{$village}%"];
        }
        
        if (!empty($projectYear)) {
            $where[] = ['a.project_year', '=', $projectYear];
        }
        
        if (!empty($paymentYear)) {
            $where[] = ['a.payment_year', '=', $paymentYear];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.payment_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.payment_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.payment_date', '<=', $endDate];
        }
        
        if (!empty($paymentStatus)) {
            $where[] = ['a.payment_status', '=', $paymentStatus];
        }
        
        try {
            // 获取数据总数
            $total = Db::name('fund_village')
                ->alias('a')
                ->where($where)
                ->count();
            
            // 查询数据
            $list = Db::name('fund_village')
                ->alias('a')
                ->where($where)
                ->field('a.*')
                ->order('a.township, a.village, a.id DESC')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return $this->success([
                'items' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取村集体打款列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加村集体打款记录
     */
    public function save()
    {
        $params = $this->request->post();
        
        // 确保金额为浮点数类型
        if (isset($params['amount'])) {
            $params['amount'] = (float)$params['amount'];
        }
        
        // 参数验证
        $validate = FacadeValidate::rule([
            'township' => 'require',
            'village' => 'require',
            'amount' => 'require|float',
            'payment_date' => 'require|date'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 准备数据
            $data = [
                'township' => $params['township'],
                'village' => $params['village'],
                'amount' => $params['amount'],
                'payment_date' => $params['payment_date'],
                'payment_status' => isset($params['payment_status']) ? $params['payment_status'] : '',
                'project_year' => isset($params['project_year']) ? $params['project_year'] : date('Y'),
                'payment_year' => isset($params['payment_year']) ? $params['payment_year'] : date('Y'),
                'county' => isset($params['county']) ? $params['county'] : '平邑县',
                'recipient' => isset($params['recipient']) ? $params['recipient'] : $params['village'],
                'bank_account' => isset($params['bank_account']) ? $params['bank_account'] : '',
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            // 添加记录
            $id = Db::name('fund_village')->insertGetId($data);
            
            return $this->success(['id' => $id], '添加成功');
        } catch (\Exception $e) {
            return $this->error('添加失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新村集体打款记录
     */
    public function update($id)
    {
        $params = $this->request->put();
        
        // 确保金额为浮点数类型
        if (isset($params['amount'])) {
            $params['amount'] = (float)$params['amount'];
        }
        
        // 参数验证
        $validate = FacadeValidate::rule([
            'township' => 'require',
            'village' => 'require',
            'amount' => 'require|float',
            'payment_date' => 'require|date'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 检查记录是否存在
            $record = Db::name('fund_village')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 准备数据
            $data = [
                'township' => $params['township'],
                'village' => $params['village'],
                'amount' => $params['amount'],
                'payment_date' => $params['payment_date'],
                'payment_status' => isset($params['payment_status']) ? $params['payment_status'] : $record['payment_status'],
                'project_year' => isset($params['project_year']) ? $params['project_year'] : $record['project_year'],
                'payment_year' => isset($params['payment_year']) ? $params['payment_year'] : $record['payment_year'],
                'county' => isset($params['county']) ? $params['county'] : $record['county'],
                'recipient' => isset($params['recipient']) ? $params['recipient'] : $record['recipient'],
                'bank_account' => isset($params['bank_account']) ? $params['bank_account'] : $record['bank_account'],
                'remark' => isset($params['remark']) ? $params['remark'] : $record['remark'],
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 更新记录
            Db::name('fund_village')->where('id', $id)->update($data);
            
            return $this->success('更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除村集体打款记录
     */
    public function delete($id)
    {
        try {
            // 检查记录是否存在
            $record = Db::name('fund_village')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 删除记录
            Db::name('fund_village')->where('id', $id)->delete();
            
            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 导入村集体打款数据
     */
    public function import()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请上传文件');
        }
        
        // 验证文件类型
        $fileExt = $file->getOriginalExtension();
        if (!in_array($fileExt, ['xlsx', 'xls'])) {
            return $this->error('仅支持 Excel 文件导入');
        }
        
        try {
            // 获取文件内容
            $spreadsheet = IOFactory::load($file->getPathname());
            
            // 导入结果统计
            $success = 0;
            $fail = 0;
            $duplicate = 0;
            $errors = [];
            
            Db::startTrans();
            
            // 获取所有工作表
            $worksheetCount = $spreadsheet->getSheetCount();
            
            for ($sheetIndex = 0; $sheetIndex < $worksheetCount; $sheetIndex++) {
                $worksheet = $spreadsheet->getSheet($sheetIndex);
                $sheetName = $worksheet->getTitle();
                $rows = $worksheet->toArray();
                
                // 至少需要有标题行和字段名称行，再加上至少一行数据
                if (count($rows) < 3) {
                    $errors[] = "工作表 '{$sheetName}' 内容不足，Excel文件至少需要包含标题行、字段名行和数据行";
                    continue;
                }
                
                // 第一行是标题，例如: 2019年项目平邑县光伏项目村集体收益（2024年度）
                $title = $rows[0][0] ?? '';
                
                // 记录原始标题用于调试
                $originalTitle = $title;
                
                // 解析标题中的项目年度、所在县和发放年度
                $projectYear = null;
                $county = null;
                $paymentYear = null;
                
                // 增强正则表达式，支持不同格式的标题
                if (preg_match('/(\d{4})年项目(.*?)光伏项目村集体收益(?:明细表)?（(\d{4})年度）/', $title, $matches)) {
                    $projectYear = $matches[1] ?? null;
                    $county = $matches[2] ?? null;
                    $paymentYear = $matches[3] ?? null;
                } 
                // 再尝试一种更通用的格式
                elseif (preg_match('/(\d{4})年.*?项目.*?([\u4e00-\u9fa5]{2,4}县).*?(?:收益|明细).*?（(\d{4})年/', $title, $matches)) {
                    $projectYear = $matches[1] ?? null;
                    $county = $matches[2] ?? null;
                    $paymentYear = $matches[3] ?? null;
                }
                // 最后尝试提取年份和县名的通用方法
                elseif (preg_match('/(\d{4})年.*?([\u4e00-\u9fa5]{2,4}县).*?(\d{4})年/', $title, $matches)) {
                    $projectYear = $matches[1] ?? null;
                    $county = $matches[2] ?? null;
                    $paymentYear = $matches[3] ?? null;
                }
                // 手动尝试提取信息（备用方案）
                else {
                    // 提取第一个年份作为项目年度
                    if (preg_match('/(\d{4})年/', $title, $yearMatches)) {
                        $projectYear = $yearMatches[1];
                    }
                    
                    // 提取县名
                    if (preg_match('/([\u4e00-\u9fa5]{2,4}县)/', $title, $countyMatches)) {
                        $county = $countyMatches[1];
                    } elseif ($sheetName && preg_match('/([\u4e00-\u9fa5]{2,4}(?:县|市))/', $sheetName, $sheetNameMatches)) {
                        // 如果标题中没有县名，尝试从工作表名称中提取
                        $county = $sheetNameMatches[1];
                    }
                    
                    // 提取最后一个年份作为发放年度
                    if (preg_match_all('/(\d{4})年/', $title, $allYearMatches)) {
                        $allYears = $allYearMatches[1];
                        if (count($allYears) > 1) {
                            $paymentYear = end($allYears);
                        } else {
                            // 如果只有一个年份，则假设是当前年度
                            $paymentYear = date('Y');
                        }
                    }
                }
                
                // 如果仍然无法获取必要信息，且工作表名称看起来像县名，则尝试使用默认值
                if ((!$projectYear || !$county || !$paymentYear) && 
                    preg_match('/([\u4e00-\u9fa5]{2,4}(?:县|市))/', $sheetName, $sheetNameMatches)) {
                    
                    if (!$projectYear) {
                        // 默认使用标题中的年份，或者当前年份减1作为项目年度
                        $projectYear = $projectYear ?: (date('Y') - 1);
                    }
                    
                    if (!$county) {
                        $county = $sheetNameMatches[1];
                    }
                    
                    if (!$paymentYear) {
                        // 默认使用当前年份作为发放年度
                        $paymentYear = date('Y');
                    }
                }
                
                if (!$projectYear || !$county || !$paymentYear) {
                    $errors[] = "工作表 '{$sheetName}' 标题格式不正确，无法解析项目年度、所在县或发放年度。原始标题: '{$originalTitle}'";
                    continue;
                }
                
                // 去除县名中可能存在的多余空格
                $county = trim($county);
                
                // 第二行是字段名称
                $headers = $rows[1];
                
                // 检查必要的字段是否存在
                $requiredFields = ['乡镇', '村居', '村集体收益（元）'];
                $missingFields = [];
                
                foreach ($requiredFields as $field) {
                    if (!in_array($field, $headers)) {
                        $missingFields[] = $field;
                    }
                }
                
                if (!empty($missingFields)) {
                    $errors[] = "工作表 '{$sheetName}' 缺少必要字段：" . implode(', ', $missingFields);
                    continue;
                }
                
                // 构建字段映射
                $fieldMap = [];
                foreach ($headers as $index => $header) {
                    if ($header == '乡镇') {
                        $fieldMap['township'] = $index;
                    } elseif ($header == '村居') {
                        $fieldMap['village'] = $index;
                    } elseif ($header == '村集体收益（元）') {
                        $fieldMap['amount'] = $index;
                    } elseif ($header == '发放状态') {
                        $fieldMap['payment_status'] = $index;
                    }
                }
                
                // 移除标题行和字段行
                array_shift($rows);
                array_shift($rows);
                
                // 跟踪当前乡镇，用于处理跨行情况
                $currentTownship = '';
                
                foreach ($rows as $index => $row) {
                    // 跳过空行
                    if (empty(array_filter($row))) {
                        continue;
                    }
                    
                    // 获取字段值并处理乡镇跨行情况
                    $township = trim($row[$fieldMap['township']] ?? '');
                    
                    // 如果乡镇为空，使用前一个乡镇值（处理跨行的情况）
                    if (empty($township)) {
                        $township = $currentTownship;
                    } else {
                        // 否则更新当前乡镇
                        $currentTownship = $township;
                    }
                    
                    $village = trim($row[$fieldMap['village']] ?? '');
                    $amount = trim($row[$fieldMap['amount']] ?? '');
                    $paymentStatus = isset($fieldMap['payment_status']) ? trim($row[$fieldMap['payment_status']] ?? '') : '';
                    
                    // 数据验证和处理
                    // 村居和金额是必填的，乡镇可能因为合并单元格不在每行出现，但应该已经通过上面的逻辑处理
                    if (empty($township) || empty($village) || !is_numeric($amount)) {
                        $fail++;
                        $errors[] = "工作表 '{$sheetName}' 第" . ($index + 3) . "行数据不完整或格式错误";
                        continue;
                    }
                    
                    try {
                        // 准备数据
                        $data = [
                            'township' => $township,
                            'village' => $village,
                            'amount' => floatval($amount),
                            'payment_status' => $paymentStatus,
                            'project_year' => intval($projectYear),
                            'payment_year' => intval($paymentYear),
                            'county' => $county,
                            'payment_date' => date('Y-m-d'), // 默认使用当前日期
                            'create_time' => date('Y-m-d H:i:s'),
                            'recipient' => $village, // 默认收款方为村名
                            'bank_account' => '' // 银行账户为必填字段，需要后续补充
                        ];
                        
                        // 检查是否存在相同记录
                        $existingRecord = Db::name('fund_village')
                            ->where([
                                ['payment_year', '=', intval($paymentYear)],
                                ['county', '=', $county],
                                ['township', '=', $township],
                                ['village', '=', $village],
                                ['amount', '=', floatval($amount)]
                            ])
                            ->find();
                        
                        if ($existingRecord) {
                            // 记录已存在，跳过
                            $duplicate++;
                            $errors[] = "工作表 '{$sheetName}' 第" . ($index + 3) . "行: 记录已存在，跳过导入";
                            continue;
                        }
                        
                        // 插入记录
                        Db::name('fund_village')->insert($data);
                        
                        $success++;
                    } catch (\Exception $e) {
                        $fail++;
                        $errors[] = "工作表 '{$sheetName}' 第" . ($index + 3) . "行: " . $e->getMessage();
                    }
                }
            }
            
            if ($success > 0) {
                Db::commit();
                return $this->success([
                    'success' => $success,
                    'fail' => $fail,
                    'duplicate' => $duplicate,
                    'errors' => $errors
                ], '导入完成');
            } else {
                Db::rollback();
                return $this->error('导入失败，没有成功导入的数据: ' . implode('; ', $errors));
            }
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导出村集体打款数据
     */
    public function export()
    {
        $township = input('township', '', 'trim');
        $village = input('village', '', 'trim');
        $projectYear = input('project_year', '', 'intval');
        $paymentYear = input('payment_year', '', 'intval');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $county = input('county', '', 'trim');
        $paymentStatus = input('payment_status', '', 'trim');
        
        $where = [];
        
        // 构建查询条件
        if (!empty($township)) {
            $where[] = ['a.township', 'like', "%{$township}%"];
        }
        
        if (!empty($village)) {
            $where[] = ['a.village', 'like', "%{$village}%"];
        }
        
        if (!empty($projectYear)) {
            $where[] = ['a.project_year', '=', $projectYear];
        }
        
        if (!empty($paymentYear)) {
            $where[] = ['a.payment_year', '=', $paymentYear];
        }
        
        if (!empty($county)) {
            $where[] = ['a.county', 'like', "%{$county}%"];
        }
        
        if (!empty($paymentStatus)) {
            $where[] = ['a.payment_status', '=', $paymentStatus];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.payment_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.payment_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.payment_date', '<=', $endDate];
        }
        
        try {
            // 查询数据
            $list = Db::name('fund_village')
                ->alias('a')
                ->where($where)
                ->field('a.*')
                ->order('a.township, a.village, a.id')
                ->select()
                ->toArray();
            
            if (empty($list)) {
                return $this->error('没有数据可导出');
            }
            
            // 获取导出文件的项目年度和发放年度
            $exportProjectYear = $projectYear ?: ($list[0]['project_year'] ?? date('Y'));
            $exportPaymentYear = $paymentYear ?: ($list[0]['payment_year'] ?? date('Y'));
            $exportCounty = $list[0]['county'] ?? '平邑县'; // 默认使用平邑县
            
            // 创建电子表格
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置标题行（第一行）
            $titleText = $exportProjectYear . '年项目' . $exportCounty . '光伏项目村集体收益（' . $exportPaymentYear . '年度）';
            $sheet->setCellValue('A1', $titleText);
            // 合并标题单元格
            $sheet->mergeCells('A1:F1');
            // 设置标题样式
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // 设置字段名（第二行）
            $sheet->setCellValue('A2', '序号');
            $sheet->setCellValue('B2', '乡镇');
            $sheet->setCellValue('C2', '序号');
            $sheet->setCellValue('D2', '村居');
            $sheet->setCellValue('E2', '村集体收益（元）');
            $sheet->setCellValue('F2', '发放状态');
            
            // 给字段行添加粗体样式
            $sheet->getStyle('A2:F2')->getFont()->setBold(true);
            
            // 填充数据并处理乡镇合并
            $row = 3;
            $seqNo = 1;
            $townshipData = [];
            
            // 先根据乡镇分组处理数据
            foreach ($list as $item) {
                if (!isset($townshipData[$item['township']])) {
                    $townshipData[$item['township']] = [
                        'villages' => [],
                        'seqNo' => $seqNo++
                    ];
                }
                
                $townshipData[$item['township']]['villages'][] = [
                    'village' => $item['village'],
                    'amount' => $item['amount'],
                    'payment_status' => $item['payment_status']
                ];
            }
            
            // 然后输出到Excel
            foreach ($townshipData as $township => $data) {
                $townshipStartRow = $row;
                $innerSeqNo = 1;
                
                // 设置乡镇名称和总序号，只在该乡镇的第一行设置
                $sheet->setCellValue('A' . $townshipStartRow, $data['seqNo']);
                $sheet->setCellValue('B' . $townshipStartRow, $township);
                
                // 填充该乡镇下的所有村数据
                foreach ($data['villages'] as $villageData) {
                    $sheet->setCellValue('C' . $row, $innerSeqNo++);
                    $sheet->setCellValue('D' . $row, $villageData['village']);
                    $sheet->setCellValue('E' . $row, $villageData['amount']);
                    $sheet->setCellValue('F' . $row, $villageData['payment_status']);
                    $row++;
                }
                
                // 合并乡镇单元格
                $villageCount = count($data['villages']);
                if ($villageCount > 1) {
                    $sheet->mergeCells('A' . $townshipStartRow . ':A' . ($row - 1));
                    $sheet->mergeCells('B' . $townshipStartRow . ':B' . ($row - 1));
                }
            }
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(10);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(10);
            $sheet->getColumnDimension('D')->setWidth(25);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            
            // 设置单元格边框
            $styleArray = [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];
            $sheet->getStyle('A1:F' . ($row - 1))->applyFromArray($styleArray);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="村集体收益数据_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取导入模板
     */
    public function template()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置标题行（第一行）
            $currentYear = date('Y');
            $titleText = '2019年项目平邑县光伏项目村集体收益（' . $currentYear . '年度）';
            $sheet->setCellValue('A1', $titleText);
            // 合并标题单元格
            $sheet->mergeCells('A1:F1');
            // 设置标题样式
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // 设置字段名（第二行）
            $sheet->setCellValue('A2', '序号');
            $sheet->setCellValue('B2', '乡镇');
            $sheet->setCellValue('C2', '序号');
            $sheet->setCellValue('D2', '村居');
            $sheet->setCellValue('E2', '村集体收益（元）');
            $sheet->setCellValue('F2', '发放状态');
            
            // 给字段行添加粗体样式
            $sheet->getStyle('A2:F2')->getFont()->setBold(true);
            
            // 添加一些示例数据
            // 第一个乡镇示例 - 卞桥镇（3个村）
            $sheet->setCellValue('A3', '1');
            $sheet->setCellValue('B3', '卞桥镇');
            $sheet->setCellValue('C3', '1');
            $sheet->setCellValue('D3', '平邑县卞桥镇北官庄村');
            $sheet->setCellValue('E3', '8490.56');
            $sheet->setCellValue('F3', '未发放');
            
            $sheet->setCellValue('C4', '2');
            $sheet->setCellValue('D4', '平邑县卞桥镇卜家湖');
            $sheet->setCellValue('E4', '18109.93');
            $sheet->setCellValue('F4', '未发放');
            
            $sheet->setCellValue('C5', '3');
            $sheet->setCellValue('D5', '平邑县卞桥镇陈家湖');
            $sheet->setCellValue('E5', '22487.32');
            $sheet->setCellValue('F5', '未发放');
            
            // 合并乡镇单元格
            $sheet->mergeCells('A3:A5');
            $sheet->mergeCells('B3:B5');
            
            // 第二个乡镇示例 - 白彦镇（2个村）
            $sheet->setCellValue('A6', '2');
            $sheet->setCellValue('B6', '白彦镇');
            $sheet->setCellValue('C6', '1');
            $sheet->setCellValue('D6', '白彦镇高庄（柴山湾村）');
            $sheet->setCellValue('E6', '3564.32');
            $sheet->setCellValue('F6', '未发放');
            
            $sheet->setCellValue('C7', '2');
            $sheet->setCellValue('D7', '白彦镇槐树沟村');
            $sheet->setCellValue('E7', '9782.51');
            $sheet->setCellValue('F7', '已发放');
            
            // 合并乡镇单元格
            $sheet->mergeCells('A6:A7');
            $sheet->mergeCells('B6:B7');
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(10);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(10);
            $sheet->getColumnDimension('D')->setWidth(25);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            
            // 设置单元格边框
            $styleArray = [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];
            $sheet->getStyle('A1:F7')->applyFromArray($styleArray);
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="村集体收益导入模板.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('获取模板失败: ' . $e->getMessage());
        }
    }
} 