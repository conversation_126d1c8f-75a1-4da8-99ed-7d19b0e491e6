<?php
declare (strict_types = 1);
namespace app\service\ticket;

use think\facade\Db;
use app\service\BaseService;
use think\facade\Log;

class DetailService extends BaseService
{
    /**
     * 获取工单详情
     * @param int $id 工单ID
     * @param int $userId 当前用户ID
     * @param bool $isAdmin 是否为管理员
     * @return array
     */
    public function getDetail($id, $userId, $isAdmin = false)
    {
        // 获取工单基本信息
        $ticket = Db::name('ticket')
            ->where('id', $id)
            ->find();
            
        if (!$ticket) {
            return [
                'code' => 404,
                'message' => '工单不存在',
                'data' => null
            ];
        }
        
        // 检查访问权限
        Log::debug('检查访问权限，工单ID: ' . $id . ', 当前用户ID: ' . $userId . ', 是否为管理员: ' . $isAdmin . '，工单创建者ID: ' . $ticket['creator_id']);
        if (!$isAdmin && $ticket['creator_id'] != $userId) {
            // 如果不是管理员且不是创建者，检查是否为处理人
            $isHandler = Db::name('ticket_handler')
                ->where('ticket_id', $id)
                ->where('handler_id', $userId)
                ->count() > 0;
                
            if (!$isHandler) {
                return [
                    'code' => 403,
                    'message' => '无权访问此工单',
                    'data' => null
                ];
            }
        }
        
        // 获取分类信息
        if (!empty($ticket['category_id'])) {
            $category = Db::name('ticket_category')
                ->where('id', $ticket['category_id'])
                ->field('id, name, code, description')
                ->find();
            $ticket['category'] = $category;
        } else {
            $ticket['category'] = null;
        }
        
        // 获取创建者信息
        if (!empty($ticket['creator_id'])) {
            $creator = Db::name('user')
                ->alias('u')
                ->join('department d', 'u.department_id = d.id', 'LEFT')
                ->where('u.id', $ticket['creator_id'])
                ->field('u.id, u.username, u.realname, d.name as department_name')
                ->find();
            $ticket['creator'] = $creator;
        } else {
            $ticket['creator'] = null;
        }
        
        // 获取部门信息
        if (!empty($ticket['department_id'])) {
            $department = Db::name('department')
                ->where('id', $ticket['department_id'])
                ->field('id, name, parent_id')
                ->find();
            $ticket['department'] = $department;
        } else {
            $ticket['department'] = null;
        }
        
        // 获取处理人信息
        $handlers = Db::name('ticket_handler')
            ->alias('th')
            ->join('user u', 'th.handler_id = u.id', 'LEFT')
            ->join('department d', 'u.department_id = d.id', 'LEFT')
            ->where('th.ticket_id', $id)
            ->field([
                'th.*',
                'u.username', 'u.realname',
                'd.name as department_name'
            ])
            ->select()
            ->toArray();
            
        // 为每个处理人增加状态文本和颜色
        foreach ($handlers as &$handler) {
            switch ($handler['status']) {
                case -1:
                    $handler['status_text'] = '已拒绝';
                    $handler['status_color'] = 'danger';
                    break;
                case 0:
                    $handler['status_text'] = '待处理';
                    $handler['status_color'] = 'info';
                    break;
                case 1:
                    $handler['status_text'] = '处理中';
                    $handler['status_color'] = 'warning';
                    break;
                case 2:
                    $handler['status_text'] = '已完成';
                    $handler['status_color'] = 'success';
                    break;
                default:
                    $handler['status_text'] = '未知';
                    $handler['status_color'] = 'default';
            }
            
            // 优化显示名称
            $handler['display_name'] = !empty($handler['realname']) ? $handler['realname'] : $handler['username'];
            
            // 添加部门信息到显示名称
            if (!empty($handler['department_name'])) {
                $handler['display_name'] .= ' (' . $handler['department_name'] . ')';
            }
        }
        
        $ticket['handlers'] = $handlers;
        
        // 获取标签
        $tags = Db::name('ticket_tag_relation')
            ->alias('r')
            ->join('ticket_tag t', 'r.tag_id = t.id')
            ->where('r.ticket_id', $id)
            ->field('t.id, t.name, t.color')
            ->select()
            ->toArray();
        $ticket['tags'] = $tags;
        
        // 获取附件
        trace('开始获取工单附件，工单ID: ' . $id, 'debug');
        $attachments = Db::name('ticket_attachment')
            ->alias('ta')
            ->leftJoin('user u', 'ta.handler_id = u.id')
            ->where('ta.ticket_id', $id)
            ->field('ta.id, ta.ticket_id, ta.name, ta.original_name, ta.path, ta.type, ta.size, ta.create_at, ta.handler_id, u.realname as handler_name, u.username')
            ->select()
            ->toArray();
            
        trace('获取到附件数量: ' . count($attachments), 'debug');
        if (!empty($attachments)) {
            trace('第一个附件信息: ' . json_encode($attachments[0], JSON_UNESCAPED_UNICODE), 'debug');
        } else {
            trace('没有找到附件', 'debug');
            // 检查是否有附件记录
            $count = Db::name('ticket_attachment')->where('ticket_id', $id)->count();
            trace('附件记录数: ' . $count, 'debug');
        }
            
        foreach ($attachments as &$attachment) {
            // 确保下载URL正确
            $attachment['download_url'] = url('ticket_new/download', ['id' => $attachment['id']])->build();
            // 格式化文件大小
            $attachment['filesize_formatted'] = $this->formatFileSize($attachment['size']);
            // 确保文件名存在
            if (empty($attachment['name']) && !empty($attachment['path'])) {
                $pathParts = explode('/', $attachment['path']);
                $attachment['name'] = end($pathParts);
            }
            // 处理handler_name
            if (empty($attachment['handler_name']) && !empty($attachment['username'])) {
                $attachment['handler_name'] = $attachment['username'];
            } elseif (empty($attachment['handler_name'])) {
                $attachment['handler_name'] = '未知用户';
            }
        }
        
        $ticket['attachments'] = $attachments;
        
        // 构建工单时间线
        $timeline = $this->buildTimeline($id, $ticket, $handlers);
        $ticket['timeline'] = $timeline;
        
        return [
            'code' => 200,
            'message' => '获取成功',
            'data' => $ticket
        ];
    }
    
    /**
     * 构建工单时间线
     * @param int $id 工单ID
     * @param array $ticket 工单信息
     * @param array $handlers 处理人信息
     * @return array
     */
    private function buildTimeline($id, $ticket, $handlers)
    {
        $timeline = [];
        
        // 创建时间
        $creatorName = '';
        if (isset($ticket['creator']) && !empty($ticket['creator'])) {
            $creatorName = !empty($ticket['creator']['realname']) ? $ticket['creator']['realname'] : $ticket['creator']['username'];
        } else {
            $creatorName = $ticket['creator_name'] ?? '未知用户';
        }
        
        $timeline[] = [
            'id' => 'create',
            'time' => $ticket['create_at'],
            'title' => '创建工单',
            'content' => "由 {$creatorName} 创建工单",
            'color' => 'primary'
        ];
        
        // 处理人时间线
        foreach ($handlers as $handler) {
            $handlerName = $handler['display_name'];
            
            // 分配处理人
            $handlerTime = $handler['create_at'];
            $timeline[] = [
                'id' => 'assign_' . $handler['id'],
                'time' => $handlerTime,
                'title' => '分配处理人',
                'content' => "工单分配给 {$handlerName} 处理",
                'color' => 'info'
            ];
            
            // 处理人接受或拒绝
            if ($handler['status'] == -1) {
                // 拒绝
                $timeline[] = [
                    'id' => 'reject_' . $handler['id'],
                    'time' => $handler['update_at'],
                    'title' => '拒绝处理',
                    'content' => "{$handlerName} 拒绝处理此工单，原因：" . ($handler['reject_reason'] ?? '无'),
                    'color' => 'danger'
                ];
            } else if ($handler['status'] >= 1) {
                // 接受
                $timeline[] = [
                    'id' => 'accept_' . $handler['id'],
                    'time' => $handler['update_at'],
                    'title' => '接受处理',
                    'content' => "{$handlerName} 接受处理此工单",
                    'color' => 'success'
                ];
                
                // 已完成
                if ($handler['status'] == 2) {
                    $timeline[] = [
                        'id' => 'complete_' . $handler['id'],
                        'time' => $handler['complete_at'],
                        'title' => '处理完成',
                        'content' => "{$handlerName} 已完成工单处理",
                        'color' => 'success'
                    ];
                }
            }
        }
        
        // 查询转派记录
        $transfers = Db::name('ticket_transfer')
            ->alias('t')
            ->join('user fu', 't.from_user_id = fu.id', 'LEFT')
            ->join('user tu', 't.to_user_id = tu.id', 'LEFT')
            ->where('t.ticket_id', $id)
            ->field([
                't.*',
                'fu.username as from_username', 'fu.realname as from_realname',
                'tu.username as to_username', 'tu.realname as to_realname'
            ])
            ->order('t.create_at', 'asc')
            ->select()
            ->toArray();
            
        foreach ($transfers as $transfer) {
            $fromName = !empty($transfer['from_realname']) ? $transfer['from_realname'] : $transfer['from_username'];
            $toName = !empty($transfer['to_realname']) ? $transfer['to_realname'] : $transfer['to_username'];
            
            $timeline[] = [
                'id' => 'transfer_' . $transfer['id'],
                'time' => $transfer['create_at'],
                'title' => '工单转派',
                'content' => "工单由 {$fromName} 转派给 {$toName}，原因：{$transfer['remark']}",
                'color' => 'warning'
            ];
        }
        
        // 工单完成
        if ($ticket['status'] == 1 && !empty($ticket['completed_at'])) {
            $timeline[] = [
                'id' => 'complete',
                'time' => $ticket['completed_at'],
                'title' => '工单完成',
                'content' => "工单已完成处理",
                'color' => 'success'
            ];
        }
        
        // 按时间排序
        usort($timeline, function($a, $b) {
            return strtotime($a['time']) - strtotime($b['time']);
        });
        
        return $timeline;
    }
    
    /**
     * 格式化文件大小
     * @param int $size 文件大小（字节）
     * @return string
     */
    private function formatFileSize($size)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $i = 0;
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        
        return round($size, 2) . ' ' . $units[$i];
    }
} 