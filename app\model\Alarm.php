<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Alarm extends Model
{
    protected $name = 'alarm';
    protected $prefix = 'sp_';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    // 关联电站
    public function station()
    {
        return $this->belongsTo(Station::class, 'station_id', 'id');
    }

    // 关联处理人
    public function handleUser()
    {
        return $this->belongsTo(User::class, 'handle_user_id', 'id');
    }
} 