<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\UserService;
use app\service\LogService;
use think\facade\Request;
use think\App;
use think\facade\Db;
use think\facade\Cache;

class User extends BaseController
{
    protected $userService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->userService = new UserService();
    }

    /**
     * 获取用户列表
     */
    public function index()
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }
        try {
            // 获取部门ID参数
            $departmentId = input('department_id/d', 0);
            
            // 如果指定了部门ID，先检查部门是否存在
            if ($departmentId > 0) {
                $department = Db::name('department')->where('id', $departmentId)->find();
                if (!$department) {
                    // 部门不存在，返回空数据列表而不是错误
                    trace('部门不存在，返回空数据列表: department_id=' . $departmentId, 'debug');
                    return json([
                        'code' => 200,
                        'message' => 'success',
                        'data' => [
                            'items' => [],
                            'total' => 0
                        ]
                    ]);
                }
            }
            
            // 使用UserService获取用户列表，确保包含角色信息
            $result = $this->userService->getList([
                'page' => input('page/d', 1),
                'limit' => input('limit/d', 20),
                'keyword' => input('keyword/s', ''),
                'department_id' => $departmentId
            ]);
            
            return json($result);
        } catch (\Exception $e) {
            trace('获取用户列表失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500, // 使用500错误码以明确指示服务器错误
                'message' => '获取用户列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建用户
     */
    public function create()
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $params = Request::post();
        $result = $this->userService->create($params);
        
        if ($result['code'] === 200) {
            // 记录操作日志，只记录非敏感信息
            $logData = array_diff_key($params, ['password' => '']);
            if (isset($result['data']) && isset($result['data']['id'])) {
                $logData['id'] = $result['data']['id'];
            }
            LogService::recordOperation('user', 'create', $logData);
        }
        
        return json($result);
    }

    /**
     * 更新用户
     */
    public function update($id)
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $params = Request::put();
        $result = $this->userService->update($id, $params);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('user', 'update', array_merge(
                ['id' => $id],
                array_diff_key($params, ['password' => ''])
            ));
        }
        
        return json($result);
    }

    /**
     * 删除用户
     */
    public function delete($id)
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $result = $this->userService->delete($id);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('user', 'delete', ['id' => $id]);
        }
        
        return json($result);
    }

    /**
     * 更改用户状态
     */
    public function changeStatus($id)
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $status = Request::put('status');
        $result = $this->userService->changeStatus($id, $status);
        return json($result);
    }

    /**
     * 分配角色
     */
    public function assignRoles($id)
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $roleIds = Request::post('role_ids');
        if (!is_array($roleIds)) {
            return $this->error('参数错误');
        }

        $result = $this->userService->assignRoles($id, $roleIds);
        return json($result);
    }

    /**
     * 重置密码
     */
    public function resetPassword($id)
    {
        if (!$this->isLogin()) {
            return $this->error('未登录', 401);
        }

        $result = $this->userService->resetPassword($id);
        return json($result);
    }

    /**
     * 获取可选的处理人列表
     */
    public function handlers()
    {
        try {
            // 获取有处理工单权限的用户
            $users = $this->userService->getHandlers();
            return $this->success($users);
        } catch (\Exception $e) {
            return $this->error('获取处理人列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    public function read($id)
    {
        try {
            // 添加请求缓存，提高响应速度
            $cacheKey = 'user_detail_' . $id;
            $cache = cache($cacheKey);
            
            if ($cache) {
                return $this->success($cache);
            }
            
            $user = $this->userService->detail($id);
            if (!$user) {
                return $this->error('用户不存在');
            }
            
            // 缓存用户信息（10分钟）
            cache($cacheKey, $user, 600);
            
            return $this->success($user);
        } catch (\Exception $e) {
            return $this->error('获取用户详情失败：' . $e->getMessage());
        }
    }

    /**
     * 获取部门用户树形结构
     */
    public function departmentUserTree()
    {
        try {
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 开始获取部门用户树', 'debug');
            
            // 1. 获取所有部门
            $departments = Db::name('department')
                ->where('status', 1)
                ->field(['id', 'name', 'parent_id', 'sort'])
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
            
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 查询到部门数量: ' . count($departments), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 部门数据: ' . json_encode($departments), 'debug');

            // 2. 获取所有启用的用户
            $users = Db::name('user')
                ->alias('u')
                ->join('department d', 'u.department_id = d.id', 'LEFT')
                ->where('u.status', 1)
                ->field([
                    'u.id',
                    'u.username',
                    'u.realname',
                    'u.department_id',
                    'd.name as department_name'
                ])
                ->select()
                ->toArray();
            
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 查询到用户数量: ' . count($users), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 用户数据: ' . json_encode($users), 'debug');

            // 3. 构建树形结构
            $tree = [];
            foreach ($departments as $dept) {
                if (!$dept['parent_id']) {
                    trace(__FILE__ . ':' . __LINE__ . ' [用户树] 处理顶级部门: ' . $dept['name'], 'debug');
                    $node = [
                        'id' => 'dept_' . $dept['id'],
                        'label' => $dept['name'],
                        'type' => 'department',
                        'disabled' => true,
                        'children' => $this->buildDepartmentUserTree($departments, $users, $dept['id'])
                    ];
                    if (!empty($node['children'])) {
                        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 添加部门节点: ' . $dept['name'] . 
                              ', 子节点数量: ' . count($node['children']), 'debug');
                        $tree[] = $node;
                    } else {
                        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 跳过空部门: ' . $dept['name'], 'debug');
                    }
                }
            }

            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 最终树形结构节点数量: ' . count($tree), 'debug');
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 返回的树形结构: ' . json_encode($tree), 'debug');
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 获取部门用户树失败: ' . $e->getMessage() . 
                  "\n" . $e->getTraceAsString(), 'error');
            return json([
                'code' => 500,
                'message' => '获取部门用户树失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 构建部门用户树
     */
    protected function buildDepartmentUserTree($departments, $users, $parentId)
    {
        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 开始构建部门ID ' . $parentId . ' 的子树', 'debug');
        $tree = [];
        
        // 1. 添加该部门下的用户
        $departmentUsers = array_filter($users, function($user) use ($parentId) {
            return $user['department_id'] == $parentId;
        });
        
        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 部门 ' . $parentId . ' 下的用户数量: ' . 
              count($departmentUsers), 'debug');
        
        foreach ($departmentUsers as $user) {
            $userNode = [
                'id' => $user['id'],
                'label' => $user['realname'] ?: $user['username'],
                'type' => 'user',
                'disabled' => false,
                'username' => $user['username'],
                'realname' => $user['realname'],
                'department_id' => $user['department_id']
            ];
            $tree[] = $userNode;
            trace(__FILE__ . ':' . __LINE__ . ' [用户树] 添加用户节点: ' . json_encode($userNode), 'debug');
        }
        
        // 2. 添加子部门
        $childDepartments = array_filter($departments, function($dept) use ($parentId) {
            return $dept['parent_id'] == $parentId;
        });
        
        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 部门 ' . $parentId . ' 的子部门数量: ' . 
              count($childDepartments), 'debug');
        
        foreach ($childDepartments as $dept) {
            $children = $this->buildDepartmentUserTree($departments, $users, $dept['id']);
            if (!empty($children)) {
                $deptNode = [
                    'id' => 'dept_' . $dept['id'],
                    'label' => $dept['name'],
                    'type' => 'department',
                    'disabled' => true,
                    'children' => $children
                ];
                $tree[] = $deptNode;
                trace(__FILE__ . ':' . __LINE__ . ' [用户树] 添加子部门节点: ' . $dept['name'] . 
                      ', 子节点数量: ' . count($children), 'debug');
            } else {
                trace(__FILE__ . ':' . __LINE__ . ' [用户树] 跳过空子部门: ' . $dept['name'], 'debug');
            }
        }
        
        trace(__FILE__ . ':' . __LINE__ . ' [用户树] 部门 ' . $parentId . ' 的子树构建完成，节点数量: ' . 
              count($tree), 'debug');
        return $tree;
    }

    protected function getUserRoles($userId)
    {
        try {
            return Db::name('user_role')
                ->alias('ur')
                ->join('role r', 'ur.role_id = r.id')
                ->where('ur.user_id', $userId)
                ->where('r.status', 1)
                ->column('r.name');  // 使用 column 而不是 select
        } catch (\Exception $e) {
            trace('Get user roles error: ' . $e->getMessage(), 'error');
            return [];
        }
    }
} 