<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class StationData extends Model
{
    protected $name = 'station_data';
    protected $autoWriteTimestamp = true;
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'station_id'        => 'int',
        'record_time'       => 'datetime',
        'power_generation'  => 'float',
        'power_consumption' => 'float',
        'irradiance'        => 'float',
        'temperature'       => 'float',
        'humidity'          => 'float',
        'wind_speed'        => 'float',
        'create_at'       => 'datetime',
        'update_at'       => 'datetime'
    ];
    
    // 关联电站
    public function station()
    {
        return $this->belongsTo(Station::class);
    }
} 