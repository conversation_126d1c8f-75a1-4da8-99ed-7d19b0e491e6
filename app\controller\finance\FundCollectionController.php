<?php
declare (strict_types = 1);

namespace app\controller\finance;

use app\BaseController;
use think\facade\Db;
use think\facade\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\facade\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use think\facade\Validate;

/**
 * 资金归集管理控制器
 */
class FundCollection extends BaseController
{
    /**
     * 上传并解析Excel文件
     */
    public function uploadExcel()
    {
        try {
            $file = request()->file('excel_file');
            if (!$file) {
                return json(['code' => 400, 'message' => '未接收到文件']);
            }
            
            // 检查文件类型和大小
            $validate = \think\facade\Validate::rule([
                'file' => 'fileSize:10240|fileExt:xlsx,xls'
            ]);
            
            if (!$validate->check(['file' => $file])) {
                return json(['code' => 400, 'message' => $validate->getError()]);
            }
            
            // 保存文件到临时目录
            $info = $file->move(runtime_path() . 'upload');
            if (!$info) {
                return json(['code' => 400, 'message' => '文件上传失败']);
            }
            
            // 获取文件路径
            $filepath = $info->getPathname();
            
            // 解析Excel文件
            $reader = IOFactory::createReaderForFile($filepath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($filepath);
            $sheet = $spreadsheet->getActiveSheet();
            
            // 获取数据范围
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            
            // 获取表头
            $headers = $sheet->rangeToArray('A1:' . $highestColumn . '1', null, true, false)[0];
            
            // 获取预览数据（最多5行）
            $previewData = [];
            $previewRows = min(5, $highestRow - 1);
            
            for ($row = 2; $row <= $previewRows + 1; $row++) {
                $rowData = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, null, true, false)[0];
                $parsedData = $this->parseRowData($rowData, $headers);
                $previewData[] = $parsedData;
            }
            
            Log::debug('Excel解析成功, 表头数量: ' . count($headers) . ', 总行数: ' . ($highestRow - 1));
            
            return json([
                'code' => 200,
                'message' => '文件解析成功',
                'data' => [
                    'filepath' => $filepath,
                    'total_rows' => $highestRow - 1,
                    'headers' => $headers,
                    'preview' => $previewData
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Excel解析失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '文件解析失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * @route POST finance/fund/import
     */
    public function import()
    {
        try {
            $file = request()->file('file');  // 修正文件字段名
            if (!$file) {
                return json(['code' => 400, 'message' => '请选择要上传的文件']);
            }

            // 文件验证
            $validate = [
                'file' => 'fileSize:10240|fileExt:xlsx,xls'
            ];
            if (!Validate::check(['file' => $file], $validate)) {
                return json(['code' => 400, 'message' => Validate::getError()]);
            }

            // 保存文件
            $savePath = runtime_path() . 'upload/';
            $info = $file->move($savePath);
            if (!$info) {
                return json(['code' => 500, 'message' => '文件保存失败']);
            }

            // 调用导入逻辑
            return $this->importData();

        } catch (\Exception $e) {
            Log::error('导入异常: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '导入失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 导入Excel数据
     */
    protected function importData()
    {
        Db::startTrans();
        try {
            $params = Request::post();
            $filepath = public_path() . 'storage/' . $params['filepath'];
            $year = isset($params['year']) ? intval($params['year']) : date('Y');
            $month = isset($params['month']) ? intval($params['month']) : date('n');
            
            if (!file_exists($filepath)) {
                return json(['code' => 400, 'message' => '文件不存在']);
            }
            
            // 解析Excel文件
            $reader = IOFactory::createReaderForFile($filepath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($filepath);
            $sheet = $spreadsheet->getActiveSheet();
            
            // 获取数据范围
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            
            // 获取表头
            $headers = $sheet->rangeToArray('A1:' . $highestColumn . '1', null, true, false)[0];
            
            $successCount = 0;
            $errorCount = 0;
            $errorMessages = [];
            
            // 处理每一行数据
            for ($row = 2; $row <= $highestRow; $row++) {
                try {
                    $rowData = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, null, true, false)[0];
                    $parsedData = $this->parseRowData($rowData, $headers);
                    
                    // 处理基础数据
                    $baseId = $this->saveBaseData($parsedData['base']);
                    
                    // 处理资金归集明细
                    foreach ($parsedData['details'] as $detail) {
                        if ($detail['year'] == $year && $detail['month'] == $month) {
                            $detail['base_id'] = $baseId;
                            $this->saveDetailData($detail);
                        }
                    }
                    
                    // 更新月度汇总
                    $this->updateMonthlyStats($baseId, $year, $month);
                    
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessages[] = "第{$row}行处理失败: " . $e->getMessage();
                    Log::error("导入第{$row}行失败: " . $e->getMessage());
                }
            }
            
            Db::commit();
            
            // 记录导入结果
            Log::info("导入完成: 成功{$successCount}条, 失败{$errorCount}条");
            
            $this->logOperation('import', [
                'file' => $filepath,
                'success_count' => $successCount,
                'error_count' => $errorCount
            ]);
            
            return json([
                'code' => 200,
                'message' => "导入完成，成功: {$successCount}条，失败: {$errorCount}条",
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'error_messages' => $errorMessages
                ]
            ]);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('导入失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '导入失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 解析Excel行数据
     */
    private function parseRowData($rowData, $headers)
    {
        // 初始化数据
        $baseData = [];
        $detailData = [];
        
        // 映射固定列
        $fixedFieldMapping = [
            0 => 'arc_id',           // 档案号
            1 => 'department_name',  // 县区改为department_name
            2 => 'township',         // 乡镇
            3 => 'village',          // 村名
            4 => 'install_address',  // 安装地址
            5 => 'account_name',     // 户名
            6 => 'account_number',   // 国网号
            7 => 'id_card',          // 客户身份证号
            8 => 'contact_phone',    // 联系电话
            9 => 'panel_count',      // 光伏板块数
            10 => 'product_price',   // 产品价格
            11 => 'installed_capacity', // 装机容量
            12 => 'income_calculation', // 收益计算
            13 => 'archive_status',    // 档案接收情况
            14 => 'receive_bank_card', // 接收的银行卡
            15 => 'elec_card_number',  // 电费卡卡号
            16 => 'opening_bank',      // 开户行
            17 => 'latest_card_number', // 最新卡号
            // 18是卡号尾号, 跳过
            19 => 'latest_opening_bank', // 最新开户行
        ];
        
        // 处理基础数据
        foreach ($fixedFieldMapping as $index => $field) {
            if (isset($rowData[$index])) {
                $baseData[$field] = $rowData[$index];
            }
        }
        
        // 处理末尾固定列
        $lastFixedFields = [
            80 => 'card_status',      // 电站电费卡状态
            81 => 'remarks',          // 备注
            82 => 'card_open_time',   // 开卡时间
            83 => 'state_grid_signed' // 国网签约
        ];
        
        foreach ($lastFixedFields as $index => $field) {
            if (isset($rowData[$index])) {
                $baseData[$field] = $rowData[$index];
            }
        }
        
        // 处理归集明细数据 (29-79列)
        for ($i = 29; $i <= 79; $i++) {
            if (isset($rowData[$i]) && $rowData[$i] !== '' && is_numeric($rowData[$i])) {
                $headerText = $headers[$i] ?? '';
                $detailInfo = $this->parseCollectionDetail($headerText, $rowData[$i]);
                if ($detailInfo) {
                    $detailData[] = $detailInfo;
                }
            }
        }
        
        return [
            'base' => $baseData,
            'details' => $detailData
        ];
    }
    
    /**
     * 解析归集明细信息
     */
    private function parseCollectionDetail($headerText, $amount)
    {
        // 默认值
        $result = [
            'year' => date('Y'),
            'month' => 0,
            'day' => 1,
            'type' => '其他',
            'amount' => floatval($amount)
        ];
        
        // 尝试匹配年份信息
        if (preg_match('/(20)?(\d{2})[\.\-年](\d{1,2})[\.\-月](\d{1,2})?/i', $headerText, $matches)) {
            // 完整年月日格式 (例如: 2023.5.15 或 23.5.15)
            $result['year'] = isset($matches[1]) ? intval($matches[1].$matches[2]) : intval('20'.$matches[2]);
            $result['month'] = intval($matches[3]);
            $result['day'] = isset($matches[4]) ? intval($matches[4]) : 1;
        } else if (preg_match('/(\d{1,2})[\.\-月](\d{1,2})?/i', $headerText, $matches)) {
            // 仅月日格式 (例如: 5.15)
            $result['month'] = intval($matches[1]);
            $result['day'] = isset($matches[2]) ? intval($matches[2]) : 1;
        } else if (preg_match('/(\d{1,2})月/i', $headerText, $matches)) {
            // 仅月份格式 (例如: 5月)
            $result['month'] = intval($matches[1]);
        }
        
        // 判断收款类型
        $lowerHeader = strtolower($headerText);
        if (strpos($lowerHeader, 'pos') !== false) {
            $result['type'] = 'pos';
        } else if (strpos($lowerHeader, '临商') !== false) {
            $result['type'] = '临商';
        } else if (strpos($lowerHeader, '国网') !== false) {
            $result['type'] = '国网';
        } else if (strpos($lowerHeader, '存现') !== false) {
            $result['type'] = '存现';
        }
        
        // 生成日期字符串
        $result['collection_date'] = sprintf('%04d-%02d-%02d', 
            $result['year'], $result['month'], $result['day']);
        
        return $result;
    }
    
    /**
     * 保存基础数据
     */
    private function saveBaseData($baseData)
    {
        // 确保部门字段存储到正确位置
        $validData = [
            'department_name' => $baseData['department_name'] ?? '',
            'arc_id' => $baseData['arc_id'] ?? '',
            // 其他字段映射...
        ];
        Db::name('sp_fund_collection_base')->insertGetId($validData);
    }
    
    /**
     * 保存归集明细数据
     */
    private function saveDetailData($detail)
    {
        $allowedTypes = ['pos', 'linShang', 'nationalGrid', 'cash', 'other'];
        if (!in_array($detail['collection_type'], $allowedTypes)) {
            throw new \Exception("无效的归集类型: {$detail['collection_type']}");
        }
        // 其他存储逻辑不变...
    }
    
    /**
     * 更新月度统计
     */
    private function updateMonthlyStats($baseId, $year, $month)
    {
        // 各类型金额汇总
        $summary = Db::name('sp_fund_collection_detail')
            ->where('base_id', $baseId)
            ->where('collection_year', $year)
            ->where('collection_month', $month)
            ->field('collection_type, sum(amount) as type_sum')
            ->group('collection_type')
            ->select()
            ->toArray();
        
        // 初始化金额
        $amounts = [
            'pos_amount' => 0,
            'linshang_amount' => 0,
            'guowang_amount' => 0,
            'cash_amount' => 0,
            'other_amount' => 0,
            'total_amount' => 0
        ];
        
        // 汇总各类型金额
        foreach ($summary as $item) {
            switch ($item['collection_type']) {
                case 'pos':
                    $amounts['pos_amount'] = $item['type_sum'];
                    break;
                case '临商':
                    $amounts['linshang_amount'] = $item['type_sum'];
                    break;
                case '国网':
                    $amounts['guowang_amount'] = $item['type_sum'];
                    break;
                case '存现':
                    $amounts['cash_amount'] = $item['type_sum'];
                    break;
                default:
                    $amounts['other_amount'] += $item['type_sum'];
            }
            $amounts['total_amount'] += $item['type_sum'];
        }
        
        // 检查是否已存在月度汇总
        $existMonthly = Db::name('sp_fund_collection_monthly')
            ->where('base_id', $baseId)
            ->where('collection_year', $year)
            ->where('collection_month', $month)
            ->find();
        
        if ($existMonthly) {
            // 更新记录
            Db::name('sp_fund_collection_monthly')
                ->where('id', $existMonthly['id'])
                ->update(array_merge($amounts, [
                    'update_time' => date('Y-m-d H:i:s')
                ]));
        } else {
            // 新增记录
            Db::name('sp_fund_collection_monthly')->insert(array_merge($amounts, [
                'base_id' => $baseId,
                'collection_year' => $year,
                'collection_month' => $month,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]));
        }
    }
    
    /**
     * @route GET finance/fund/list
     */
    public function getList()
    {
        try {
            $params = Request::get();
            
            // 构建查询条件
            $where = [];
            
            // 按年份筛选
            if (isset($params['year']) && !empty($params['year'])) {
                $where[] = ['m.collection_year', '=', intval($params['year'])];
            }
            
            // 按月份筛选
            if (isset($params['month']) && $params['month'] !== '') {
                $where[] = ['m.collection_month', '=', intval($params['month'])];
            }
            
            // 按县区筛选
            if (isset($params['county']) && !empty($params['county'])) {
                $where[] = ['b.county', 'like', '%' . $params['county'] . '%'];
            }
            
            // 按关键词搜索
            if (isset($params['keyword']) && !empty($params['keyword'])) {
                $where[] = ['b.account_name|b.account_number|b.arc_id|b.village', 'like', '%' . $params['keyword'] . '%'];
            }
            
            // 添加部门筛选
            if (isset($params['department_name']) && !empty($params['department_name'])) {
                $where[] = ['b.department_name', 'like', '%' . $params['department_name'] . '%'];
            }
            
            // 分页参数
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            // 查询数据
            $list = Db::name('sp_fund_collection_monthly')
                ->alias('m')
                ->join('sp_fund_collection_base b', 'm.base_id = b.id')
                ->where($where)
                ->field('m.id, 
                    b.department_name,
                    b.arc_id,
                    m.collection_year,
                    m.collection_month,
                    m.collection_type,
                    m.amount')
                ->order('m.collection_year DESC, m.collection_month DESC, b.county, b.township')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            // 获取总数
            $total = Db::name('sp_fund_collection_monthly')
                ->alias('m')
                ->join('sp_fund_collection_base b', 'm.base_id = b.id')
                ->where($where)
                ->count();
            
            // 获取汇总数据
            $summary = Db::name('sp_fund_collection_monthly')
                ->alias('m')
                ->join('sp_fund_collection_base b', 'm.base_id = b.id')
                ->where($where)
                ->field('SUM(m.total_amount) as total_sum, 
                         SUM(m.pos_amount) as pos_sum, 
                         SUM(m.linshang_amount) as linshang_sum, 
                         SUM(m.guowang_amount) as guowang_sum, 
                         SUM(m.cash_amount) as cash_sum, 
                         SUM(m.other_amount) as other_sum')
                ->find();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'summary' => $summary
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取收入列表失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取数据失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * @route GET finance/fund/detail
     */
    public function getDetail()
    {
        try {
            $params = Request::get();
            
            // 必须提供baseId和年月
            if (!isset($params['base_id']) || !isset($params['year']) || !isset($params['month'])) {
                return json(['code' => 400, 'message' => '参数不完整']);
            }
            
            $baseId = intval($params['base_id']);
            $year = intval($params['year']);
            $month = intval($params['month']);
            
            // 查询基础信息
            $baseInfo = Db::name('sp_fund_collection_base')
                ->where('id', $baseId)
                ->find();
            
            if (!$baseInfo) {
                return json(['code' => 404, 'message' => '未找到基础数据']);
            }
            
            // 查询月度汇总
            $monthly = Db::name('sp_fund_collection_monthly')
                ->where('base_id', $baseId)
                ->where('collection_year', $year)
                ->where('collection_month', $month)
                ->find();
            
            // 查询明细数据
            $details = Db::name('sp_fund_collection_detail')
                ->where('base_id', $baseId)
                ->where('collection_year', $year)
                ->where('collection_month', $month)
                ->order('collection_date')
                ->select()
                ->toArray();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'base' => $baseInfo,
                    'monthly' => $monthly,
                    'details' => $details
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取收入明细失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取数据失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取统计数据
     */
    public function getStats()
    {
        try {
            $params = Request::get();
            $year = isset($params['year']) ? intval($params['year']) : date('Y');
            
            // 按月份统计
            $monthlyStats = Db::name('sp_fund_collection_monthly')
                ->where('collection_year', $year)
                ->field('collection_month, 
                        SUM(total_amount) as total_sum, 
                        SUM(pos_amount) as pos_sum, 
                        SUM(linshang_amount) as linshang_sum,
                        SUM(guowang_amount) as guowang_sum,
                        SUM(cash_amount) as cash_sum,
                        SUM(other_amount) as other_sum')
                ->group('collection_month')
                ->order('collection_month')
                ->select()
                ->toArray();
            
            // 按县区统计
            $countyStats = Db::name('sp_fund_collection_monthly')
                ->alias('m')
                ->join('sp_fund_collection_base b', 'm.base_id = b.id')
                ->where('m.collection_year', $year)
                ->field('b.county, SUM(m.total_amount) as total_sum')
                ->group('b.county')
                ->order('total_sum DESC')
                ->select()
                ->toArray();
            
            // 按归集方式统计
            $typeStats = [
                'pos' => Db::name('sp_fund_collection_monthly')
                    ->where('collection_year', $year)
                    ->sum('pos_amount'),
                'linshang' => Db::name('sp_fund_collection_monthly')
                    ->where('collection_year', $year)
                    ->sum('linshang_amount'),
                'guowang' => Db::name('sp_fund_collection_monthly')
                    ->where('collection_year', $year)
                    ->sum('guowang_amount'),
                'cash' => Db::name('sp_fund_collection_monthly')
                    ->where('collection_year', $year)
                    ->sum('cash_amount'),
                'other' => Db::name('sp_fund_collection_monthly')
                    ->where('collection_year', $year)
                    ->sum('other_amount')
            ];
            
            // 年度总计
            $yearTotal = Db::name('sp_fund_collection_monthly')
                ->where('collection_year', $year)
                ->sum('total_amount');
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'monthly_stats' => $monthlyStats,
                    'county_stats' => $countyStats,
                    'type_stats' => $typeStats,
                    'year_total' => $yearTotal
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取统计数据失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取统计数据失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 导出数据
     */
    public function exportData()
    {
        try {
            $params = Request::get();
            $where = $this->buildQueryConditions($params);
            
            $data = Db::name('sp_fund_collection_monthly')
                ->alias('m')
                ->join('sp_fund_collection_base b', 'm.base_id = b.id')
                ->where($where)
                ->field('b.arc_id,
                    b.department_name,
                    m.collection_year,
                    m.collection_month,
                    m.collection_type,
                    m.amount')
                ->order('m.collection_year DESC, m.collection_month DESC')
                ->select()
                ->toArray();

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $headers = ['档案号', '县区', '乡镇', '村名', '户名', '国网号',
                       '年份', '月份', 'POS金额', '临商金额', '国网金额',
                       '存现金额', '其他金额', '总金额'];
            $sheet->fromArray($headers, null, 'A1');

            // 填充数据
            $row = 2;
            foreach ($data as $item) {
                $sheet->fromArray([
                    $item['arc_id'],
                    $item['department_name'],
                    $item['collection_year'],
                    $item['collection_month'],
                    $item['collection_type'],
                    $item['amount']
                ], null, "A{$row}");
                $row++;
            }

            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="收入数据导出.xlsx"');
            header('Cache-Control: max-age=0');

            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('导出失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '导出失败']);
        }
    }

    private function logOperation($action, $data = [])
    {
        Db::name('sp_operation_log')->insert([
            'module' => 'finance/fund',
            'action' => $action,
            'operator_id' => session('user_id'),
            'content' => json_encode($data),
            'ip' => request()->ip(),
            'create_time' => date('Y-m-d H:i:s')
        ]);
    }
}