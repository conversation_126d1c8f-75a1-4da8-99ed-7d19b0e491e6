<?php
declare (strict_types = 1);

namespace app\service;

class BaseService
{
    /**
     * 返回成功结果
     */
    protected function success($data = null, string $message = 'success')
    {
        return [
            'code' => 200,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 返回错误结果
     */
    protected function error(string $message = 'error', int $code = 500, $data = null)
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 事务封装
     */
    protected function transaction(\Closure $callback)
    {
        try {
            \think\facade\Db::startTrans();
            $result = $callback();
            \think\facade\Db::commit();
            return $this->success($result);
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return $this->error($e->getMessage());
        }
    }
} 