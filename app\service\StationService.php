<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Station;
use think\facade\Db;
use think\facade\Log;

class StationService extends BaseService
{
    /**
     * 获取电站列表
     */
    public function getList($params = [])
    {
        try {
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            
            // 详细的调试日志
            trace('Station list request params: ' . json_encode($params), 'debug');
            
            $query = Station::with(['department']);
            
            // 确保只查询未删除的记录
            $query->where('is_deleted', 0);

            // 应用电站类型过滤（确保类型转换为整数）
            if (isset($params['type'])) {
                $type = intval($params['type']);
                $query->where('type', '=', $type);
                trace('Filtering by type: ' . $type, 'debug');
            }

            // 应用搜索条件
            if (!empty($params['keyword'])) {
                $query->where(function($query) use ($params) {
                    $query->where('station_name', 'like', "%{$params['keyword']}%")
                          ->whereOr('address', 'like', "%{$params['keyword']}%")
                          ->whereOr('contact_name', 'like', "%{$params['keyword']}%")
                          ->whereOr('contact_phone', 'like', "%{$params['keyword']}%");
                });
            }
            if (!empty($params['department_id'])) {
                $query->where('department_id', intval($params['department_id']));
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('status', intval($params['status']));
            }

            // 先获取SQL语句
            $sqlBeforeQuery = $query->buildSql();
            trace('Generated SQL before query: ' . $sqlBeforeQuery, 'debug');

            // 获取总数
            $total = $query->count();
            trace('Total count: ' . $total, 'debug');
            
            // 获取分页数据
            $items = $query->page($page, $limit)
                ->order('id', 'desc')
                ->select();

            // 记录最终执行的SQL
            trace('Final executed SQL: ' . Db::getLastSql(), 'debug');
            trace('Retrieved items count: ' . count($items), 'debug');
            trace('First item data: ' . json_encode($items->first()), 'debug');

            return $this->success([
                'items' => $items,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            trace('Get station list error: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return $this->error('获取电站列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建电站
     */
    public function create($data)
    {
        Db::startTrans();
        try {
            $station = new Station;
            $station->type = intval($data['type']);
            $station->station_name = $data['station_name'];
            $station->address = $data['address'];
            $station->contact_name = $data['contact_name'];
            $station->contact_phone = $data['contact_phone'];
            $station->capacity = $data['capacity'];
            $station->install_date = $data['install_date'];
            $station->department_id = $data['department_id'];
            $station->latitude = $data['latitude'] ?? null;
            $station->longitude = $data['longitude'] ?? null;
            $station->status = $data['status'] ?? 1;
            
            // 工商业电站特有字段
            if ($station->type === Station::TYPE_BUSINESS) {
                $station->business_type = $data['business_type'] ?? null;
            }
            
            $station->contract_number = $data['contract_number'] ?? null;
            
            if ($station->save() === false) {
                Db::rollback();
                return $this->error('电站创建失败');
            }

            Db::commit();
            return $this->success($station, '电站创建成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('电站创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新电站
     */
    public function update($id, $data)
    {
        Db::startTrans();
        try {
            // 记录原始请求数据
            trace('Update request - ID: ' . $id, 'debug');
            trace('Update data received: ' . json_encode($data), 'debug');

            // 获取原始数据
            $station = Station::find($id);
            if (!$station) {
                Db::rollback();
                return $this->error('电站不存在');
            }

            // 构建更新数据
            $updateData = [];
            $allowedFields = [
                'station_name' => 'string',
                'business_type' => 'string',
                'contact_name' => 'string',
                'contact_phone' => 'string',
                'address' => 'string',
                'capacity' => 'float',
                'install_date' => 'string',
                'contract_number' => 'string',
                'department_id' => 'int',
                'latitude' => 'string',
                'longitude' => 'string',
                'status' => 'int'
            ];

            // 过滤和处理数据
            foreach ($allowedFields as $field => $type) {
                if (isset($data[$field])) {
                    if ($type === 'int') {
                        $updateData[$field] = intval($data[$field]);
                    } elseif ($type === 'float') {
                        $updateData[$field] = floatval($data[$field]);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            // 记录将要更新的数据
            trace('Data to update: ' . json_encode($updateData), 'debug');

            // 使用原生SQL更新
            $setClause = [];
            $params = [];
            foreach ($updateData as $field => $value) {
                $setClause[] = "`{$field}` = ?";
                $params[] = $value;
            }
            $params[] = $id;

            $sql = "UPDATE `sp_station` SET " . implode(', ', $setClause) . " WHERE `id` = ?";
            
            // 记录SQL语句
            trace('Update SQL: ' . $sql, 'debug');
            trace('SQL params: ' . json_encode($params), 'debug');

            // 执行更新
            $result = Db::execute($sql, $params);

            if ($result === false) {
                Db::rollback();
                return $this->error('电站更新失败');
            }

            // 获取更新后的数据
            $updatedStation = Station::find($id);
            trace('Updated station data: ' . json_encode($updatedStation), 'debug');

            Db::commit();
            return $this->success($updatedStation, '电站更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            trace('Update station error: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return $this->error('电站更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除电站
     */
    public function delete($id)
    {
        Db::startTrans();
        try {
            $station = Station::find($id);
            if (!$station) {
                Db::rollback();
                Log::warning("尝试删除不存在的电站 (ID: {$id})");
                return $this->error('电站不存在');
            }

            // 软删除电站（修改is_deleted标记为1）
            if (Db::table('sp_station')->where('id', $id)->update(['is_deleted' => 1]) === false) {
                Db::rollback();
                return $this->error('电站删除失败');
            }

            Db::commit();
            return $this->success(null, '电站删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            trace('删除电站失败: ' . $e->getMessage(), 'error');
            return $this->error('电站删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 更改电站状态
     */
    public function changeStatus($id, $status)
    {
        try {
            // 确保 ID 是数字
            $id = intval($id);
            trace('Changing station status - ID: ' . $id . ', Status: ' . $status, 'debug');
            
            // 使用参数绑定进行查询
            $station = Db::table('sp_station')
                ->where('id', '=', $id)
                ->find();
                
            trace('SQL: ' . Db::getLastSql(), 'debug');
            trace('Query result: ' . json_encode($station), 'debug');

            if (!$station) {
                trace('Station not found with ID: ' . $id, 'error');
                return $this->error('电站不存在');
            }

            // 使用参数绑定进行更新
            $result = Db::table('sp_station')
                ->where('id', '=', $id)
                ->update([
                    'status' => (int)$status,
                    'update_at' => date('Y-m-d H:i:s')
                ]);
                
            trace('Update SQL: ' . Db::getLastSql(), 'debug');
            trace('Update result: ' . $result, 'debug');

            if ($result === false) {
                trace('Failed to update station status', 'error');
                return $this->error('状态更新失败');
            }

            return $this->success(['status' => (int)$status], '状态更新成功');
        } catch (\Exception $e) {
            trace('Exception: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取电站详情
     */
    public function detail($id)
    {
        try {
            $station = Station::with(['department'])->find($id);
            if (!$station) {
                return $this->error('电站不存在');
            }

            // 添加类型文本
            $station->type_text = $station->type == Station::TYPE_FARMER ? '合作农户' : '工商业';

            return $this->success($station);
        } catch (\Exception $e) {
            trace('Get station detail error: ' . $e->getMessage(), 'error');
            return $this->error('获取电站详情失败: ' . $e->getMessage());
        }
    }
} 