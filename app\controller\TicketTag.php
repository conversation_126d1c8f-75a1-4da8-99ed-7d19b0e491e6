<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;

class TicketTag
{
    /**
     * 获取标签列表
     */
    public function index()
    {
        try {
            $tags = Db::name('ticket_tag')
                ->field('id, name, color, used_count')
                ->order('id asc')
                ->select();

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $tags
            ]);
        } catch (\Exception $e) {
            trace('获取标签数据失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取标签数据失败',
                'data' => null
            ]);
        }
    }

    /**
     * 创建标签
     */
    public function create()
    {
        try {
            $data = request()->post();
            trace('创建标签请求数据：' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 验证数据
            if (empty($data['name']) || empty($data['color'])) {
                return json([
                    'code' => 400,
                    'message' => '标签名称和颜色不能为空',
                    'data' => null
                ]);
            }

            // 检查名称是否已存在
            $exists = Db::name('ticket_tag')
                ->where('name', $data['name'])
                ->find();
            
            if ($exists) {
                return json([
                    'code' => 400,
                    'message' => '标签名称已存在',
                    'data' => null
                ]);
            }

            // 获取表字段信息
            $fields = Db::name('ticket_tag')->getTableFields();
            trace('标签表字段：' . json_encode($fields, JSON_UNESCAPED_UNICODE), 'debug');

            // 创建标签
            $insertData = [
                'name' => $data['name'],
                'color' => $data['color'],
                'create_at' => date('Y-m-d H:i:s')
            ];

            trace('准备插入的数据：' . json_encode($insertData, JSON_UNESCAPED_UNICODE), 'debug');
            $id = Db::name('ticket_tag')->insertGetId($insertData);

            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            trace('创建标签失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '创建标签失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新标签
     */
    public function update($id)
    {
        try {
            $data = request()->param();
            
            // 验证数据
            if (empty($data['name']) || empty($data['color'])) {
                return json([
                    'code' => 400,
                    'message' => '标签名称和颜色不能为空',
                    'data' => null
                ]);
            }

            // 检查标签是否存在
            $tag = Db::name('ticket_tag')->where('id', $id)->find();
            if (!$tag) {
                return json([
                    'code' => 404,
                    'message' => '标签不存在',
                    'data' => null
                ]);
            }

            // 检查名称是否已被其他标签使用
            $exists = Db::name('ticket_tag')
                ->where('name', $data['name'])
                ->where('id', '<>', $id)
                ->find();
            
            if ($exists) {
                return json([
                    'code' => 400,
                    'message' => '标签名称已存在',
                    'data' => null
                ]);
            }

            // 更新标签
            Db::name('ticket_tag')
                ->where('id', $id)
                ->update([
                    'name' => $data['name'],
                    'color' => $data['color'],
                    'update_at' => date('Y-m-d H:i:s')
                ]);

            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            trace('更新标签失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '更新标签失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除标签
     */
    public function delete($id)
    {
        try {
            // 检查标签是否在使用中
            $tag = Db::name('ticket_tag')
                ->where('id', $id)
                ->find();
            
            if (!$tag) {
                return json([
                    'code' => 404,
                    'message' => '标签不存在',
                    'data' => null
                ]);
            }

            if ($tag['used_count'] > 0) {
                return json([
                    'code' => 400,
                    'message' => '该标签正在使用中，无法删除',
                    'data' => null
                ]);
            }

            // 删除标签
            Db::name('ticket_tag')->where('id', $id)->delete();

            // 删除关联关系
            Db::name('ticket_tag_relation')->where('tag_id', $id)->delete();

            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            trace('删除标签失败：' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '删除标签失败',
                'data' => null
            ]);
        }
    }
} 