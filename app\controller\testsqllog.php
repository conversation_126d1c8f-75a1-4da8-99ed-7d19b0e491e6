<?php
public function testSqlLog()
{
    // 记录开始日志
    trace('开始测试 SQL 日志记录', 'info');
    
    // 执行一个简单的 SQL 查询
    $result = Db::table('sp_user')->where('id', 1)->find();
    
    // 直接调用日志记录方法
    $sql = Db::getLastSql();
    trace('手动记录 SQL: ' . $sql, 'info');
    
    // 直接调用记录方法
    $success = LogService::record($sql, ['execution_time' => 0.001, 'explain' => '测试']);
    
    // 返回测试结果
    return json([
        'success' => $success,
        'sql' => $sql,
        'result' => $result
    ]);
}