<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Station;
use app\model\StationData;
use app\model\Alarm;
use think\facade\Db;

class DashboardService
{
    protected $station;
    protected $stationData;
    protected $alarm;

    public function __construct(Station $station, StationData $stationData, Alarm $alarm)
    {
        $this->station = $station;
        $this->stationData = $stationData;
        $this->alarm = $alarm;
    }

    /**
     * 获取总览数据
     */
    public function getOverview($params)
    {
        // 部门权限
        $query = $this->station;
        if (!empty($params['department_id'])) {
            $query->where('department_id', $params['department_id']);
        }
        
        // 统计数据
        $totalCapacity = $query->sum('capacity');
        $totalStations = $query->count();
        $runningStations = $query->where('status', Station::STATUS_RUNNING)->count();
        
        // 今日发电量
        $today = date('Y-m-d');
        $todayPower = $this->stationData
            ->whereTime('record_time', 'today')
            ->sum('power_generation');
            
        // 未处理告警
        $unhandledAlarms = $this->alarm
            ->where('status', '<>', Alarm::STATUS_RESOLVED)
            ->count();
            
        return [
            'total_capacity' => $totalCapacity,
            'total_stations' => $totalStations,
            'running_stations' => $runningStations,
            'today_power' => $todayPower,
            'unhandled_alarms' => $unhandledAlarms
        ];
    }

    /**
     * 获取电站统计
     */
    public function getStationStats($params)
    {
        // 部门权限
        $query = $this->station;
        if (!empty($params['department_id'])) {
            $query->where('department_id', $params['department_id']);
        }
        
        // 按状态统计
        $statusStats = $query->group('status')
            ->field(['status', 'count(*) as count'])
            ->select();
            
        // 按装机容量区间统计
        $capacityStats = $query->field([
                'CASE 
                    WHEN capacity <= 100 THEN "0-100kW"
                    WHEN capacity <= 500 THEN "100-500kW"
                    WHEN capacity <= 1000 THEN "500-1000kW"
                    ELSE ">1000kW"
                END as range',
                'count(*) as count'
            ])
            ->group('range')
            ->select();
            
        return [
            'status_stats' => $statusStats,
            'capacity_stats' => $capacityStats
        ];
    }

    /**
     * 获取发电趋势
     */
    public function getPowerTrend($params)
    {
        // 时间范围
        $startTime = $params['start_time'] ?? date('Y-m-d', strtotime('-30 days'));
        $endTime = $params['end_time'] ?? date('Y-m-d');
        
        // 部门权限
        $stationIds = [];
        if (!empty($params['department_id'])) {
            $stationIds = $this->station
                ->where('department_id', $params['department_id'])
                ->column('id');
        }
        
        // 查询数据
        $query = $this->stationData
            ->whereTime('record_time', 'between', [$startTime, $endTime])
            ->field([
                'DATE(record_time) as date',
                'sum(power_generation) as power',
                'avg(irradiance) as irradiance'
            ])
            ->group('date');
            
        if ($stationIds) {
            $query->whereIn('station_id', $stationIds);
        }
        
        return $query->select();
    }

    /**
     * 获取告警统计
     */
    public function getAlarmStats($params)
    {
        // 部门权限
        $stationIds = [];
        if (!empty($params['department_id'])) {
            $stationIds = $this->station
                ->where('department_id', $params['department_id'])
                ->column('id');
        }
        
        $query = $this->alarm;
        if ($stationIds) {
            $query->whereIn('station_id', $stationIds);
        }
        
        // 按类型统计
        $typeStats = $query->group('type')
            ->field(['type', 'count(*) as count'])
            ->select();
            
        // 按级别统计
        $levelStats = $query->group('level')
            ->field(['level', 'count(*) as count'])
            ->select();
            
        // 按状态统计
        $statusStats = $query->group('status')
            ->field(['status', 'count(*) as count'])
            ->select();
            
        return [
            'type_stats' => $typeStats,
            'level_stats' => $levelStats,
            'status_stats' => $statusStats
        ];
    }
} 