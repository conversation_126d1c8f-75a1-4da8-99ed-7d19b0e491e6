<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\PermissionService;
use think\facade\Request;

class Permission extends BaseController
{
    protected $permissionService;

    public function initialize()
    {
        parent::initialize();
        $this->permissionService = new PermissionService();
    }

    /**
     * 获取权限列表
     */
    public function index()
    {
        return json($this->permissionService->getList());
    }

    /**
     * 获取权限树
     */
    public function tree()
    {
        return json($this->permissionService->getTree());
    }

    /**
     * 创建权限
     */
    public function create()
    {
        $params = Request::post();
        return json($this->permissionService->create($params));
    }

    /**
     * 更新权限
     */
    public function update($id)
    {
        $params = Request::put();
        return json($this->permissionService->update($id, $params));
    }

    /**
     * 删除权限
     */
    public function delete($id)
    {
        return json($this->permissionService->delete($id));
    }

    /**
     * 初始化权限数据
     */
    public function init()
    {
        return json($this->permissionService->initializePermissions());
    }
} 