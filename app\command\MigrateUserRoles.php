<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class MigrateUserRoles extends Command
{
    protected function configure()
    {
        $this->setName('migrate:user-roles')
            ->setDescription('迁移用户角色数据，从sp_user_role表到sp_user表的role_ids字段');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("<info>开始迁移用户角色数据...</info>");
        
        try {
            // 检查sp_user表是否已有role_ids字段
            $hasField = false;
            $columns = Db::query("SHOW COLUMNS FROM sp_user");
            foreach ($columns as $column) {
                if ($column['Field'] === 'role_ids') {
                    $hasField = true;
                    break;
                }
            }
            
            // 如果role_ids字段不存在，则创建
            if (!$hasField) {
                $output->writeln("<info>为sp_user表添加role_ids字段...</info>");
                Db::execute("ALTER TABLE sp_user ADD COLUMN role_ids VARCHAR(255) DEFAULT NULL COMMENT '角色ID，多个用逗号分隔'");
            }
            
            // 查询所有用户
            $users = Db::name('user')->select()->toArray();
            $output->writeln("<info>找到 " . count($users) . " 个用户</info>");
            
            $successful = 0;
            $failed = 0;
            
            // 查询用户角色关系表并更新用户表
            foreach ($users as $user) {
                try {
                    // 获取用户的角色ID
                    $roleIds = Db::name('user_role')
                        ->where('user_id', $user['id'])
                        ->column('role_id');
                    
                    if (!empty($roleIds)) {
                        // 转换为字符串并保存
                        $roleIdsStr = implode(',', $roleIds);
                        Db::name('user')
                            ->where('id', $user['id'])
                            ->update(['role_ids' => $roleIdsStr]);
                        
                        $output->writeln("- 用户 ID:{$user['id']}, 用户名:{$user['username']} - 已分配角色: " . $roleIdsStr);
                        $successful++;
                    } else {
                        $output->writeln("- 用户 ID:{$user['id']}, 用户名:{$user['username']} - 无角色");
                        $successful++;
                    }
                } catch (\Exception $e) {
                    $output->writeln("<error>处理用户 ID:{$user['id']} 失败: " . $e->getMessage() . "</error>");
                    $failed++;
                }
            }
            
            $output->writeln("<info>迁移完成!</info>");
            $output->writeln("<info>成功: " . $successful . " 个用户</info>");
            
            if ($failed > 0) {
                $output->writeln("<error>失败: " . $failed . " 个用户</error>");
            }
            
            $output->writeln("<comment>注意: 原sp_user_role表中的数据已经迁移到sp_user表的role_ids字段中。</comment>");
            $output->writeln("<comment>请确认数据迁移成功后再进行其他操作。</comment>");
            
        } catch (\Exception $e) {
            $output->writeln("<error>迁移过程中出错: " . $e->getMessage() . "</error>");
            return 1;
        }
        
        return 0;
    }
} 