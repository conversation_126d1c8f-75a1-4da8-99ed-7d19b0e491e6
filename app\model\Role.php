<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class Role extends Model
{
    protected $name = 'role';
    protected $prefix = 'sp_';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';

    // 添加 code 字段
    protected $field = ['id', 'code', 'name', 'description', 'create_at', 'update_at'];

    // 关联权限
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'role_permission', 'role_id', 'permission_id');
    }

    // 关联用户
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_role', 'role_id', 'user_id');
    }
}