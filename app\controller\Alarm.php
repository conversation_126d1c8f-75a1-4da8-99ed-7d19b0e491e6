<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\AlarmService;
use think\facade\Request;

class Alarm extends BaseController
{
    protected $alarmService;

    public function initialize()
    {
        parent::initialize();
        $this->alarmService = new AlarmService();
    }

    /**
     * 获取告警列表
     */
    public function index()
    {
        $params = Request::get();
        return json($this->alarmService->getList($params));
    }

    /**
     * 处理告警
     */
    public function handle($id)
    {
        $params = Request::put();
        return json($this->alarmService->handle($id, $params));
    }

    /**
     * 删除告警
     */
    public function delete($id)
    {
        return json($this->alarmService->delete($id));
    }
} 