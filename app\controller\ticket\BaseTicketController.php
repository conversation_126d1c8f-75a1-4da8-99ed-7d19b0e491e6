<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\BaseController;
use app\model\Ticket as TicketModel;
use app\service\TicketService;
use think\App;
use think\facade\Log;

class BaseTicketController extends BaseController
{
    protected $ticketModel;
    protected $ticketService;
    
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->ticketModel = new TicketModel();
        $this->ticketService = new TicketService();
    }
    
    /**
     * 验证当前用户是否为管理员
     * @return bool
     */
    protected function isAdmin()
    {
        $roleIds = session('role_ids');
        Log::debug('basecontroller::验证管理员权限 - roleIds: ' . json_encode($roleIds));
        
        // 如果roleIds不存在，尝试使用其他方法判断
        if (empty($roleIds)) {
            // 尝试从roles数组中判断是否包含超级管理员
            $roles = session('roles');
            if (is_array($roles) && in_array('超级管理员', $roles)) {
                return true;
            }
            
            // 从数据库实时查询用户角色
            $userId = session('user_id');
            if ($userId) {
                $roleIds = \think\facade\Db::name('user_role')
                    ->where('user_id', $userId)
                    ->column('role_id');
                    
                Log::debug('basecontroller::从数据库查询的roleIds: ' . json_encode($roleIds));
                return is_array($roleIds) && in_array(1, $roleIds);
            }
        }
        
        return is_array($roleIds) && in_array(1, $roleIds);
    }
    
    /**
     * 检查用户登录状态
     * @return bool|\think\response\Json
     */
    protected function checkLogin()
    {
        if (!session('user_id')) {
            return json([
                'code' => 401,
                'message' => '未登录或登录已过期'
            ]);
        }
        
        return true;
    }
} 