<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\DepartmentService;
use app\service\LogService;
use think\App;

class Department extends BaseController
{
    protected $departmentService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->departmentService = new DepartmentService();
    }

    /**
     * 获取部门列表
     */
    public function index()
    {
        return json($this->departmentService->getList());
    }

    /**
     * 获取部门树
     */
    public function tree()
    {
        return json($this->departmentService->getTree());
    }

    /**
     * 创建部门
     */
    public function create()
    {
        $data = $this->request->post();
        $result = $this->departmentService->create($data);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('department', 'create', array_merge(
                ['id' => $result['data']['id']],
                $data
            ));
        }
        
        return json($result);
    }

    /**
     * 更新部门
     */
    public function update($id)
    {
        $data = $this->request->put();
        $result = $this->departmentService->update($id, $data);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('department', 'update', array_merge(
                ['id' => $id],
                $data
            ));
        }
        
        return json($result);
    }

    /**
     * 删除部门
     */
    public function delete($id)
    {
        $result = $this->departmentService->delete($id);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('department', 'delete', ['id' => $id]);
        }
        
        return json($result);
    }

    /**
     * 获取部门树，包含部门下的用户信息，用于工单管理
     */
    public function getDepartmentsWithUsers()
    {
        try {
            $departments = $this->departmentService->getDepartmentsWithUsers();
            
            // 构建前端所需的处理人树结构
            $handlersTree = [];
            
            // 处理顶级部门
            foreach ($departments as $dept) {
                if ($dept['parent_id'] == 0) {
                    $deptNode = [
                        'id' => $dept['id'],  // 不添加前缀，保持与前端一致
                        'type' => 'department',
                        'label' => $dept['name'],
                        'children' => []
                    ];
                    
                    // 添加部门下的用户
                    if (isset($dept['users']) && !empty($dept['users'])) {
                        foreach ($dept['users'] as $user) {
                            $deptNode['children'][] = [
                                'id' => $user['id'],  // 不添加前缀
                                'type' => 'user',
                                'label' => $user['realname'] ?: $user['username'],
                                'user_data' => $user
                            ];
                        }
                    }
                    
                    // 添加子部门
                    foreach ($departments as $subDept) {
                        if ($subDept['parent_id'] == $dept['id']) {
                            $subDeptNode = [
                                'id' => $subDept['id'],  // 不添加前缀
                                'type' => 'department',
                                'label' => $subDept['name'],
                                'children' => []
                            ];
                            
                            // 添加子部门下的用户
                            if (isset($subDept['users']) && !empty($subDept['users'])) {
                                foreach ($subDept['users'] as $user) {
                                    $subDeptNode['children'][] = [
                                        'id' => $user['id'],  // 不添加前缀
                                        'type' => 'user',
                                        'label' => $user['realname'] ?: $user['username'],
                                        'user_data' => $user
                                    ];
                                }
                            }
                            
                            $deptNode['children'][] = $subDeptNode;
                        }
                    }
                    
                    $handlersTree[] = $deptNode;
                }
            }
            
            // 同时提供扁平化的部门列表，以符合前端期望
            $flatDepartments = [];
            foreach ($departments as $dept) {
                $flatDepartments[] = [
                    'id' => $dept['id'],
                    'name' => $dept['name'],
                    'parent_id' => $dept['parent_id']
                ];
            }
            
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'departments' => $flatDepartments,
                    'handlers' => $handlersTree
                ]
            ]);
        } catch (\Exception $e) {
            trace('获取部门和用户数据失败: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '获取部门和用户数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取部门及其子部门的所有用户
     */
    public function getDepartmentUsers()
    {
        try {
            // 验证登录状态
            if (!$this->isLogin()) {
                return $this->error('未登录', 401);
            }
            
            $departmentId = input('department_id/d', 0);
            $includeChild = input('include_child/d', 1); // 默认包含子部门
            $status = input('status/d', 1); // 默认只获取状态正常的用户
            
            if (!$departmentId) {
                return $this->error('请指定部门ID');
            }
            
            // 查询部门是否存在
            $department = \app\model\Department::find($departmentId);
            if (!$department) {
                return $this->error('部门不存在');
            }
            
            // 收集所有需要查询的部门ID
            $departmentIds = [$departmentId];
            
            // 如果需要包含子部门
            if ($includeChild) {
                // 获取所有子部门
                $childDepartments = \app\model\Department::where('parent_id', $departmentId)->column('id');
                if (!empty($childDepartments)) {
                    $departmentIds = array_merge($departmentIds, $childDepartments);
                }
            }
            
            trace('获取部门用户 - 查询的部门IDs：' . json_encode($departmentIds), 'debug');
            
            // 查询这些部门下的所有用户
            $userQuery = \app\model\User::whereIn('department_id', $departmentIds);
            
            // 状态过滤
            if ($status !== null) {
                $userQuery->where('status', $status);
            }
            
            // 获取用户数据
            $users = $userQuery->field('id, username, realname, department_id, mobile, email, status')
                ->select()
                ->toArray();
            
            // 为用户添加部门名称
            if (!empty($users)) {
                // 获取所有相关部门信息
                $deptMap = [];
                $departments = \app\model\Department::whereIn('id', array_column($users, 'department_id'))->select();
                foreach ($departments as $dept) {
                    $deptMap[$dept['id']] = $dept['name'];
                }
                
                // 添加部门名称
                foreach ($users as &$user) {
                    $user['department_name'] = isset($deptMap[$user['department_id']]) ? $deptMap[$user['department_id']] : '';
                }
            }
            
            trace('获取部门用户 - 共找到' . count($users) . '个用户', 'debug');
            
            return $this->success($users);
        } catch (\Exception $e) {
            trace('获取部门用户失败：' . $e->getMessage(), 'error');
            return $this->error('获取部门用户失败：' . $e->getMessage());
        }
    }
} 