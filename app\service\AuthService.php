<?php
declare (strict_types = 1);

namespace app\service;

use app\model\User;
use Firebase\JWT\JWT;
use think\Exception;
use think\facade\Config;
use think\facade\Cache;
use think\facade\Db;

class AuthService
{
    protected $user;

    public function __construct(User $user = null)
    {
        $this->user = $user ?? new User();
    }

    /**
     * 用户登录
     */
    public function login($username, $password)
    {
        try {
            // 获取用户信息
            $user = Db::name('user')
                ->where('username', $username)
                ->where('status', 1)
                ->find();

            if (!$user) {
                return ['code' => 400, 'message' => '用户不存在或已禁用'];
            }

            // 验证密码
            if (!password_verify($password, $user['password'])) {
                // 添加日志记录
                trace("密码验证失败 - 用户名: {$username}, 输入密码: {$password}", 'error');
                return ['code' => 400, 'message' => '密码错误'];
            }

            // 生成token
            $token = $this->createToken($user['id']);
            
            // 更新登录信息
            Db::name('user')->where('id', $user['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => request()->ip()
            ]);

            return [
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'realname' => $user['realname'],
                        'email' => $user['email'],
                        'mobile' => $user['mobile'],
                        'department_id' => $user['department_id']
                    ]
                ]
            ];
        } catch (\Exception $e) {
            trace("登录异常: " . $e->getMessage(), 'error');
            return ['code' => 500, 'message' => '登录失败：' . $e->getMessage()];
        }
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        return ['message' => '登出成功'];
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo($userId)
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    'code' => 401,
                    'message' => '用户信息不存在',
                    'data' => null
                ];
            }

            // 基本信息
            $data = [
                'id' => $user->id,
                'username' => $user->username,
                'realname' => $user->realname,
                'department_id' => $user->department_id,
                'status' => $user->status
            ];

            // 部门信息
            try {
                if ($user->department) {
                    $data['department'] = [
                        'id' => $user->department->id,
                        'name' => $user->department->name
                    ];
                }
            } catch (\Exception $e) {
                trace('获取部门信息失败：' . $e->getMessage(), 'error');
            }

            // 角色信息
            try {
                $roles = $user->roles()->select();
                $data['roles'] = $roles;
            } catch (\Exception $e) {
                trace('获取角色信息失败：' . $e->getMessage(), 'error');
                $data['roles'] = [];
            }

            return [
                'code' => 200,
                'message' => '获取用户信息成功',
                'data' => $data
            ];
            
        } catch (\Exception $e) {
            trace('获取用户信息失败：' . $e->getMessage(), 'error');
            return [
                'code' => 500,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 创建token
     */
    protected function createToken($userId)
    {
        $time = time();
        $payload = [
            'iss' => Config::get('jwt.issuer'),
            'iat' => $time,
            'exp' => $time + Config::get('jwt.ttl'),
            'user_id' => $userId
        ];
        
        return JWT::encode($payload, Config::get('jwt.secret'), 'HS256');
    }
} 