# 村集体打款字段映射文档

## 一、表头格式说明

导入村集体打款数据时，Excel文件的第一行应为标题行，格式为：

```
[项目建设年度]年项目[所在县]光伏项目村集体收益（[打款年度]年度）
```

例如：
```
2019年项目平邑县光伏项目村集体收益（2024年度）
```

系统会从标题中自动提取以下信息：
- 项目建设年度：2019
- 所在县：平邑县
- 打款年度：2024

## 二、Excel与数据库字段映射关系

Excel表格的第二行为字段名行，系统将按照以下映射关系进行字段匹配：

| Excel表头      | 数据库字段       | 字段类型      | 说明                 |
|---------------|----------------|--------------|---------------------|
| 序号           | -               | -            | 仅用于显示，不导入数据库 |
| 乡镇           | township        | varchar(100) | 所在乡镇              |
| 序号           | -               | -            | 二级序号，不导入数据库   |
| 村居           | village         | varchar(100) | 所在村                |
| 村集体收益（元） | amount          | decimal(12,2)| 收益金额              |
| 发放状态        | payment_status  | varchar(50)  | 发放状态              |

## 三、表格格式说明

导入的Excel表格格式应为：

1. 第一行：标题行，格式见上文
2. 第二行：字段名行，需包含"乡镇"、"村居"、"村集体收益（元）"、"发放状态"等必要字段
3. 第三行及以后：数据行

数据中乡镇相同的行会被合并显示，系统会自动处理合并单元格问题。

## 四、字段填写说明

1. **乡镇**：必填，所在乡镇名称
2. **村居**：必填，所在村名称
3. **村集体收益（元）**：必填，村集体收益金额，必须为数字
4. **发放状态**：选填，表示资金发放状态，可填写"已发放"、"未发放"等 