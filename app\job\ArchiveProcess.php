<?php
namespace app\job;

use think\queue\Job;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

class ArchiveProcess
{
    protected $progressKey = 'archive_init_progress';
    protected $archiveDir = ''; // 存储档案目录名
    
    /**
     * 执行任务
     * @param Job $job 队列
     * @param array $data 数据
     */
    public function fire(Job $job, $data)
    {
        try {
            // 设置无限执行时间
            ini_set('max_execution_time', 0);
            set_time_limit(0);
            
            // 确保必要的参数存在
            if (!isset($data['department_id']) || !isset($data['archive_dir'])) {
                Log::error('缺少必要参数: department_id 或 archive_dir');
                $job->delete();
                return;
            }
            
            $departmentId = $data['department_id'];
            $archiveDir = $data['archive_dir'];
            $departmentName = isset($data['department_name']) ? $data['department_name'] : '未知部门';
            $taskId = isset($data['task_id']) ? $data['task_id'] : 0;
            
            // 更新任务状态
            if ($taskId > 0) {
                Db::name('task_queue')->where('id', $taskId)->update([
                    'status' => 'processing',
                    'start_time' => date('Y-m-d H:i:s')
                ]);
            }
            
            // 保存档案目录名
            $this->archiveDir = $archiveDir;
            
            Log::info("队列开始处理档案 - 部门ID: {$departmentId}, 目录: {$archiveDir}, 任务ID: {$taskId}");
            
            // 初始化进度信息
            $this->updateProgress([
                'logs' => ["队列任务已启动，开始处理档案 - 部门: {$departmentName}, 目录: {$archiveDir}"]
            ]);
            
            // 开始处理档案
            $this->processArchives($departmentId, $archiveDir);
            
            // 标记任务完成
            $this->updateProgress([
                'finished' => true,
                'success' => true,
                'percentage' => 100,
                'logs' => ["档案初始化任务成功完成"]
            ]);
            
            // 更新任务状态
            if ($taskId > 0) {
                Db::name('task_queue')->where('id', $taskId)->update([
                    'status' => 'completed',
                    'progress' => 100,
                    'end_time' => date('Y-m-d H:i:s'),
                    'result' => '处理完成'
                ]);
            }
            
            Log::info("档案处理完成 - 部门ID: {$departmentId}, 目录: {$archiveDir}, 任务ID: {$taskId}");
            
            // 删除任务
            $job->delete();
        } catch (\Exception $e) {
            Log::error("档案处理异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            // 详细记录堆栈信息，帮助调试
            $errorDetails = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
            Log::error("错误详情: " . json_encode($errorDetails, JSON_UNESCAPED_UNICODE));
            
            // 更新任务状态
            if (isset($taskId) && $taskId > 0) {
                Db::name('task_queue')->where('id', $taskId)->update([
                    'status' => 'failed',
                    'end_time' => date('Y-m-d H:i:s'),
                    'error' => $e->getMessage()
                ]);
            }
            
            // 更新进度为错误状态
            $this->updateProgress([
                'finished' => true,
                'success' => false,
                'logs' => ["错误: 处理档案失败 - " . $e->getMessage()]
            ]);
            
            // 如果任务已经重试了指定次数，则删除
            if ($job->attempts() > 2) {
                Log::warning("任务重试次数过多，放弃处理");
                $job->delete();
            } else {
                // 延迟10秒后重试
                $job->release(10);
            }
        }
    }
    
    /**
     * 处理档案
     */
    protected function processArchives($departmentId, $archiveDir)
    {
        // 获取档案目录
        $oldArchPath = root_path() . 'public/oldarch/' . $archiveDir;
        
        if (!is_dir($oldArchPath)) {
            throw new \Exception('档案目录不存在');
        }
        
        // 获取部门
        $department = Db::name('department')->where('id', $departmentId)->find();
        if (!$department) {
            throw new \Exception('部门不存在');
        }
        
        // 从缓存中获取任务ID
        $progress = Cache::get($this->progressKey, []);
        $taskId = $progress['task_id'] ?? 0;
        
        // 阶段1: 收集所有文件并准备任务数组
        Log::info("开始收集文件信息...");
        $this->updateProgress(['logs' => ["开始收集文件信息..."]]);
        
        $taskList = $this->collectFileTasks($oldArchPath, $departmentId, $department, $archiveDir);
        
        // 更新总数
        $totalCount = count($taskList);
        $this->updateProgress([
            'total' => $totalCount,
            'logs' => ["已收集 {$totalCount} 个档案文件待处理"]
        ]);
        
        if ($totalCount == 0) {
            $this->updateProgress([
                'logs' => ["未找到需要处理的档案文件"],
                'finished' => true,
                'success' => false
            ]);
            throw new \Exception('未找到需要处理的档案文件');
        }
        
        // 阶段2: 执行文件处理
        Log::info("开始处理 {$totalCount} 个档案文件");
        $this->updateProgress(['logs' => ["开始处理 {$totalCount} 个档案文件..."]]);
        
        return $this->executeFileTasks($taskList);
    }
    
    /**
     * 收集所有文件任务
     */
    protected function collectFileTasks($oldArchPath, $departmentId, $department, $archiveDirName)
    {
        $taskList = [];
        $logs = [];
        
        // 从目录名称中提取年份信息（如果目录名包含年份）
        preg_match('/(\d{4})年(.+)/', $archiveDirName, $matches);
        
        $year = date('Y'); // 默认当前年份
        if (count($matches) >= 3) {
            $year = $matches[1];
        }
        
        // 使用目录名作为部门安装日期的依据
        $installDate = $year . '-01-01 00:00:00';
        
        // 准备更新电站安装日期的任务
        $taskList[] = [
            'type' => 'update_install_date',
            'dept_id' => $departmentId,
            'dept_name' => $department['name'],
            'install_date' => $installDate
        ];
        
        // 获取电站目录列表（或文件列表，如果直接是电站文件夹）
        $entries = $this->getDirectoryContents($oldArchPath);
        $stationDirs = $entries['dirs'];
        $files = $entries['files'];
        
        // 如果有直接的文件，则将其视为属于一个虚拟电站
        if (!empty($files)) {
            // 使用目录名称作为电站标识
            $arcId = 'default';
            $contactName = $archiveDirName;
            
            // 尝试从目录名提取arcId
            preg_match('/([^-]+)-(.+)/', $archiveDirName, $stationMatches);
            if (count($stationMatches) >= 3) {
                $arcId = trim($stationMatches[1]);
                $contactName = trim($stationMatches[2]);
            }
            
            // 查找对应的电站
            $station = $this->findMatchingStation($arcId, $contactName);
            
            if ($station) {
                $stationId = $station['id'];
                
                // 准备目标基础目录 - 不再在这里包含arc_id，让addFileTask处理
                $baseTargetDir = 'uploads/archive/' . date('Y/m/d') . '/' . $departmentId;
                $baseTargetPath = root_path() . 'public/' . $baseTargetDir;
                
                // 收集文件处理任务
                foreach ($files as $file) {
                    $taskList = $this->addFileTask($taskList, $oldArchPath, $file, $stationId, $arcId, $contactName, $baseTargetDir, $baseTargetPath);
                }
                
                $logs[] = "已收集 " . count($files) . " 个文件，从目录: {$archiveDirName}";
            } else {
                $logs[] = "警告: 未找到匹配的电站，使用目录名: {$archiveDirName}，将尝试创建";
                
                // 创建新电站记录
                $stationId = $this->createStation($arcId, $contactName, $departmentId);
                
                // 准备目标基础目录
                $baseTargetDir = 'uploads/archive/' . date('Y/m/d') . '/' . $departmentId;
                $baseTargetPath = root_path() . 'public/' . $baseTargetDir;
                
                // 收集文件处理任务
                foreach ($files as $file) {
                    $taskList = $this->addFileTask($taskList, $oldArchPath, $file, $stationId, $arcId, $contactName, $baseTargetDir, $baseTargetPath);
                }
                
                $logs[] = "已收集 " . count($files) . " 个文件，从目录: {$archiveDirName}，并创建了新电站记录";
            }
        }
        
        // 处理子目录（如果有）
        foreach ($stationDirs as $stationDir) {
            // 解析电站目录名称（arc_id-contact_name）
            preg_match('/([^-]+)-(.+)/', $stationDir, $stationMatches);
            
            if (count($stationMatches) >= 3) {
                $arcId = trim($stationMatches[1]);
                $contactName = trim($stationMatches[2]);
                
                // 查找对应的电站
                $station = $this->findMatchingStation($arcId, $contactName);
                
                if ($station) {
                    $stationId = $station['id'];
                    
                    // 获取档案文件
                    $stationDirPath = $oldArchPath . '/' . $stationDir;
                    $files = $this->getFiles($stationDirPath);
                    
                    // 准备目标基础目录
                    $baseTargetDir = 'uploads/archive/' . date('Y/m/d') . '/' . $departmentId;
                    $baseTargetPath = root_path() . 'public/' . $baseTargetDir;
                    
                    // 收集文件处理任务
                    foreach ($files as $file) {
                        $taskList = $this->addFileTask($taskList, $stationDirPath, $file, $stationId, $arcId, $contactName, $baseTargetDir, $baseTargetPath);
                    }
                    
                    if (!empty($files)) {
                        $logs[] = "已收集电站 {$arcId}-{$contactName} 的 " . count($files) . " 个档案文件";
                    }
                } else {
                    $logs[] = "警告: 未找到匹配的电站: {$arcId}-{$contactName}，将尝试创建";
                    
                    // 创建新电站记录
                    $stationId = $this->createStation($arcId, $contactName, $departmentId);
                    
                    // 获取档案文件
                    $stationDirPath = $oldArchPath . '/' . $stationDir;
                    $files = $this->getFiles($stationDirPath);
                    
                    // 准备目标基础目录
                    $baseTargetDir = 'uploads/archive/' . date('Y/m/d') . '/' . $departmentId;
                    $baseTargetPath = root_path() . 'public/' . $baseTargetDir;
                    
                    // 收集文件处理任务
                    foreach ($files as $file) {
                        $taskList = $this->addFileTask($taskList, $stationDirPath, $file, $stationId, $arcId, $contactName, $baseTargetDir, $baseTargetPath);
                    }
                    
                    if (!empty($files)) {
                        $logs[] = "已收集电站 {$arcId}-{$contactName} 的 " . count($files) . " 个档案文件，并创建了新电站记录";
                    }
                }
            } else {
                $logs[] = "警告: 电站目录名称格式不正确: {$stationDir}，跳过处理";
            }
        }
        
        // 更新日志
        if (!empty($logs)) {
            $this->updateProgress(['logs' => $logs]);
        }
        
        return $taskList;
    }
    
    /**
     * 查找匹配的电站
     */
    protected function findMatchingStation($arcId, $contactName)
    {
        // 查找对应的电站
        $station = Db::name('station')
            ->where([
                ['arc_id', '=', $arcId],
                ['contact_name', 'like', "%{$contactName}%"]
            ])
            ->find();
        
        if (!$station) {
            // 尝试仅根据arc_id查找
            $station = Db::name('station')
                ->where('arc_id', $arcId)
                ->find();
        }
        
        return $station;
    }
    
    /**
     * 创建新电站记录
     */
    protected function createStation($arcId, $contactName, $departmentId)
    {
        $stationData = [
            'arc_id' => $arcId,
            'contact_name' => $contactName,
            'department_id' => $departmentId,
            'install_date' => date('Y-m-d H:i:s'),
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 插入新电站记录
        $stationId = Db::name('station')->insertGetId($stationData);
        return $stationId;
    }
    
    /**
     * 添加文件处理任务
     */
    protected function addFileTask($taskList, $basePath, $file, $stationId, $arcId, $contactName, $targetDir, $targetPath)
    {
        // 检查是否是相对路径 (包含目录)
        $isRelativePath = strpos($file, '/') !== false;
        $filePath = $basePath . '/' . $file;
        
        // 如果文件不存在，可能是递归获取的路径，需要处理
        if (!file_exists($filePath) && $isRelativePath) {
            // 已经是相对路径，无需再拼接目录名
            $filePath = $basePath . '/' . $file;
        }
        
        if (file_exists($filePath) && is_file($filePath)) {
            $fileSize = filesize($filePath);
            $fileInfo = pathinfo($filePath);
            $fileType = isset($fileInfo['extension']) ? $fileInfo['extension'] : '';
            
            // 原始文件名不包含任何路径，确保只取基本文件名
            $fileName = $fileInfo['filename'];
            
            // 额外检查文件名中是否还包含路径分隔符
            if (strpos($fileName, '/') !== false || strpos($fileName, '\\') !== false) {
                // 不使用basename函数，而是手动清理路径分隔符
                // 取最后一个路径分隔符后的内容
                $lastSlashPos = max(strrpos($fileName, '/'), strrpos($fileName, '\\'));
                if ($lastSlashPos !== false) {
                    $fileName = substr($fileName, $lastSlashPos + 1);
                }
                // 确保没有残留的路径分隔符
                $fileName = str_replace(['/', '\\'], '_', $fileName);
            }
            
            // 保留子目录结构，创建对应的目标子目录
            $subDir = '';
            if ($isRelativePath) {
                $subDir = dirname($file);
                if ($subDir == '.') $subDir = '';
            }
            
            // 从targteDir中提取departmentId (区别于路径中直接使用变量)
            // targetDir格式: uploads/archive/YYYY/MM/DD/departmentId
            $pathParts = explode('/', $targetDir);
            // 确保有足够的路径部分
            if (count($pathParts) >= 5) {
                $departmentIdFromPath = $pathParts[count($pathParts) - 1];
                
                // 新的文件路径中加入arc_id - 放在departmentId后面
                $finalTargetDir = $targetDir . '/' . $arcId;
                $finalTargetPath = $targetPath . '/' . $arcId;
                
                if ($subDir) {
                    $finalTargetDir .= '/' . $subDir;
                    $finalTargetPath .= '/' . $subDir;
                }
                
                // 生成新的文件名
                $newFileName = date('YmdHis') . md5(uniqid(mt_rand(), true)) . ($fileType ? '.' . $fileType : '');
                
                // 添加到任务列表
                $taskList[] = [
                    'type' => 'process_file',
                    'station_id' => $stationId,
                    'station_name' => $arcId . '-' . $contactName,
                    'source_file' => $filePath,
                    'target_dir' => $finalTargetDir,
                    'target_path' => $finalTargetPath,
                    'new_file_name' => $newFileName,
                    'original_file_name' => $fileName, // 纯文件名，不含路径
                    'file_size' => $fileSize,
                    'file_type' => $fileType,
                    'sub_dir' => $subDir,
                    'arc_id' => $arcId
                ];
            } else {
                // 日志记录路径格式错误
                \think\facade\Log::error('路径格式错误，无法提取departmentId: ' . $targetDir);
            }
        }
        
        return $taskList;
    }
    
    /**
     * 获取目录内容（文件和子目录）
     */
    protected function getDirectoryContents($path)
    {
        $dirs = [];
        $files = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($entry = readdir($handle)) !== false) {
                    if ($entry != '.' && $entry != '..') {
                        $entryPath = $path . '/' . $entry;
                        if (is_dir($entryPath)) {
                            $dirs[] = $entry;
                        } elseif (is_file($entryPath)) {
                            $files[] = $entry;
                        }
                    }
                }
                closedir($handle);
            }
        }
        
        return ['dirs' => $dirs, 'files' => $files];
    }
    
    /**
     * 执行文件处理任务
     */
    protected function executeFileTasks($taskList)
    {
        $logs = [];
        $processedCount = 0;
        $totalCount = count($taskList);
        $stationUpdateCount = 0;
        $fileProcessCount = 0;
        $lastLogUpdate = 0;
        $processedFiles = []; // 跟踪已处理的文件
        $originalDirs = []; // 跟踪原始目录
        
        // 批处理参数
        $batchSize = 100; // 每批处理100个任务
        $sleepTime = 1;   // 每批之间休息1秒
        
        // 避免空的任务列表
        if ($totalCount == 0) {
            $logs[] = "警告: 没有找到需要处理的任务";
            $this->updateProgress(['logs' => $logs]);
            return false;
        }
        
        // 按批次处理任务
        for ($i = 0; $i < $totalCount; $i += $batchSize) {
            $batchEnd = min($i + $batchSize, $totalCount);
            $logs[] = "开始处理第 " . ($i+1) . " 到 " . $batchEnd . " 个任务...";
            $this->updateProgress(['logs' => $logs]);
            $logs = [];
            
            // 处理当前批次
            for ($j = $i; $j < $batchEnd; $j++) {
                $task = $taskList[$j];
                $processedCount++;
                $percentage = intval(($processedCount / $totalCount) * 100);
                
                try {
                    if ($task['type'] === 'update_install_date') {
                        // 更新电站安装日期
                        Db::name('station')
                            ->where('department_id', $task['dept_id'])
                            ->update(['install_date' => $task['install_date']]);
                        
                        $stationUpdateCount++;
                        $logs[] = "已更新部门 {$task['dept_name']}(ID: {$task['dept_id']}) 的电站安装日期为 {$task['install_date']}";
                        
                    } else if ($task['type'] === 'process_file') {
                        // 处理文件
                        $currentFile = basename($task['source_file']);
                        
                        // 更新进度
                        $this->updateProgress([
                            'processed' => $processedCount,
                            'percentage' => $percentage,
                            'currentFile' => $currentFile
                        ]);
                        
                        // 确保目标目录存在
                        if (!is_dir($task['target_path'])) {
                            mkdir($task['target_path'], 0755, true);
                        }
                        
                        // 复制文件
                        $newFilePath = $task['target_path'] . '/' . $task['new_file_name'];
                        if (copy($task['source_file'], $newFilePath)) {
                            // 记录此文件已处理，以及所在目录
                            $processedFiles[] = $task['source_file'];
                            $dir = dirname($task['source_file']);
                            if (!in_array($dir, $originalDirs)) {
                                $originalDirs[] = $dir;
                            }
                            
                            // 从路径中去掉"uploads/"前缀，只保留相对路径
                            $dbFilePath = str_replace('uploads/', '', $task['target_dir']) . '/' . $task['new_file_name'];
                            
                            // 确保name字段不包含任何路径，直接使用original_file_name
                            // 因为original_file_name已经在addFileTask方法中确保是纯文件名
                            $pureName = $task['original_file_name'];
                            
                            // 添加档案记录
                            $archiveData = [
                                'station_id' => $task['station_id'],
                                'name' => $pureName, // 使用已清理的纯文件名
                                'file_path' => $dbFilePath,
                                'file_size' => $task['file_size'],
                                'file_type' => $task['file_type'],
                                'create_at' => date('Y-m-d H:i:s')
                            ];
                            
                            Db::name('station_archive')->insert($archiveData);
                            $fileProcessCount++;
                            
                            // 每10个文件或总数的10%更新一次进度日志，避免日志过多
                            $updateInterval = max(10, intval($totalCount * 0.1));
                            if ($processedCount % $updateInterval == 0 || $processedCount == $totalCount) {
                                $logs[] = "已处理 {$processedCount}/{$totalCount} 个任务，当前处理: {$currentFile}";
                            }
                        } else {
                            $logs[] = "错误: 无法复制文件 {$currentFile} 到新位置";
                        }
                    }
                    
                    // 内存管理 - 每100个文件后清理一次内存
                    if ($processedCount % 100 == 0) {
                        gc_collect_cycles();
                    }
                    
                } catch (\Exception $e) {
                    Log::error("处理任务失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                    $logs[] = "错误: 处理任务 " . ($task['type'] == 'process_file' ? basename($task['source_file']) : $task['dept_name']) . " 失败: " . $e->getMessage();
                    $this->updateProgress(['logs' => $logs]);
                    $logs = [];
                }
            }
            
            // 每批次处理完成后更新进度日志
            $this->updateProgress([
                'processed' => $processedCount,
                'percentage' => $percentage,
                'logs' => $logs
            ]);
            $logs = [];
            
            // 每批次间休眠，避免服务器负载过高
            if ($i + $batchSize < $totalCount) {
                Log::info("批次处理完成，休眠 {$sleepTime} 秒...");
                sleep($sleepTime);
            }
        }
        
        // 处理完成后，删除选定目录内的所有文件和目录
        if ($fileProcessCount > 0) {
            try {
                $logs[] = "开始清理选定目录内的文件...";
                $this->updateProgress(['logs' => $logs]);
                
                // 获取原始目录 - 选定的档案目录路径
                $oldArchPath = root_path() . 'public/oldarch/' . $this->archiveDir;
                
                if (is_dir($oldArchPath)) {
                    // 删除目录内所有内容，但保留目录本身
                    $this->emptyDirectory($oldArchPath);
                    $logs[] = "选定目录内容已清理完成，保留了空的目录结构";
                } else {
                    $logs[] = "警告: 找不到选定的原始目录: " . $oldArchPath;
                }
            } catch (\Exception $e) {
                Log::error("清理原始文件失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                $logs[] = "警告: 清理原始文件失败: " . $e->getMessage();
            }
        }
        
        // 完成
        $finalLogs = ["档案处理完成，共处理 {$stationUpdateCount} 个部门安装日期更新和 {$fileProcessCount} 个档案文件"];
        $this->updateProgress([
            'finished' => true,
            'success' => true,
            'percentage' => 100,
            'processed' => $totalCount,
            'logs' => array_merge($logs, $finalLogs)
        ]);
        
        // 清理内存
        gc_collect_cycles();
        
        return true;
    }
    
    /**
     * 清空目录但保留目录本身
     */
    protected function emptyDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $path = $dir . '/' . $file;
                
                if (is_dir($path)) {
                    // 递归删除子目录
                    $this->emptyDirectory($path);
                    // 删除空目录
                    rmdir($path);
                } else {
                    // 删除文件
                    unlink($path);
                }
            }
        }
        
        return true;
    }
    
    /**
     * 获取目录列表
     */
    protected function getDirectories($path)
    {
        $dirs = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..' && is_dir($path . '/' . $file)) {
                        $dirs[] = $file;
                    }
                }
                closedir($handle);
            }
        }
        
        return $dirs;
    }
    
    /**
     * 获取文件列表
     */
    protected function getFiles($path)
    {
        $files = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..') {
                        if (is_file($path . '/' . $file)) {
                            $files[] = $file;
                        } elseif (is_dir($path . '/' . $file)) {
                            // 如果是子目录，则递归获取
                            $subDirFiles = $this->getFilesRecursive($path . '/' . $file, $file);
                            $files = array_merge($files, $subDirFiles);
                        }
                    }
                }
                closedir($handle);
            }
        }
        
        return $files;
    }
    
    /**
     * 递归获取所有文件，包括子目录
     * @param string $path 当前目录路径
     * @param string $relativePath 相对于起始目录的路径
     * @return array 文件路径列表，相对于起始目录
     */
    protected function getFilesRecursive($path, $relativePath = '')
    {
        $files = [];
        
        if (is_dir($path)) {
            $handle = opendir($path);
            
            if ($handle) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != '..') {
                        $filePath = $path . '/' . $file;
                        $relativeFilePath = $relativePath ? $relativePath . '/' . $file : $file;
                        
                        if (is_file($filePath)) {
                            $files[] = $relativeFilePath;
                        } elseif (is_dir($filePath)) {
                            // 递归处理子目录
                            $subDirFiles = $this->getFilesRecursive($filePath, $relativeFilePath);
                            $files = array_merge($files, $subDirFiles);
                        }
                    }
                }
                closedir($handle);
            }
        }
        
        return $files;
    }
    
    /**
     * 更新进度信息
     */
    protected function updateProgress(array $data)
    {
        try {
            $progress = Cache::get($this->progressKey, []);
            
            // 如果缓存为空，重新初始化进度数据
            if (empty($progress)) {
                Log::warning('进度缓存数据为空，正在重新初始化');
                $progress = [
                    'total' => 0,
                    'processed' => 0,
                    'percentage' => 0,
                    'currentFile' => '',
                    'finished' => false,
                    'success' => false,
                    'logs' => ['进度数据已重新初始化...']
                ];
            }
            
            // 合并数据
            foreach ($data as $key => $value) {
                if ($key === 'logs' && !empty($progress['logs'])) {
                    // 日志特殊处理，合并而不是替换
                    if (is_array($value)) {
                        $progress['logs'] = array_merge($progress['logs'], array_diff($value, $progress['logs']));
                    } else {
                        $progress['logs'][] = $value;
                    }
                    
                    // 控制日志数量，避免过大
                    if (count($progress['logs']) > 200) {
                        $progress['logs'] = array_slice($progress['logs'], -200);
                    }
                } else {
                    $progress[$key] = $value;
                }
            }
            
            // 添加当前时间戳，便于调试
            $progress['last_update'] = date('Y-m-d H:i:s');
            
            // 保存更新后的进度数据
            $result = Cache::set($this->progressKey, $progress, 7200); // 增加缓存时间到2小时
            if (!$result) {
                Log::error('更新进度缓存失败: ' . json_encode($progress));
            }
            
            // 如果有任务ID，同步更新任务表进度
            if (isset($progress['task_id']) && $progress['task_id'] > 0) {
                $updateData = [
                    'progress' => $progress['percentage'] ?? 0
                ];
                
                // 如果任务完成，更新状态
                if (isset($progress['finished']) && $progress['finished']) {
                    $updateData['status'] = isset($progress['success']) && $progress['success'] ? 'completed' : 'failed';
                    $updateData['end_time'] = date('Y-m-d H:i:s');
                    
                    if (isset($progress['success']) && !$progress['success']) {
                        $lastError = end($progress['logs']);
                        if ($lastError) {
                            $updateData['error'] = $lastError;
                        }
                    } else {
                        $updateData['result'] = isset($progress['processed']) ? "处理完成，共处理 {$progress['processed']} 个文件" : "处理完成";
                    }
                }
                
                Db::name('task_queue')->where('id', $progress['task_id'])->update($updateData);
            }
            
            return $progress;
        } catch (\Exception $e) {
            Log::error('更新进度数据异常: ' . $e->getMessage());
            return false;
        }
    }
}
