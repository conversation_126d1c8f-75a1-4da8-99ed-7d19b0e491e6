// 资金归集管理组件
const FundCollection = {
    template: `
        <div class="fund-collection">
            <h2>资金归集管理</h2>
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-form :inline="true" :model="filterForm">
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="filterForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="联系人姓名">
                        <el-input v-model="filterForm.contact_name" placeholder="请输入联系人姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="总序号">
                        <el-input v-model="filterForm.total_serial" placeholder="请输入总序号"></el-input>
                    </el-form-item>
                    <el-form-item label="归集方式">
                        <el-select v-model="filterForm.collection_type" placeholder="请选择归集方式">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="POS扣款" value="pos"></el-option>
                            <el-option label="临商银行" value="临商"></el-option>
                            <el-option label="国网" value="国网"></el-option>
                            <el-option label="存现" value="存现"></el-option>
                            <el-option label="其他" value="其他"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button @click="resetFilter">重置</el-button>
                        <el-button type="success" @click="showFundDialog">登记归集</el-button>
                        <el-button type="info" @click="downloadTemplate">下载模板</el-button>
                        <el-button type="warning" @click="showImportDialog">导入数据</el-button>
                        <el-button type="warning" @click="showCustomImportDialog">导入自定义归集表</el-button>
                        <el-button type="primary" @click="handleExport">导出数据</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 数据展示区域 -->
            <div class="data-section">
                <!-- 直接显示数据列表 -->
                <div class="table-section">
                    <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
                        <el-table-column prop="total_serial" label="总序号" width="100"></el-table-column>
                        <el-table-column prop="gongwang_account" label="国网号" width="150"></el-table-column>
                        <el-table-column prop="contact_name" label="户名" width="100"></el-table-column>
                        <el-table-column prop="collection_date" label="归集日期" width="120"></el-table-column>
                        <el-table-column prop="collection_type" label="归集方式" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getCollectionTypeColor(scope.row.collection_type)">
                                    {{ scope.row.collection_type }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="amount" label="归集金额" width="120">
                            <template slot-scope="scope">
                                {{ formatCurrency(scope.row.amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注说明" min-width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button type="text" size="small" @click="handleDelete(scope.row)" style="color: #F56C6C;">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalItems">
                        </el-pagination>
                    </div>
                </div>
            </div>

            <!-- 资金归集登记对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="总序号" prop="total_serial">
                        <el-autocomplete
                            ref="totalSerialInput"
                            v-model="form.total_serial"
                            clearable
                            :fetch-suggestions="queryStationsByTotalSerial"
                            placeholder="请输入总序号"
                            @select="handleStationSelectByTotalSerial"
                            @blur="handleSerialBlur"
                            value-key="total_serial"
                            :trigger-on-focus="true"
                            popper-class="station-autocomplete"
                            style="width: 100%;"
                        >
                            <template slot-scope="{ item }">
                                <div style="padding: 5px 0; display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-weight: bold;">总序号: {{ item.total_serial }}</span>
                                    <span style="color: #666; margin-left: 10px;">联系人: {{ item.contact_name || '无联系人' }}</span>
                                </div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>
                    <el-form-item label="国网号" prop="gongwang_account">
                        <el-input v-model="form.gongwang_account" placeholder="请输入国网号" @blur="handleAccountBlur"></el-input>
                    </el-form-item>
                    <el-form-item label="户名" prop="contact_name">
                        <el-input v-model="form.contact_name" placeholder="请输入户名"></el-input>
                    </el-form-item>
                    <el-form-item label="归集日期" prop="collection_date">
                        <el-date-picker
                            v-model="form.collection_date"
                            type="date"
                            placeholder="选择归集日期"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="归集方式" prop="collection_type">
                        <el-select v-model="form.collection_type" placeholder="请选择归集方式" style="width: 100%;">
                            <el-option label="POS扣款" value="pos"></el-option>
                            <el-option label="临商银行" value="临商"></el-option>
                            <el-option label="国网" value="国网"></el-option>
                            <el-option label="存现" value="存现"></el-option>
                            <el-option label="其他" value="其他"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="归集金额" prop="amount">
                        <el-input v-model="form.amount" type="number" placeholder="请输入金额" @blur="handleAmountBlur">
                            <template slot="append">元</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="备注说明" prop="remark">
                        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 电站选择对话框 -->
            <el-dialog title="选择电站" :visible.sync="stationDialogVisible" width="800px">
                <div style="margin-bottom: 20px;">
                    <el-input 
                        v-model="stationSearchKeyword" 
                        placeholder="搜索电站号/国网号/总序号" 
                        style="width: 300px;"
                        @keyup.enter.native="searchStations">
                        <el-button slot="append" icon="el-icon-search" @click="searchStations"></el-button>
                    </el-input>
                </div>
                <el-table :data="stationList" border height="400px" @row-click="selectStation">
                    <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                    <el-table-column prop="total_serial" label="总序号" width="100"></el-table-column>
                    <el-table-column prop="gongwang_account" label="国网号" width="150"></el-table-column>
                    <el-table-column prop="contact_name" label="户名" width="120"></el-table-column>
                    <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip></el-table-column>
                </el-table>
            </el-dialog>

            <!-- 标准导入对话框 -->
            <el-dialog title="导入资金归集数据" :visible.sync="importDialogVisible" width="500px">
                <div class="import-info">
                    <p>请选择标准格式的Excel文件进行导入:</p>
                    <ul>
                        <li>请先下载导入模板，按照模板格式填写数据</li>
                        <li>总序号和户号必须与系统中的电站信息一致</li>
                        <li>归集方式可选：pos、临商、国网、存现、其他</li>
                        <li>日期格式：YYYY-MM-DD</li>
                    </ul>
                </div>
                <el-upload
                    class="upload-demo"
                    action="/finance/fund/import"
                    :headers="uploadHeaders"
                    :on-success="handleImportSuccess"
                    :on-error="handleImportError"
                    :before-upload="beforeImportUpload"
                    :file-list="importFileList">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">
                        仅支持Excel格式文件(.xlsx, .xls)
                    </div>
                </el-upload>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="importDialogVisible = false">取 消</el-button>
                </span>
            </el-dialog>

            <!-- 自定义导入对话框 -->
            <el-dialog title="导入自定义归集表" :visible.sync="customImportDialogVisible" width="500px">
                <div class="custom-import-info">
                    <p>请选择您的自定义格式Excel文件进行导入:</p>
                    <ul>
                        <li>该功能支持导入特定格式的资金归集明细表</li>
                        <li>支持从表格标题中提取部门和年份信息</li>
                        <li>自动识别带有"X月pos/临商/国网/存现"等格式的列为归集数据</li>
                        <li>支持自动创建或更新电站信息</li>
                    </ul>
                </div>
                <el-upload
                    class="upload-demo"
                    action="/finance/fund/import-custom"
                    :headers="uploadHeaders"
                    :on-success="handleCustomImportSuccess"
                    :on-error="handleImportError"
                    :before-upload="beforeImportUpload"
                    :file-list="fileList">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">
                        Excel格式，第一行为标题("XX部20XX年项目归集资金明细表")，第二行为字段名
                    </div>
                </el-upload>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="customImportDialogVisible = false">取 消</el-button>
                </span>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            // 过滤表单
            filterForm: {
                dateRange: [],
                contact_name: '',
                total_serial: '',
                collection_type: ''
            },
            // 表格数据
            tableData: [],
            tableLoading: false,
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            
            // 对话框控制
            dialogVisible: false,
            dialogTitle: '登记资金归集',
            importDialogVisible: false,
            customImportDialogVisible: false,
            
            // 表单数据
            form: {
                station_id: null,
                gongwang_account: '',
                total_serial: '',
                contact_name: '',
                collection_date: '',
                collection_type: '',
                amount: '',
                remark: ''
            },
            rules: {
                total_serial: [
                    { required: true, message: '请输入总序号', trigger: 'blur' }
                ],
                gongwang_account: [
                    { required: true, message: '请输入国网号', trigger: 'blur' }
                ],
                contact_name: [
                    { required: true, message: '请输入户名', trigger: 'blur' }
                ],
                collection_date: [
                    { required: true, message: '请选择归集日期', trigger: 'change' }
                ],
                collection_type: [
                    { required: true, message: '请选择归集方式', trigger: 'change' }
                ],
                amount: [
                    { required: true, message: '请输入归集金额', trigger: 'blur' },
                    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
                ]
            },
            
            // 电站选择
            stationDialogVisible: false,
            stationList: [],
            stationSearchKeyword: '',
            selectedStation: null,
            stationOptions: [], // 用于自动完成的电站选项
            
            // 上传相关
            uploadHeaders: {
                Authorization: 'Bearer ' + localStorage.getItem('token')
            },
            loading: false,
            importLoading: false,
            submitLoading: false,
            deleteLoading: false,
            importFileList: [],
            fileList: []
        };
    },
    mounted() {
        this.fetchData();
        this.fetchStationOptions(); // 初始化电站选项数据
    },
    methods: {
        async fetchData() {
            this.tableLoading = true;
            try {
                const params = {
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                // 添加筛选条件
                if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                    params.start_date = this.filterForm.dateRange[0];
                    params.end_date = this.filterForm.dateRange[1];
                }
                if (this.filterForm.contact_name) {
                    params.contact_name = this.filterForm.contact_name;
                }
                if (this.filterForm.total_serial) {
                    params.total_serial = this.filterForm.total_serial;
                }
                if (this.filterForm.collection_type) {
                    params.collection_type = this.filterForm.collection_type;
                }
                
                const response = await this.$http.get('/finance/fund/list', { params });
                
                if (response.data.code === 200) {
                    this.tableData = response.data.data.items || [];
                    this.totalItems = response.data.data.total || 0;
                } else {
                    this.$message.error(response.data.message || '获取资金归集数据失败');
                }
            } catch (error) {
                console.error('获取资金归集数据异常:', error);
                this.$message.error('获取资金归集数据失败: ' + (error.message || '未知错误'));
            } finally {
                this.tableLoading = false;
            }
        },
        
        getCollectionTypeColor(type) {
            const colors = {
                'pos': 'success',
                '临商': 'primary',
                '国网': 'warning',
                '存现': 'info',
                '其他': ''
            };
            return colors[type] || '';
        },
        
        showFundDialog() {
            this.dialogTitle = '登记资金归集';
            this.form = {
                station_id: null,
                gongwang_account: '',
                total_serial: '',
                contact_name: '',
                collection_date: '',
                collection_type: '',
                amount: '',
                remark: ''
            };
            this.selectedStation = null;
            this.dialogVisible = true;
        },
        
        handleEdit(row) {
            this.dialogTitle = '编辑资金归集';
            this.form = { ...row };
            this.selectedStation = {
                id: row.station_id,
                total_serial: row.total_serial,
                gongwang_account: row.gongwang_account,
                contact_name: row.contact_name
            };
            this.dialogVisible = true;
        },
        
        async handleDelete(row) {
            try {
                await this.$confirm('确定要删除这条资金归集记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                this.deleteLoading = true;
                const response = await this.$http.delete(`/finance/fund/${row.id}`);
                
                if (response.data.code === 200) {
                    this.$message.success('删除成功');
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除资金归集记录异常:', error);
                    this.$message.error('删除失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.deleteLoading = false;
            }
        },
        
        async submitForm() {
            try {
                await this.$refs.form.validate();
                
                this.submitLoading = true;
                let response;
                
                if (this.form.id) {
                    response = await this.$http.put(`/finance/fund/${this.form.id}`, this.form);
                } else {
                    // 使用新的register接口进行登记
                    const registerData = {
                        total_serial: this.form.total_serial,
                        gongwang_account: this.form.gongwang_account,
                        contact_name: this.form.contact_name,
                        collection_date: this.form.collection_date,
                        collection_type: this.form.collection_type,
                        amount: parseFloat(this.form.amount),
                        remark: this.form.remark
                    };
                    response = await this.$http.post('/finance/fund/register', registerData);
                }
                
                if (response.data.code === 200) {
                    this.$message.success(this.form.id ? '更新成功' : '登记成功');
                    this.dialogVisible = false;
                    this.fetchData();
                } else {
                    this.$message.error(response.data.message || (this.form.id ? '更新失败' : '登记失败'));
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('提交资金归集表单异常:', error);
                    this.$message.error('提交失败: ' + (error.message || '未知错误'));
                }
            } finally {
                this.submitLoading = false;
            }
        },
        
        async showStationSelect() {
            this.stationDialogVisible = true;
            await this.searchStations();
        },
        
        async searchStations() {
            this.stationLoading = true;
            try {
                const params = {
                    keyword: this.stationSearchKeyword,
                    page: 1,
                    limit: 10
                };
                
                const response = await this.$http.get('/station/search', { params });
                
                if (response.data.code === 200) {
                    this.stationList = response.data.data.items || [];
                } else {
                    this.$message.error(response.data.message || '获取电站列表失败');
                }
            } catch (error) {
                console.error('获取电站列表异常:', error);
                this.$message.error('获取电站列表失败: ' + (error.message || '未知错误'));
            } finally {
                this.stationLoading = false;
            }
        },
        
        selectStation(row) {
            this.selectedStation = row;
            this.form.station_id = row.id;
            this.form.gongwang_account = row.gongwang_account;
            this.form.total_serial = row.total_serial;
            this.form.contact_name = row.contact_name;
            this.stationDialogVisible = false;
        },
        
        handleSearch() {
            this.currentPage = 1;
            this.fetchData();
        },
        
        resetFilter() {
            this.filterForm = {
                dateRange: [],
                contact_name: '',
                total_serial: '',
                collection_type: ''
            };
            this.currentPage = 1;
            this.fetchData();
        },
        
        resetForm() {
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },
        
        handleSizeChange(val) {
            this.pageSize = val;
            this.fetchData();
        },
        
        handleCurrentChange(val) {
            this.currentPage = val;
            this.fetchData();
        },
        
        formatCurrency(value) {
            if (!value) return '0.00';
            
            // 转换为数字并保留2位小数
            const num = parseFloat(value);
            if (isNaN(num)) return '0.00';
            
            return num.toFixed(2);
        },
        
        handleAmountBlur() {
            if (this.form.amount) {
                this.form.amount = parseFloat(this.form.amount).toFixed(2);
            }
        },

        // 处理总序号输入，自动查找对应电站信息
        async handleSerialBlur() {
            if (this.form.total_serial && this.form.gongwang_account) {
                await this.autoFillStationInfo();
            }
        },

        // 处理国网号输入，自动查找对应电站信息
        async handleAccountBlur() {
            if (this.form.total_serial && this.form.gongwang_account) {
                await this.autoFillStationInfo();
            }
        },

        // 自动填充电站信息
        async autoFillStationInfo() {
            try {
                const params = {
                    total_serial: this.form.total_serial,
                    gongwang_account: this.form.gongwang_account
                };

                const response = await this.$http.get('/station/search', { params });

                if (response.data.code === 200 && response.data.data.items.length > 0) {
                    const station = response.data.data.items[0];
                    this.form.contact_name = station.contact_name || '';
                    this.form.station_id = station.id;
                    this.selectedStation = station;
                }
            } catch (error) {
                console.error('自动填充电站信息失败:', error);
            }
        },

        // 获取电站选项数据
        async fetchStationOptions() {
            // 如果已经加载过电站数据且有数据，则直接返回缓存数据
            if (this.stationOptions && this.stationOptions.length > 0) {
                console.log('使用缓存的电站数据，跳过API请求');
                return this.stationOptions;
            }

            console.log('开始获取电站数据...');
            try {
                const response = await this.$http.get('/station/problem/index');
                if (response.data.code === 200) {
                    console.log('获取的电站数据示例:', response.data.data[0]);
                    console.log('API返回的电站数据条数:', response.data.data.length);

                    // 确保字段名称一致性
                    this.stationOptions = response.data.data.map(station => {
                        return {
                            ...station,
                            // 统一字段名称，但不替换值
                            total_serial: station.total_serial || '',
                            gongwang_account: station.gongwang_account || station.gongwangAccount || '',
                            contact_name: station.contact_name || station.contactName || '',
                            contact_phone: station.contact_phone || station.contactPhone || ''
                        };
                    });

                    console.log('处理后的电站数据示例:', this.stationOptions[0]);
                    console.log('电站总数:', this.stationOptions.length);

                    // 验证数据中包含total_serial字段的比例
                    const stationsWithTotalSerial = this.stationOptions.filter(station =>
                        station.total_serial && station.total_serial.trim() !== ''
                    ).length;

                    console.log(`包含有效总序号的电站数量: ${stationsWithTotalSerial}/${this.stationOptions.length}`);

                    return this.stationOptions;
                } else {
                    console.error('获取电站数据失败:', response.data.message);
                    this.stationOptions = [];
                    return [];
                }
            } catch (error) {
                console.error('获取电站列表失败:', error);
                this.stationOptions = [];
                return [];
            }
        },

        // 根据总序号查询电站
        queryStationsByTotalSerial(queryString, callback) {
            console.log('查询电站选项，关键词:', queryString);
            console.log('当前所有电站选项数量:', this.stationOptions.length);

            // 添加调试信息验证数据结构
            if (this.stationOptions.length > 0) {
                console.log('第一条电站数据total_serial字段值:', this.stationOptions[0].total_serial);
                console.log('第一条电站数据完整信息:', JSON.stringify(this.stationOptions[0]));
            }

            // 尝试从API重新获取电站数据
            if (this.stationOptions.length === 0) {
                console.log('电站选项为空，重新获取数据');
                this.fetchStationOptions().then(stations => {
                    console.log('重新获取的电站数据数量:', stations.length);
                    this._processQueryResults(queryString, callback);
                }).catch(err => {
                    console.error('重新获取电站数据失败:', err);
                    callback([]);
                });
            } else {
                this._processQueryResults(queryString, callback);
            }
        },

        // 处理查询结果
        _processQueryResults(queryString, callback) {
            // 过滤出匹配的电站，只保留有实际总序号的项目
            let results = [];

            // 首先过滤出有效的total_serial的电站
            const validStations = this.stationOptions.filter(station =>
                station.total_serial && station.total_serial.trim() !== ''
            );

            console.log('有效总序号的电站数量:', validStations.length);

            if (!queryString) {
                // 如果没有查询字符串，显示所有有效总序号的选项
                results = [...validStations];
                console.log('未输入查询条件，返回所有有效总序号选项');
            } else {
                // 有查询字符串时，在有效总序号中进行过滤
                results = validStations.filter(station => {
                    const serialStr = station.total_serial.toString();
                    return serialStr.includes(queryString);
                });
                console.log(`根据条件"${queryString}"过滤后的电站数量:`, results.length);
            }

            // 去除重复的总序号
            const uniqueKeys = new Set();
            results = results.filter(station => {
                if (uniqueKeys.has(station.total_serial)) {
                    return false;
                }
                uniqueKeys.add(station.total_serial);
                return true;
            });

            // 规范化结果对象属性名
            results = results.map(station => {
                // 创建一个新对象避免修改原始数据
                return {
                    id: station.id,
                    total_serial: station.total_serial,
                    gongwang_account: station.gongwang_account || '',
                    // 尝试多种可能的属性名
                    contact_name: station.contact_name || station.contactName || '',
                    contact_phone: station.contact_phone || station.contactPhone || '',
                    value: station.total_serial // 用于显示在输入框中的值
                };
            });

            console.log('处理后的结果数据数量:', results.length);
            if (results.length > 0) {
                console.log('处理后的第一条数据:', results[0]);
            }

            // 限制返回10条结果
            const finalResults = results.slice(0, 10);
            console.log('最终返回的结果数量:', finalResults.length);
            callback(finalResults);
        },

        // 选择电站
        handleStationSelectByTotalSerial(item) {
            console.log('选中的电站数据:', item);

            // 根据选中的总序号找到完整的电站数据
            const selectedStation = this.stationOptions.find(station =>
                station.total_serial === item.total_serial || station.id === item.id
            );

            console.log('找到完整电站数据:', selectedStation);

            if (selectedStation) {
                this.form.station_id = selectedStation.id;
                // 设置表单字段
                this.form.total_serial = selectedStation.total_serial;
                this.form.gongwang_account = selectedStation.gongwang_account || '';

                // 填充联系人信息
                if (selectedStation.contact_name) {
                    this.form.contact_name = selectedStation.contact_name;
                } else if (selectedStation.contactName) {
                    this.form.contact_name = selectedStation.contactName;
                } else {
                    this.form.contact_name = '';
                }

                this.selectedStation = selectedStation;

                console.log('填充后的表单数据:', this.form);
            } else {
                console.warn('未找到选中的电站数据');
            }
        },
        
        handleExport() {
            const params = {};
            
            if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
                params.start_date = this.filterForm.dateRange[0];
                params.end_date = this.filterForm.dateRange[1];
            }
            if (this.filterForm.contact_name) {
                params.contact_name = this.filterForm.contact_name;
            }
            if (this.filterForm.total_serial) {
                params.total_serial = this.filterForm.total_serial;
            }
            if (this.filterForm.collection_type) {
                params.collection_type = this.filterForm.collection_type;
            }
            
            const queryString = Object.keys(params)
                .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
                .join('&');
            
            window.open(`/finance/fund/export?${queryString}`, '_blank');
        },
        
        downloadTemplate() {
            window.open('/finance/fund/template', '_blank');
        },

        showImportDialog() {
            this.importDialogVisible = true;
            this.importFileList = [];
        },

        showCustomImportDialog() {
            this.customImportDialogVisible = true;
            this.fileList = [];
        },

        handleImportSuccess(response) {
            if (response.code === 200) {
                this.$message.success(`导入完成！成功：${response.data.success}条，失败：${response.data.fail}条`);
                if (response.data.errors && response.data.errors.length > 0) {
                    this.$alert(response.data.errors.join('\n'), '导入错误详情', {
                        confirmButtonText: '确定',
                        type: 'warning'
                    });
                }
                this.importDialogVisible = false;
                this.fetchData();
            } else {
                this.$message.error(response.message || '导入失败');
            }
            this.importFileList = [];
        },

        handleImportError(error) {
            this.$message.error('导入失败: ' + error.message);
            this.importFileList = [];
        },

        beforeImportUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                           file.type === 'application/vnd.ms-excel';
            if (!isExcel) {
                this.$message.error('只能上传Excel文件!');
                return false;
            }
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过 10MB!');
                return false;
            }
            return true;
        },
        
        handleCustomImportSuccess(response) {
            if (response.code === 200) {
                const data = response.data;
                let message = `导入完成，总计处理${data.success + data.fail}条数据。`;
                message += `成功：${data.success}条`;
                
                if (data.fail > 0) {
                    message += `，失败：${data.fail}条`;
                    // 如果有错误信息，在控制台输出详细错误
                    if (data.errors && data.errors.length > 0) {
                        console.error('导入错误详情:', data.errors);
                        // 显示前5条错误信息
                        const errorTips = data.errors.slice(0, 5).join('\n');
                        this.$notify({
                            title: '部分数据导入失败',
                            message: errorTips,
                            type: 'warning',
                            duration: 10000
                        });
                    }
                }
                
                // 显示导入详细结果
                this.$notify({
                    title: '导入结果详情',
                    dangerouslyUseHTMLString: true,
                    message: `<strong>成功:</strong> ${data.success}条<br/>
                              <strong>失败:</strong> ${data.fail}条<br/>
                              <strong>新增电站:</strong> ${data.stations_created}个<br/>
                              <strong>更新电站:</strong> ${data.stations_updated}个<br/>
                              <strong>新增归集记录:</strong> ${data.collections_created}条<br/>
                              <strong>部门:</strong> ${data.department}<br/>
                              <strong>年份:</strong> ${data.year}`,
                    type: data.success > 0 ? 'success' : 'info',
                    duration: 8000
                });
                
                // 如果包含调试日志，显示在控制台并弹出一个详细对话框
                if (data.debug_logs && data.debug_logs.length > 0) {
                    console.log('导入调试日志:', data.debug_logs);
                    
                    // 只有在有错误时才自动显示调试信息
                    if (data.fail > 0) {
                        this.$alert(
                            `<div style="max-height: 400px; overflow-y: auto; font-family: monospace; white-space: pre-wrap;">${data.debug_logs.join('<br>')}</div>`,
                            '导入调试日志',
                            {
                                dangerouslyUseHTMLString: true,
                                confirmButtonText: '确定',
                                callback: () => {}
                            }
                        );
                    }
                }
                
                this.$message.success(message);
                this.customImportDialogVisible = false;
                this.fetchData();
            } else {
                this.$message.error(response.message || '导入失败');
            }
        },
        
        handleImportError(error) {
            console.error('导入失败:', error);
            this.$message.error('导入失败: ' + (error.message || '未知错误'));
        },
        
        beforeImportUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                           file.type === 'application/vnd.ms-excel';
            const isLt5M = file.size / 1024 / 1024 < 5;
            
            if (!isExcel) {
                this.$message.error('请上传Excel文件！');
                return false;
            }
            if (!isLt5M) {
                this.$message.error('文件大小不能超过5MB！');
                return false;
            }
            return true;
        }
    }
};

// 修改导出方式
export default FundCollection;

// 添加自动完成样式
const style = document.createElement('style');
style.textContent = `
.station-autocomplete .el-autocomplete-suggestion__wrap {
    max-height: 300px;
}

.station-autocomplete .el-autocomplete-suggestion__list {
    padding: 0;
}

.station-autocomplete .el-autocomplete-suggestion__list li {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
}

.station-autocomplete .el-autocomplete-suggestion__list li:hover {
    background-color: #f5f7fa;
}

.station-autocomplete .el-autocomplete-suggestion__list li:last-child {
    border-bottom: none;
}
`;
document.head.appendChild(style);