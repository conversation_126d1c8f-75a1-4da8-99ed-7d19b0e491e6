<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateTaskQueueTable extends Migrator
{
    /**
     * 创建任务队列表
     */
    public function change()
    {
        $table = $this->table('task_queue', ['engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci']);
        $table->addColumn('task_type', 'string', ['limit' => 50, 'comment' => '任务类型'])
              ->addColumn('task_data', 'text', ['comment' => '任务数据(JSON)'])
              ->addColumn('status', 'string', ['limit' => 20, 'default' => 'pending', 'comment' => '任务状态(pending/processing/completed/failed)'])
              ->addColumn('progress', 'integer', ['default' => 0, 'comment' => '进度百分比'])
              ->addColumn('result', 'text', ['null' => true, 'comment' => '任务结果'])
              ->addColumn('error', 'text', ['null' => true, 'comment' => '错误信息'])
              ->addColumn('create_time', 'datetime', ['comment' => '创建时间'])
              ->addColumn('start_time', 'datetime', ['null' => true, 'comment' => '开始时间'])
              ->addColumn('end_time', 'datetime', ['null' => true, 'comment' => '结束时间'])
              ->addIndex(['task_type', 'status'])
              ->create();
    }
} 