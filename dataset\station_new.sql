-- 禁用外键约束
SET FOREIGN_KEY_CHECKS = 0;

-- 删除旧的电站表
DROP TABLE IF EXISTS `sp_station`;

-- 创建新的电站表
CREATE TABLE `sp_station` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '电站名称',
    `address` varchar(255) NOT NULL COMMENT '电站地址',
    `capacity` decimal(10,2) NOT NULL COMMENT '装机容量(kW)',
    `install_date` date NOT NULL COMMENT '并网日期',
    `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
    `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-正常',
    `department_id` int(11) NOT NULL COMMENT '所属部门ID',
    `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '电站类型：1-合作农户，2-工商业',
    `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `business_type` varchar(50) DEFAULT NULL COMMENT '企业类型（工商业）',
    `contract_number` varchar(50) DEFAULT NULL COMMENT '合同编号',
    `create_at` datetime DEFAULT NULL COMMENT '创建时间',
    `update_at` datetime DEFAULT NULL COMMENT '更新时间',
    `total_serial` varchar(255) DEFAULT NULL COMMENT '总序号',
    `gongwang_account` varchar(255) DEFAULT NULL COMMENT '国网户号',
    `id_card` varchar(255) DEFAULT NULL COMMENT '身份证号',
    `county` varchar(255) DEFAULT NULL COMMENT '县区',
    `component_brand` varchar(255) DEFAULT NULL COMMENT '组件品牌',
    `component_count` int(11) DEFAULT NULL COMMENT '组件数量',
    `component_power` decimal(10,0) DEFAULT NULL COMMENT '组件功率（w）',
    `inverter_brand` varchar(255) DEFAULT NULL COMMENT '逆变器品牌',
    `inverter_serial` varchar(255) DEFAULT NULL COMMENT '逆变器序列号',
    `fixed_income` decimal(10,0) DEFAULT NULL COMMENT '固定收益',
    PRIMARY KEY (`id`),
    KEY `idx_department` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电站信息表';

-- 启用外键约束
SET FOREIGN_KEY_CHECKS = 1;