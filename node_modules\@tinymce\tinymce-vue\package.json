{"name": "@tinymce/tinymce-vue", "version": "6.1.0", "description": "Official TinyMCE Vue 3 Component", "private": false, "repository": {"url": "https://github.com/tinymce/tinymce-vue"}, "main": "lib/cjs/main/ts/index.js", "module": "lib/es2015/main/ts/index.js", "scripts": {"test": "bedrock-auto -b chrome-headless -f src/test/ts/**/*Test.ts", "test-manual": "bedrock -f src/test/ts/**/*Test.ts", "clean": "<PERSON><PERSON><PERSON> lib", "lint": "eslint src/{main,test}/**/*.ts", "build": "yarn run clean && yarn run lint && tsc -p ./tsconfig.es2015.json && tsc -p ./tsconfig.cjs.json && node rollup.build.js", "storybook": "start-storybook -p 6006", "demo": "vite", "build-storybook": "build-storybook", "deploy-storybook": "yarn build-storybook && gh-pages -d ./storybook-static -u 'tiny-bot <<EMAIL>>'"}, "keywords": ["<PERSON><PERSON><PERSON>", "vue", "component"], "author": "Ephox Inc", "license": "MIT", "files": ["lib/*/**", "README.md", "CHANGELOG.md", "LICENSE.txt"], "peerDependencies": {"tinymce": "^7.0.0 || ^6.0.0 || ^5.5.1", "vue": "^3.0.0"}, "peerDependenciesMeta": {"tinymce": {"optional": true}}, "devDependencies": {"@babel/core": "^7.20.2", "@ephox/agar": "^8.0.1", "@ephox/bedrock-client": "^14.1.1", "@ephox/bedrock-server": "^14.1.4", "@ephox/katamari": "^9.1.6", "@ephox/sugar": "^9.3.1", "@storybook/addon-actions": "^6.5.13", "@storybook/addon-essentials": "^6.5.13", "@storybook/addon-links": "^6.5.13", "@storybook/addon-notes": "^5.3.21", "@storybook/builder-webpack5": "^6.5.16", "@storybook/manager-webpack5": "^6.5.16", "@storybook/vue3": "^6.5.13", "@tinymce/beehive-flow": "^0.19.0", "@tinymce/eslint-plugin": "^2.3.1", "@tinymce/miniature": "^6.0.0", "@types/node": "^20.14.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/compiler-sfc": "^3.4.27", "babel-loader": "^8.2.3", "babel-preset-vue": "^2.0.2", "babel-register": "^6.26.0", "css-loader": "^6.2.0", "file-loader": "^6.0.0", "gh-pages": "^6.1.1", "react": "^16.14.0", "react-dom": "^16.14.0", "regenerator-runtime": "^0.13.9", "rimraf": "^5.0.7", "rollup": "^4.18.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-typescript2": "^0.34.1", "rollup-plugin-uglify": "^6.0.0", "tinymce": "^7", "tinymce-4": "npm:tinymce@^4", "tinymce-5": "npm:tinymce@^5", "tinymce-6": "npm:tinymce@^6", "tinymce-7": "npm:tinymce@^7", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "vite": "^5.2.12", "vue": "^3.4.27", "vue-cli-plugin-storybook": "^2.1.0", "vue-loader": "^17.4.2", "vue-router": "^4.3.2", "vue-template-compiler": "^2.7.16", "webpack": "^5.75.0"}}