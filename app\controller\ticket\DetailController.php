<?php
declare (strict_types = 1);
namespace app\controller\ticket;

use app\service\ticket\DetailService;
use think\facade\Request;
use think\facade\Log;

class DetailController extends BaseTicketController
{
    protected $detailService;
    
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->detailService = new DetailService();
    }
    
    /**
     * 获取工单详情
     * @param int $id 工单ID
     */
    public function index($id)
    {
        try {
            trace('开始获取工单详情，ID='.$id, 'debug');
            
            // 验证登录状态
            $loginCheck = $this->checkLogin();
            if ($loginCheck !== true) {
                return $loginCheck;
            }
            
            // 获取当前用户信息
            $userId = session('user_id');
            $isAdmin = $this->isAdmin();
            Log::debug('detailcontroller::获取工单详情，ID='.$id.', 当前用户ID='.$userId.', 是否为管理员='.$isAdmin);
            // 获取工单详情
            $result = $this->detailService->getDetail($id, $userId, $isAdmin);
            
            return json($result);
        } catch (\Exception $e) {
            trace('获取工单详情异常: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => '系统异常：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
} 