<?php
declare (strict_types = 1);

namespace app;

use app\service\LogService;
use think\App;
use think\exception\ValidateException;
use think\Validate;
use think\facade\Session;
use think\facade\Cache;
use think\facade\Request;
use app\model\User;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 当前登录用户
     * @var User
     */
    protected $user;

    /**
     * 构造方法
     * @access public
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 只记录已登录用户的操作
        if (Session::has('user_id')) {
            // 记录操作日志（排除不需要记录的操作）
            $excludeMethods = [
                'index', 
                'read', 
                'loginLogs', 
                'operationLogs',
                'login',
                'logout'
            ];
            
            $action = $this->request->action();
            $method = $this->request->method();
            
            // 只记录修改性质的操作
            if (!in_array($action, $excludeMethods) && in_array($method, ['POST', 'PUT', 'DELETE'])) {
                $this->recordOperationLog();
            }
        }

        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 获取 token
        $token = Request::header('Authorization');
        trace('Authorization token: ' . $token, 'debug');

        if ($token) {
            try {
                // 移除 "Bearer " 前缀
                $token = str_replace('Bearer ', '', $token);
                
                // 从缓存中获取用户ID
                $userInfo = Cache::get('token_' . $token);
                trace('User ID from token: ' . json_encode($userInfo, JSON_UNESCAPED_UNICODE), 'debug');
                
                if ($userInfo) {
                    // 更新token过期时间
                    Cache::set('token_' . $token, $userInfo, 7 * 24 * 3600);
                    
                    // 获取用户信息
                    $this->user = User::where('id', intval($userInfo['id']))
                        ->where('status', 1)  // 只获取启用的用户
                        ->find();
                    
                    if ($this->user) {
                        // 更新 Session
                        Session::set('user_id', $this->user->id);
                        Session::set('username', $this->user->username);
                        Session::set('realname', $this->user->realname);
                        Session::set('last_active', time());
                        
                        trace('User session updated: ' . $this->user->username, 'debug');
                    } else {
                        trace('User not found or disabled: ' . $userId, 'warning');
                        Session::clear();
                    }
                }
            } catch (\Exception $e) {
                trace('Initialize error: ' . $e->getMessage(), 'error');
                Session::clear();
            }
        }
    }

    /**
     * 获取当前登录用户
     */
    protected function getLoginUser()
    {
        return $this->user;
    }

    /**
     * 验证数据
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        if ($batch) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 返回成功结果
     */
    protected function success($data = null, string $message = 'success'): \think\Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 返回错误结果
     */
    protected function error(string $message = 'error', int $code = 500, $data = null): \think\Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 检查是否已登录
     */
    protected function isLogin()
    {
        try {
            $userId = Session::get('user_id');
            $lastActive = Session::get('last_active');
            $token = Request::header('Authorization');
            
            // 记录调试信息
            trace('Login check - UserID: ' . $userId . ', Token: ' . $token, 'debug');
            
            // 如果 session 已过期但有有效 token，尝试恢复 session
            if ((!$userId || !$lastActive || (time() - $lastActive > 7200)) && $token) {
                $this->initialize();
                $userId = Session::get('user_id');
                trace('Session restored from token - New UserID: ' . $userId, 'debug');
            }
            
            // 更新最后活动时间
            if ($userId) {
                Session::set('last_active', time());
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            trace('Login check error: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 强制检查登录状态
     */
    protected function checkLogin()
    {
        if (!$this->isLogin()) {
            trace('Access denied: Not logged in', 'warning');
            return json([
                'code' => 401,
                'message' => '未登录或登录已过期',
                'data' => null
            ]);
        }
        return true;
    }

    /**
     * 记录操作日志
     */
    protected function recordOperationLog()
    {
        try {
            // 获取当前控制器名称作为模块名
            $module = strtolower($this->request->controller());
            
            // 获取操作方法
            $action = $this->request->action();
            
            // 根据请求方法确定操作类型
            $method = $this->request->method();
            switch ($method) {
                case 'POST':
                    $actionType = '新增';
                    break;
                case 'PUT':
                    $actionType = '修改';
                    break;
                case 'DELETE':
                    $actionType = '删除';
                    break;
                default:
                    $actionType = $action;
            }

            // 获取请求参数
            $params = $this->request->param();
            
            // 过滤敏感信息
            if (isset($params['password'])) {
                $params['password'] = '******';
            }
            
            // 获取用户信息 - 首先尝试从当前已加载的用户对象获取
            $userId = 0;
            $username = '';
            
            if ($this->user) {
                $userId = $this->user->id;
                $username = $this->user->username;
                trace('Using user info from user model', 'debug');
            } else {
                // 如果当前对象中没有用户信息，尝试从token获取
                $token = str_replace('Bearer ', '', Request::header('Authorization'));
                if ($token) {
                    $userInfo = \think\facade\Cache::get('token_' . $token);
                    if ($userInfo && isset($userInfo['id']) && isset($userInfo['username'])) {
                        $userId = $userInfo['id'];
                        $username = $userInfo['username'];
                        trace('Using user info from token cache', 'debug');
                    } else {
                        // 最后才尝试从Session获取
                        $userId = Session::get('user_id', 0);
                        $username = Session::get('username', '');
                        trace('Using user info from session', 'debug');
                    }
                }
            }
            
            // 确保用户信息包含在参数中
            $params['_user_id'] = $userId;
            $params['_username'] = $username;

            // 添加调试日志
            trace('Recording operation log...', 'debug');
            trace('Module: ' . $module, 'debug');
            trace('Action: ' . $actionType, 'debug');
            trace('User ID: ' . $userId, 'debug');
            trace('Username: ' . $username, 'debug');
            trace('Params: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'debug');

            // 记录操作日志
            LogService::recordOperation($module, $actionType, $params);

        } catch (\Exception $e) {
            // 记录错误日志
            trace('Record operation log error: ' . $e->getMessage(), 'error');
        }
    }
} 