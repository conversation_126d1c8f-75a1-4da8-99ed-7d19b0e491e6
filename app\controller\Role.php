<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\RoleService;
use app\service\LogService;
use think\App;
use think\facade\Db;
use think\facade\Request;

class Role extends BaseController
{
    protected $roleService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->roleService = new RoleService();
    }

    /**
     * 获取角色列表
     */
    public function index()
    {
        return json($this->roleService->getList());
    }

    /**
     * 创建角色
     */
    public function create()
    {
        $data = $this->request->post();
        $result = $this->roleService->create($data);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('role', 'create', array_merge(
                ['id' => $result['data']['id']],
                $data
            ));
        }
        
        return json($result);
    }

    /**
     * 更新角色
     */
    public function update($id)
    {
        $data = $this->request->put();
        $result = $this->roleService->update($id, $data);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('role', 'update', array_merge(
                ['id' => $id],
                $data
            ));
        }
        
        return json($result);
    }

    /**
     * 删除角色
     */
    public function delete($id)
    {
        $result = $this->roleService->delete($id);
        
        if ($result['code'] === 200) {
            LogService::recordOperation('role', 'delete', ['id' => $id]);
        }
        
        return json($result);
    }

    /**
     * 获取角色权限
     */
    public function getPermissions($id)
    {
        try {
            $permissionIds = Db::name('role_permission')
                ->where('role_id', $id)
                ->column('permission_id');
            
            if ($permissionIds === false) {
                throw new \Exception('获取权限失败');
            }
                
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $permissionIds
            ]);
        } catch (\Exception $e) {
            trace('Get role permissions error: ' . $e->getMessage(), 'error');
            return json([
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 分配权限
     */
    public function assignPermissions($id)
    {
        $params = Request::post();
        $permissionIds = isset($params['permission_ids']) ? (array)$params['permission_ids'] : [];
        return json($this->roleService->assignPermissions($id, $permissionIds));
    }
}