<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Request;
use think\facade\Db;
use think\exception\ValidateException;
use think\validate\ValidateRule;
use think\facade\Log;
use app\model\BusinessStation;

class BusinessStationController extends BaseController
{
    /**
     * 获取工商业电站列表
     */
    public function index()
    {
        try {
            $params = Request::get();
            $limit = isset($params['limit']) ? intval($params['limit']) : 10;
            $page = isset($params['page']) ? intval($params['page']) : 1;
            $keyword = $params['keyword'] ?? '';
            $department = $params['department'] ?? '';

            // --- 构建基础查询条件 ---
            $baseQuery = Db::name('business_station'); // 使用 Db::name

            // 添加软删除过滤条件
            $baseQuery->where('is_deleted', 0);

            // 关键词搜索
            if (!empty($keyword)) {
                $baseQuery->where(function ($q) use ($keyword) {
                    $q->where('company_name', 'like', "%{$keyword}%")
                      ->whereOr('address', 'like', "%{$keyword}%")
                      ->whereOr('account_number', 'like', "%{$keyword}%");
                });
            }

            // 部门搜索
            if (!empty($department)) {
                $baseQuery->where('department', 'like', "%{$department}%");
            }

            // --- 计算总数 ---
            // 克隆基础查询对象，避免影响后续分页查询
            $countQuery = clone $baseQuery;
            $total = $countQuery->count();

            // --- 获取列表数据 ---
            // 使用基础查询对象进行分页和排序
            $list = $baseQuery->page($page, $limit)
                              ->order('id', 'desc') // 或根据需要排序
                              ->select();

            // --- 手动处理 capacity 到 capacity_kw 的转换 ---
            // 因为 Db::name 不会自动调用模型的获取器
            $list->each(function ($item) {
                if (isset($item['capacity'])) {
                    // capacity 存储的是 MW，转换为 kW
                    $item['capacity_kw'] = round(floatval($item['capacity']) * 1000, 2);
                } else {
                    $item['capacity_kw'] = null;
                }
                // 可以选择性移除原始 capacity 字段，如果前端不需要
                // unset($item['capacity']);
                return $item;
            });

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'items' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
             // 记录更详细的错误日志
            \think\facade\Log::error('BusinessStation Index Error: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'message' => '服务器内部错误，请联系管理员']); // 隐藏具体错误信息
        }
    }

    /**
     * 创建工商业电站
     */
    public function create()
    {
        $data = Request::post();
        
        // 数据验证 (可以提取到 Validate 文件中)
        $rules = [
            'department'        => 'require|max:50',
            'company_name'      => 'require|max:100',
            'address'           => 'require|max:255',
            'cooperation_mode'  => 'require|max:100',
            'contact_method'    => 'require|max:100',
            'account_number'    => 'require|max:50',
            'capacity_kw'       => 'require',
            'panel_brand_power' => 'require|max:100',
            'grid_connection_time' => 'require',
        ];
        $messages = [
            'department.require' => '部门不能为空',
            'company_name.require' => '单位名称不能为空',
            'address.require' => '地址不能为空',
            'cooperation_mode.require' => '合作模式不能为空',
            'contact_method.require' => '对接方式不能为空',
            'account_number.require' => '户号不能为空',
            'capacity_kw.require' => '装机容量不能为空',
            'panel_brand_power.require' => '组件品牌功率不能为空',
            'grid_connection_time.require' => '并网时间不能为空',
            // ... 其他验证消息
        ];

        try {
            validate($rules, $messages)->check($data);

            // 模型修改器会自动处理 capacity_kw 到 capacity 的转换
            $station = BusinessStation::create($data);

            $logDetails = json_encode([
                '操作人' => $this->user->realname ?? '未知用户',
                '电站名称' => $data['company_name'] ?? 'N/A',
                '部门' => $data['department'] ?? 'N/A'
            ], JSON_UNESCAPED_UNICODE);
            Log::info("工商业电站 - 新增电站: {$logDetails}");

            return json(['code' => 200, 'message' => '创建成功', 'data' => $station]);
        } catch (ValidateException $e) {
            return json(['code' => 400, 'message' => $e->getError()]);
        } catch (\Exception $e) {
            trace('BusinessStation create error: ' . $e->getMessage(), 'error');
            return json(['code' => 500, 'message' => '创建失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 更新工商业电站
     */
    public function update($id)
    {
        $data = Request::put();
        $station = BusinessStation::find(intval($id));

        if (!$station) {
            return json(['code' => 404, 'message' => '记录未找到']);
        }
        
        // 数据验证 (同 create)
        $rules = [
            'department'        => 'require|max:50',
            'company_name'      => 'require|max:100',
            'address'           => 'require|max:255',
            'cooperation_mode'  => 'require|max:100',
            'contact_method'    => 'require|max:100',
            'account_number'    => 'require|max:50',
            'capacity_kw'       => 'require',
            'panel_brand_power' => 'require|max:100',
            'grid_connection_time' => 'require',
        ];
        $messages = [
            'department.require' => '部门不能为空',
            'company_name.require' => '单位名称不能为空',
            'address.require' => '地址不能为空',
            'cooperation_mode.require' => '合作模式不能为空',
            'contact_method.require' => '对接方式不能为空',
            'account_number.require' => '户号不能为空',
            'capacity_kw.require' => '装机容量不能为空',
            'panel_brand_power.require' => '组件品牌功率不能为空',
            'grid_connection_time.require' => '并网时间不能为空',
            // ... 验证消息同 create ...
        ];

        try {
            validate($rules, $messages)->check($data);
            
            // 模型修改器会自动处理 capacity_kw 到 capacity 的转换
            $station->save($data); // 使用 save 方法触发修改器

            $logDetails = json_encode([
                '操作人' => $this->user->realname ?? '未知用户',
                '电站ID' => $id,
                '更新后名称' => $data['company_name'] ?? 'N/A'
            ], JSON_UNESCAPED_UNICODE);
            Log::info("工商业电站 - 更新电站: {$logDetails}");

            return json(['code' => 200, 'message' => '更新成功', 'data' => $station]);
        } catch (ValidateException $e) {
            return json(['code' => 400, 'message' => $e->getError()]);
        } catch (\Exception $e) {
            trace('BusinessStation update error: ' . $e->getMessage(), 'error');
            return json(['code' => 500, 'message' => '更新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除工商业电站（软删除）
     */
    public function delete($id)
    {
        try {
            // 先查找再删除，以便记录日志
            $station = BusinessStation::find(intval($id));
            if (!$station) {
                return json(['code' => 404, 'message' => '记录未找到']);
            }
            $stationData = $station->toArray(); // 获取将要删除的数据

            // 软删除：设置 is_deleted = 1
            $result = $station->save(['is_deleted' => 1]);

            if ($result) {
                $logDetails = json_encode([
                    '操作人' => $this->user->realname ?? '未知用户',
                    '被删电站ID' => $id,
                    '被删电站数据' => $stationData // 包含被删数据
                ], JSON_UNESCAPED_UNICODE);
                 Log::info("工商业电站 - 软删除电站: {$logDetails}");
                return json(['code' => 200, 'message' => '删除成功']);
            } else {
                Log::warning("工商业电站 - 软删除失败（数据库操作未成功）: ID={$id}");
                return json(['code' => 500, 'message' => '删除失败']); // 改为500更合适
            }
        } catch (\Exception $e) {
            Log::error('BusinessStation soft delete error: ' . $e->getMessage()); // 使用框架日志
            return json(['code' => 500, 'message' => '删除失败: ' . $e->getMessage()]);
        }
    }
} 