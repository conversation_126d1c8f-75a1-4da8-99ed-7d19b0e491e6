<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class AlterTicketTable extends Command
{
    protected function configure()
    {
        $this->setName('alter:ticket_table')
            ->setDescription('为工单表添加handler_ids字段');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始修改工单表结构...');
        
        try {
            // 检查字段是否已存在
            $hasColumn = false;
            $columns = Db::query('SHOW COLUMNS FROM sp_ticket');
            foreach ($columns as $column) {
                if ($column['Field'] === 'handler_ids') {
                    $hasColumn = true;
                    break;
                }
            }
            
            if ($hasColumn) {
                $output->writeln('字段handler_ids已存在，无需添加');
            } else {
                // 添加handler_ids字段
                Db::execute('ALTER TABLE sp_ticket ADD COLUMN handler_ids TEXT COMMENT "处理人ID数组，JSON格式" AFTER handler_name');
                $output->writeln('成功添加handler_ids字段');
            }
            
            $output->writeln('工单表结构修改完成');
            return 0;
        } catch (\Exception $e) {
            $output->writeln('<error>修改表结构失败: ' . $e->getMessage() . '</error>');
            return 1;
        }
    }
} 