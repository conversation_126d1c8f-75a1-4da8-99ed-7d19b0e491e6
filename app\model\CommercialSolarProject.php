<?php
namespace app\model;

use think\Model;

class CommercialSolarProject extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'commercial_solar_projects';

    // 设置字段自动完成
    protected $schema = [
        'id'               => 'int',
        'project_name'     => 'string',
        'installed_capacity' => 'float',
        'investment_amount' => 'float',
        'location'         => 'string',
        'start_date'       => 'date',
        'end_date'         => 'date',
        'status'           => 'int',
        'created_at'       => 'timestamp',
        'updated_at'       => 'timestamp'
    ];

    // 状态常量
    const STATUS_RUNNING = 1; // 运行中
    const STATUS_ENDED = 2;   // 已结束
}