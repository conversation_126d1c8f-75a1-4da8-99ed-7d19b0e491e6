<?php
declare (strict_types = 1);

namespace app\listener;

use app\service\LogService;
use think\facade\Db;
use think\facade\Log;

class SqlListener
{
    public function handle()
    {
        // 定义要排除的表名
        $excludedTables = ['sp_login_log', 'sp_operation_log'];
        
        Db::listen(function($sql, $time, $master) use ($excludedTables) {
            // 检查 SQL 是否包含要排除的表
            foreach ($excludedTables as $table) {
                if (stripos($sql, $table) !== false) {
                    // 如果包含排除的表，直接返回，不记录日志
                    return;
                }
            }
            // 也在回调中添加直接的文件日志
            $sqldir = runtime_path() .'/sql/'. date('Ym').'/';
            if (!is_dir($sqldir)) {
                mkdir($sqldir, 0777, true);
            }
            $sqlfile = $sqldir .  date('d') .'sql.log';
            try {
                
                file_put_contents(
                    $sqlfile,
                    date('Y-m-d H:i:s') . " SQL: {$sql}, 时间: {$time}\n",
                    FILE_APPEND
                );
            } catch (\Exception $e) {
                Log::error('SQL日志写入失败: ' . $e->getMessage());
            }
            
            
        });
    }
} 