class ReconciliationTask extends Command
{
    protected function configure()
    {
        $this->setName('reconciliation')->setDescription('每日对账任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $lastMonth = date('Y-m', strtotime('-1 month'));
        $controller = new \app\controller\finance\Reconciliation;
        $controller->generateReport($lastMonth);
        $output->writeln("对账任务执行完成：{$lastMonth}");
    }
} 