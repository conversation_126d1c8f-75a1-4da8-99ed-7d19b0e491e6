<?php
namespace app\controller\finance;

use app\BaseController;
use think\facade\Db;
use app\model\Station;
use think\facade\Validate;
use think\exception\ValidateException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Grid extends BaseController
{
    /**
     * 获取国网打款列表
     */
    public function getList()
    {
        $page = input('page', 1, 'intval');
        $pageSize = input('pageSize', 10, 'intval');
        $gongwangAccount = input('gongwang_account', '', 'trim');
        $totalSerial = input('total_serial', '', 'trim');
        $startDate = input('start_date', '', 'trim');
        $endDate = input('end_date', '', 'trim');
        $voucherNumber = input('voucher_number', '', 'trim');

        $where = [];
        
        // 构建查询条件
        if (!empty($gongwangAccount)) {
            $where[] = ['d.gongwang_account', 'like', "%{$gongwangAccount}%"];
        }
        
        if (!empty($totalSerial)) {
            $where[] = ['d.total_serial', 'like', "%{$totalSerial}%"];
        }
        
        if (!empty($startDate) && !empty($endDate)) {
            $where[] = ['a.payment_date', 'between', [$startDate, $endDate]];
        } elseif (!empty($startDate)) {
            $where[] = ['a.payment_date', '>=', $startDate];
        } elseif (!empty($endDate)) {
            $where[] = ['a.payment_date', '<=', $endDate];
        }
        
        if (!empty($voucherNumber)) {
            $where[] = ['a.voucher_number', 'like', "%{$voucherNumber}%"];
        }
        
        try {
            // 获取数据总数
            $total = Db::table('sp_fund_state_grid')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->count();
            
            // 查询数据
            $list = Db::table('sp_fund_state_grid')
                ->alias('a')
                ->join('sp_station d', 'a.station_id = d.id')
                ->where($where)
                ->field('a.*, d.gongwang_account, d.total_serial, d.contact_name')
                ->order('a.payment_date DESC, a.id DESC')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return $this->success([
                'items' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取国网打款列表失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加国网打款记录
     */
    public function save()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'payment_date' => 'require|date',
            'voucher_number' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 获取电站信息
            $station = Station::where('id', $params['station_id'])->find();
            if (empty($station)) {
                return $this->error('电站不存在');
            }
            
            // 准备数据
            $data = [
                'station_id' => $params['station_id'],
                'payment_date' => $params['payment_date'],
                'voucher_number' => $params['voucher_number'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            // 添加记录
            $id = Db::table('sp_fund_state_grid')->insertGetId($data);
            
            // 检查并更新资金对账
            $this->checkAndUpdateReconciliation($params['station_id'], date('Y-m', strtotime($params['payment_date'])));
            
            return $this->success('添加成功', ['id' => $id]);
        } catch (\Exception $e) {
            return $this->error('添加失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新国网打款记录
     */
    public function update($id)
    {
        $params = $this->request->put();
        
        // 参数验证
        $validate = Validate::rule([
            'station_id' => 'require|number',
            'payment_date' => 'require|date',
            'voucher_number' => 'require',
            'amount' => 'require|float'
        ]);
        
        if (!$validate->check($params)) {
            return $this->error($validate->getError());
        }
        
        try {
            // 检查记录是否存在
            $record = Db::table('sp_fund_state_grid')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 准备数据
            $data = [
                'payment_date' => $params['payment_date'],
                'voucher_number' => $params['voucher_number'],
                'amount' => $params['amount'],
                'remark' => isset($params['remark']) ? $params['remark'] : '',
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 更新记录
            Db::table('sp_fund_state_grid')->where('id', $id)->update($data);
            
            // 检查原有记录的月份
            $oldMonth = date('Y-m', strtotime($record['payment_date']));
            $newMonth = date('Y-m', strtotime($params['payment_date']));
            
            // 更新资金对账
            $this->checkAndUpdateReconciliation($record['station_id'], $oldMonth);
            
            // 如果月份发生变化，还需要更新新月份的对账
            if ($oldMonth !== $newMonth) {
                $this->checkAndUpdateReconciliation($params['station_id'], $newMonth);
            }
            
            return $this->success('更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除国网打款记录
     */
    public function delete($id)
    {
        try {
            // 检查记录是否存在
            $record = Db::table('sp_fund_state_grid')->where('id', $id)->find();
            if (empty($record)) {
                return $this->error('记录不存在');
            }
            
            // 删除记录
            Db::table('sp_fund_state_grid')->where('id', $id)->delete();
            
            // 更新资金对账
            $month = date('Y-m', strtotime($record['payment_date']));
            $this->checkAndUpdateReconciliation($record['station_id'], $month);
            
            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 导入国网打款数据
     */
    public function import()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请上传文件');
        }
        
        // 验证文件类型
        $fileExt = $file->getOriginalExtension();
        if (!in_array($fileExt, ['xlsx', 'xls'])) {
            return $this->error('仅支持 Excel 文件导入');
        }
        
        try {
            // 获取文件内容
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // 获取标题行和字段映射
            $headers = array_shift($rows);
            $fieldMap = $this->getFieldMapping($headers);
            
            // 导入结果统计
            $success = 0;
            $fail = 0;
            $errors = [];
            
            Db::startTrans();
            
            foreach ($rows as $index => $row) {
                // 根据字段映射获取数据
                $projectCode = trim($this->getValueByField($row, $fieldMap, '项目编号', ''));
                $projectName = trim($this->getValueByField($row, $fieldMap, '项目名称', ''));
                $paymentDate = trim($this->getValueByField($row, $fieldMap, '结算日期', ''));
                
                // 必须字段验证
                if (empty($projectCode) || empty($projectName) || empty($paymentDate)) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行数据不完整，项目编号、项目名称和结算日期为必填字段";
                    continue;
                }
                
                // 查找电站ID
                $station = Station::where('gongwang_account', $projectCode)
                    ->find();
                
                if (!$station) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行: 找不到匹配的电站信息，项目编号: {$projectCode}";
                    continue;
                }
                
                try {
                    // 处理日期格式
                    if (!strtotime($paymentDate)) {
                        $fail++;
                        $errors[] = "第" . ($index + 2) . "行: 日期格式错误 '{$paymentDate}'";
                        continue;
                    }
                    
                    // 标准化日期格式
                    $formattedDate = date('Y-m-d', strtotime($paymentDate));
                    
                    // 准备基础数据
                    $data = [
                        'station_id' => $station['id'],
                        'gongwang_account' => $projectCode,
                        'projectName' => $projectName,
                        'payment_date' => $formattedDate,
                        'create_time' => date('Y-m-d H:i:s')
                    ];
                    
                    // 添加其他字段
                    $data['unit'] = $this->getValueByField($row, $fieldMap, '单位', '');
                    $data['powerSupplyOffice'] = $this->getValueByField($row, $fieldMap, '所属供电所', '');
                    $data['feeMonth'] = $this->getValueByField($row, $fieldMap, '电费年月', '');
                    $data['customerType'] = $this->getValueByField($row, $fieldMap, '客户类别', '');
                    $data['isPovertyProject'] = $this->getValueByField($row, $fieldMap, '是否扶贫项目', '') == '是' ? 1 : 0;
                    $data['povertyProjectType'] = $this->getValueByField($row, $fieldMap, '扶贫项目类型', '');
                    $data['isInSubsidyList'] = $this->getValueByField($row, $fieldMap, '是否已纳入补贴目录', '') == '是' ? 1 : 0;
                    $data['energyType'] = $this->getValueByField($row, $fieldMap, '电能类型', '');
                    $data['gridConnectionType'] = $this->getValueByField($row, $fieldMap, '上网类型', '');
                    $data['isSigned'] = $this->getValueByField($row, $fieldMap, '是否签约', '') == '是' ? 1 : 0;
                    $data['installedCapacity'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '装机容量', 0));
                    $data['gridPrice'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '上网电价', 0));
                    $data['subsidyStandard'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '补助标准', 0));
                    $data['generationAmount'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '发电量', 0));
                    $data['gridConnectionAmount'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '上网电量', 0));
                    $data['totalPayableAmount'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '总应付金额', 0));
                    $data['totalPayableCost'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '总应付成本', 0));
                    $data['totalPayableTax'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '总应付税额', 0));
                    $data['payablePurchaseFee'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '应付购电费', 0));
                    $data['payablePurchaseCost'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '应付购电成本', 0));
                    $data['payablePurchaseTax'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '应付购电税额', 0));
                    $data['centralSubsidyAmount'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '中央补助金额', 0));
                    $data['centralSubsidyCost'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '中央补助成本', 0));
                    $data['centralSubsidyTax'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '中央补助税额', 0));
                    $data['provincialSubsidyAmount'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '省级补助金额', 0));
                    $data['provincialSubsidyCost'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '省级补助成本', 0));
                    $data['provincialSubsidyTax'] = $this->getNumericValue($this->getValueByField($row, $fieldMap, '省级补助税额', 0));
                    $data['settlementAuditBatch'] = $this->getValueByField($row, $fieldMap, '结算审核批次', '');
                    
                    // 计算或获取总金额
                    $totalAmount = $this->getValueByField($row, $fieldMap, '总应付金额', 0);
                    $data['amount'] = floatval($totalAmount);
                    
                    // 凭证号和备注
                    $data['voucher_no'] = $this->getValueByField($row, $fieldMap, '凭证号', '');
                    $data['remark'] = $this->getValueByField($row, $fieldMap, '备注', '');
                    
                    // 填充总序号（如果存在）
                    $data['total_serial'] = $this->getValueByField($row, $fieldMap, '总序号', $station['total_serial'] ?? '');
                    
                    // 插入记录
                    Db::table('sp_fund_state_grid')->insert($data);
                    
                    // 更新资金对账
                    $month = date('Y-m', strtotime($formattedDate));
                    $this->checkAndUpdateReconciliation($station['id'], $month);
                    
                    $success++;
                } catch (\Exception $e) {
                    $fail++;
                    $errors[] = "第" . ($index + 2) . "行: " . $e->getMessage();
                }
            }
            
            Db::commit();
            
            return $this->success([
                'success' => $success,
                'fail' => $fail,
                'errors' => $errors
            ], '导入完成');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据字段名从映射中获取值
     */
    private function getValueByField($row, $fieldMap, $fieldName, $default = '')
    {
        if (isset($fieldMap[$fieldName]) && isset($row[$fieldMap[$fieldName]])) {
            return $row[$fieldMap[$fieldName]];
        }
        return $default;
    }
    
    /**
     * 获取并转换为数值
     */
    private function getNumericValue($value)
    {
        if (is_numeric($value)) {
            return floatval($value);
        }
        return 0;
    }
    
    /**
     * 获取字段映射关系
     */
    private function getFieldMapping($headers)
    {
        $fieldMap = [];
        foreach ($headers as $index => $header) {
            $fieldMap[trim($header)] = $index;
        }
        return $fieldMap;
    }
    
    /**
     * 导出国网打款数据
     */
    public function export()
    {
        try {
            // 获取筛选条件
            $gongwangAccount = input('gongwang_account', '', 'trim');
            $totalSerial = input('total_serial', '', 'trim');
            $startDate = input('start_date', '', 'trim');
            $endDate = input('end_date', '', 'trim');
            $voucherNo = input('voucher_no', '', 'trim');
            
            $where = [];
            
            // 构建查询条件
            if (!empty($gongwangAccount)) {
                $where[] = ['g.gongwang_account', 'like', "%{$gongwangAccount}%"];
            }
            
            if (!empty($totalSerial)) {
                $where[] = ['g.total_serial', 'like', "%{$totalSerial}%"];
            }
            
            if (!empty($startDate) && !empty($endDate)) {
                $where[] = ['g.payment_date', 'between', [$startDate, $endDate]];
            } elseif (!empty($startDate)) {
                $where[] = ['g.payment_date', '>=', $startDate];
            } elseif (!empty($endDate)) {
                $where[] = ['g.payment_date', '<=', $endDate];
            }
            
            if (!empty($voucherNo)) {
                $where[] = ['g.voucher_no', 'like', "%{$voucherNo}%"];
            }
            
            // 查询数据
            $data = Db::table('sp_fund_state_grid')
                ->alias('g')
                ->join('sp_station s', 'g.station_id = s.id')
                ->where($where)
                ->field('g.*, s.station_name, s.contact_name')
                ->order('g.payment_date DESC, g.id DESC')
                ->select()
                ->toArray();
            
            // 创建电子表格对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $headers = [
                '单位', '结算审核批次', '所属供电所', '项目编号', '项目名称', 
                '电费年月', '结算日期', '客户类别', '是否扶贫项目', '扶贫项目类型', 
                '是否已纳入补贴目录', '电能类型', '上网类型', '是否签约', '装机容量', 
                '发电量', '上网电量', '总应付金额', '总应付成本', '总应付税额', 
                '应付购电费', '应付购电成本', '应付购电税额', '中央补助金额', '中央补助成本', 
                '中央补助税额', '省级补助金额', '省级补助成本', '省级补助税额', '上网电价', 
                '补助标准', '总序号', '凭证号', '打款金额', '备注'
            ];
            
            // 字段映射（数据库字段名 => 表头）
            $fieldMap = [
                'unit' => '单位',
                'settlementAuditBatch' => '结算审核批次',
                'powerSupplyOffice' => '所属供电所',
                'gongwang_account' => '项目编号',
                'projectName' => '项目名称',
                'feeMonth' => '电费年月',
                'payment_date' => '结算日期',
                'customerType' => '客户类别',
                'isPovertyProject' => '是否扶贫项目',
                'povertyProjectType' => '扶贫项目类型',
                'isInSubsidyList' => '是否已纳入补贴目录',
                'energyType' => '电能类型',
                'gridConnectionType' => '上网类型',
                'isSigned' => '是否签约',
                'installedCapacity' => '装机容量',
                'generationAmount' => '发电量',
                'gridConnectionAmount' => '上网电量',
                'totalPayableAmount' => '总应付金额',
                'totalPayableCost' => '总应付成本',
                'totalPayableTax' => '总应付税额',
                'payablePurchaseFee' => '应付购电费',
                'payablePurchaseCost' => '应付购电成本',
                'payablePurchaseTax' => '应付购电税额',
                'centralSubsidyAmount' => '中央补助金额',
                'centralSubsidyCost' => '中央补助成本',
                'centralSubsidyTax' => '中央补助税额',
                'provincialSubsidyAmount' => '省级补助金额',
                'provincialSubsidyCost' => '省级补助成本',
                'provincialSubsidyTax' => '省级补助税额',
                'gridPrice' => '上网电价',
                'subsidyStandard' => '补助标准',
                'total_serial' => '总序号',
                'voucher_no' => '凭证号',
                'amount' => '打款金额',
                'remark' => '备注'
            ];
            
            // 写入表头
            $col = 'A';
            foreach ($headers as $header) {
                $sheet->setCellValue($col . '1', $header);
                $col++;
            }
            
            // 写入数据
            if (!empty($data)) {
                $row = 2;
                foreach ($data as $item) {
                    $col = 'A';
                    
                    // 循环写入各字段数据
                    foreach ($fieldMap as $field => $header) {
                        $value = '';
                        
                        // 获取字段值
                        if ($field == 'isPovertyProject' || $field == 'isInSubsidyList' || $field == 'isSigned') {
                            // 将布尔值转为"是"或"否"
                            $value = !empty($item[$field]) ? '是' : '否';
                        } else if ($field == 'payment_date') {
                            // 格式化日期
                            $value = !empty($item[$field]) ? date('Y-m-d', strtotime($item[$field])) : '';
                        } else if (in_array($field, ['projectName']) && empty($item[$field])) {
                            // 如果项目名称为空，使用station_name或contact_name
                            $value = $item['station_name'] ?: $item['contact_name'];
                        } else {
                            $value = $item[$field] ?? '';
                        }
                        
                        $sheet->setCellValue($col . $row, $value);
                        $col++;
                    }
                    
                    $row++;
                }
            }
            
            // 设置列宽
            foreach (range('A', 'Z') as $col) {
                $sheet->getColumnDimension($col)->setWidth(15);
            }
            foreach (range('A', 'J') as $letter) {
                $col = 'A' . $letter;
                $sheet->getColumnDimension($col)->setWidth(15);
            }
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="国网打款数据_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导出模板
     */
    public function template()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头（与yn.md字段对应）
            $headers = [
                '单位', '结算审核批次', '所属供电所', '项目编号', '项目名称', 
                '电费年月', '结算日期', '客户类别', '是否扶贫项目', '扶贫项目类型', 
                '是否已纳入补贴目录', '电能类型', '上网类型', '是否签约', '装机容量', 
                '发电量', '上网电量', '总应付金额', '总应付成本', '总应付税额', 
                '应付购电费', '应付购电成本', '应付购电税额', '中央补助金额', '中央补助成本', 
                '中央补助税额', '省级补助金额', '省级补助成本', '省级补助税额', '上网电价', 
                '补助标准', '总序号', '凭证号', '备注'
            ];
            
            // 写入表头
            $col = 'A';
            foreach ($headers as $header) {
                $sheet->setCellValue($col . '1', $header);
                $col++;
            }
            
            // 添加示例数据行
            $sheet->setCellValue('A2', '示例单位');
            $sheet->setCellValue('B2', '批次001');
            $sheet->setCellValue('C2', '示例供电所');
            $sheet->setCellValue('D2', 'GW001'); // 项目编号
            $sheet->setCellValue('E2', '示例项目名称');
            $sheet->setCellValue('F2', '2023-08');
            $sheet->setCellValue('G2', '2023-08-15');
            $sheet->setCellValue('H2', '居民');
            $sheet->setCellValue('I2', '是');
            $sheet->setCellValue('J2', '光伏扶贫');
            $sheet->setCellValue('K2', '是');
            $sheet->setCellValue('L2', '光伏');
            $sheet->setCellValue('M2', '全额上网');
            $sheet->setCellValue('N2', '是');
            $sheet->setCellValue('O2', '10.5');
            $sheet->setCellValue('P2', '1000');
            $sheet->setCellValue('Q2', '950');
            $sheet->setCellValue('R2', '800');
            $sheet->setCellValue('S2', '750');
            $sheet->setCellValue('T2', '50');
            $sheet->setCellValue('U2', '400');
            $sheet->setCellValue('V2', '380');
            $sheet->setCellValue('W2', '20');
            $sheet->setCellValue('X2', '250');
            $sheet->setCellValue('Y2', '235');
            $sheet->setCellValue('Z2', '15');
            $sheet->setCellValue('AA2', '150');
            $sheet->setCellValue('AB2', '135');
            $sheet->setCellValue('AC2', '15');
            $sheet->setCellValue('AD2', '0.85');
            $sheet->setCellValue('AE2', '0.42');
            $sheet->setCellValue('AF2', 'LS0001');
            $sheet->setCellValue('AG2', 'FP20230815001');
            $sheet->setCellValue('AH2', '国网电费支付');
            
            // 设置列宽
            foreach (range('A', 'Z') as $col) {
                $sheet->getColumnDimension($col)->setWidth(15);
            }
            foreach (range('A', 'H') as $letter) {
                $col = 'A' . $letter;
                $sheet->getColumnDimension($col)->setWidth(15);
            }
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="国网打款导入模板.xlsx"');
            header('Cache-Control: max-age=0');
            
            // 创建Excel写对象并输出
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return $this->error('获取模板失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 检查并更新资金对账
     * 
     * @param int $stationId 电站ID
     * @param string $month 月份（格式：yyyy-mm）
     */
    private function checkAndUpdateReconciliation($stationId, $month)
    {
        try {
            // 获取该月的国网打款总额
            $gridAmount = Db::table('sp_fund_state_grid')
                ->where('station_id', $stationId)
                ->whereRaw("DATE_FORMAT(payment_date, '%Y-%m') = ?", [$month])
                ->sum('amount');
            
            // 获取该月的资金归集总额
            $collectionAmount = Db::table('sp_fund_collection_detail')
                ->where('station_id', $stationId)
                ->whereRaw("CONCAT(collection_year, '-', LPAD(collection_month, 2, '0')) = ?", [$month])
                ->sum('amount');
            
            // 计算差额
            $difference = $collectionAmount - $gridAmount;
            
            // 状态判断
            $status = '正常';
            if (abs($difference) > 0.01) { // 考虑浮点数比较，使用一个很小的阈值
                $status = $difference > 0 ? '多收' : '少收';
            }
            
            // 检查是否存在对账记录
            $reconciliation = Db::table('sp_fund_reconciliation')
                ->where('station_id', $stationId)
                ->where('reconciliation_month', $month)
                ->find();
            
            // 准备数据
            $data = [
                'collected_amount' => $collectionAmount,
                'grid_amount' => $gridAmount,
                'difference' => $difference,
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            if ($reconciliation) {
                // 更新对账记录
                Db::table('sp_fund_reconciliation')
                    ->where('id', $reconciliation['id'])
                    ->update($data);
            } else {
                // 创建新对账记录
                $data['station_id'] = $stationId;
                $data['reconciliation_month'] = $month;
                $data['create_time'] = date('Y-m-d H:i:s');
                
                Db::table('sp_fund_reconciliation')->insert($data);
            }
            
            return true;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('更新资金对账失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
} 