<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class LoginLog extends Model
{
    protected $name = 'login_log';
    protected $prefix = 'sp_';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = false;

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'user_id'     => 'int',
        'username'    => 'string',
        'ip'          => 'string',
        'user_agent'  => 'string',
        'status'      => 'int',
        'message'     => 'string',
        'create_at'  => 'datetime'
    ];
} 