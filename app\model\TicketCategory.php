<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class TicketCategory extends Model
{
    protected $name = 'ticket_category';
    
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_at';
    protected $updateTime = 'update_at';
    
    // 与工单的关联
    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'category_id');
    }
} 